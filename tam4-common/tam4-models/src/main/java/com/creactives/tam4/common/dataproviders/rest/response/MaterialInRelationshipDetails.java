package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.relationships.RelationshipRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialInRelationshipDetails {
  private UUID materialId;
  private RelationshipRole role;
  private String shortDescription;
  private String materialCode;
  private String client;
}
