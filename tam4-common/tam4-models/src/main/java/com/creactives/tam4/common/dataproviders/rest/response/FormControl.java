package com.creactives.tam4.common.dataproviders.rest.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Log4j2
public class FormControl extends BasicFormControl {

  private boolean mandatory; //whether the field is mandatory when change is active (it cannot be emptied).
  private boolean coreAttribute; //add a star icon next to the field name
  private boolean technical; //specifies whether the field is associated to a technical attribute.
  private boolean editable; //TODO check Legacy how these are populated

  private String defaultValue; //value present by default in the field when activating the change checkbox

  private boolean localized;
  private boolean customer; //allow the FE to determine when a FormControl configuration is related to a customer field
  private boolean hidden;

  private Set<String> unitsOfMeasure; //list of units of measure present in the dropdown for technical attributes.
  /**
   * Flag per indicare se l'attributo è ignorato in quanto é presente un errore di configurazione
   */
  private boolean customerFieldInvalidForClientConfig;

  private String language; //show a flag relative to the language (it will show the italian flag next to the field name)
  private String source;

  private List<String> tableFields;

  private List<LocalizedFieldValue> dropdownValues;

  private String targetTaxonomyName; //nullable, present for type popup-category-chooser

  private List<String> descriptionLanguages; //identify the list to be shown for description attributes
  private Map<String, Boolean> editableDescriptionsLanguage; //identify the map language/boolean with the editable condition for description attributes
  private Map<String, Boolean> mandatoryDescriptionsLanguage; //identify the map language/boolean with the mandatory condition for description attributes
  private List<BasicFormControl> children;

  public void addLanguage(final String descriptionLanguage) {
    if (descriptionLanguages == null) {
      descriptionLanguages = new ArrayList<>();
    }
    descriptionLanguages.add(descriptionLanguage);
  }

  public void addEditableLanguage(final String language, final boolean editable) {
    if (editableDescriptionsLanguage == null) {
      editableDescriptionsLanguage = new HashMap<>();
    }
    editableDescriptionsLanguage.put(language, editable);
  }

  public void addMandatoryLanguage(final String language, final boolean editable) {
    if (mandatoryDescriptionsLanguage == null) {
      mandatoryDescriptionsLanguage = new HashMap<>();
    }
    mandatoryDescriptionsLanguage.put(language, editable);
  }
}
