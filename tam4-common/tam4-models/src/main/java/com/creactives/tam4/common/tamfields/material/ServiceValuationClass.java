package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

@Service
public class ServiceValuationClass extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    final CalculateChangesUtils<String> serviceValuationClassChange = new CalculateChangesUtils<>(originalMaterialDetails.getServiceValuationClass(),
        editedMaterialDetails.getServiceValuationClass(),
        false);
    changes.setServiceValuationClass(serviceValuationClassChange.applyChange());
  }

  @Override
  public String fieldName() {
    return AttributeCodes.SERVICE_VALUATION_CLASS;
  }
}
