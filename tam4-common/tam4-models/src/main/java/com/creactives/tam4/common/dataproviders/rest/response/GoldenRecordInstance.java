package com.creactives.tam4.common.dataproviders.rest.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoldenRecordInstance {

  private String clientId;
  private boolean enabled;
  private boolean selected;
  private List<SmartFormControl> plant;
  private boolean autoExtensionPlant;
  private boolean principal;

}
