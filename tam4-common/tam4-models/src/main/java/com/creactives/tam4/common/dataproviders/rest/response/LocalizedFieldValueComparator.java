package com.creactives.tam4.common.dataproviders.rest.response;

import java.util.Comparator;

/**
 * Created on 15/12/2021 17:19
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
public class LocalizedFieldValueComparator implements Comparator<LocalizedFieldValue> {
  @Override
  public int compare(final LocalizedFieldValue o1, final LocalizedFieldValue o2) {
    return o1.getKey().compareTo(o2.getKey());

  }
}
