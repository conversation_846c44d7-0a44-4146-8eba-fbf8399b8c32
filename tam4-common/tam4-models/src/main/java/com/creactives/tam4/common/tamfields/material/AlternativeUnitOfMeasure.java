package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AlternativeUnitOfMeasure extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    final List<MaterialAlternativeUnitOfMeasureDetails> originalAlternativeUnitsOfMeasure = Optional
        .ofNullable(originalMaterialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure())
        .orElse(Collections.emptyList());
    final List<MaterialAlternativeUnitOfMeasureDetails> alternativeUnitsOfMeasure = Optional
        .ofNullable(editedMaterialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure())
        .orElse(Collections.emptyList());
    final Map<String, MaterialAlternativeUnitOfMeasureDetails> altUOMOldMap = originalAlternativeUnitsOfMeasure
        .stream()
        .collect(Collectors.toMap(MaterialAlternativeUnitOfMeasureDetails::getAlternativeUnitOfMeasurement,
            it -> it));
    final Map<String, MaterialAlternativeUnitOfMeasureDetails> altUOMNewMap = alternativeUnitsOfMeasure
        .stream()
        .collect(Collectors.toMap(MaterialAlternativeUnitOfMeasureDetails::getAlternativeUnitOfMeasurement,
            it -> it));
    final Map<String, Change<MaterialAlternativeUnitOfMeasureDetails>> aUomChangesMap = CalculateChangesUtils.calculateMapChange(altUOMNewMap,
        altUOMOldMap);
    if (MapUtils.isNotEmpty(aUomChangesMap)) {
      changes.setAlternativeUnitsOfMeasure(new ArrayList<>(aUomChangesMap.values()));
    }
  }

  @Override
  public String fieldName() {
    return AttributeCodes.ALTERNATIVE_UNIT_OF_MEASURE;
  }
}
