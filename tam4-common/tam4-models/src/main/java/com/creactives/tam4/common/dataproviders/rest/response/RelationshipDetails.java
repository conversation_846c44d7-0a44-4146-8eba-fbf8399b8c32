package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.relationships.RelationshipType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationshipDetails {
  private UUID relationshipId;
  private RelationshipType relationshipType;
  private List<MaterialInRelationshipDetails> materialsInRelationship;
}
