package com.creactives.tam4.common;

import com.creactives.tam4.common.types.SingleEndpointConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseEndpointConfig implements Serializable {

  private static final long serialVersionUID = -5928983254794703425L;
  private SingleEndpointConfig abInbevIntegration;
  private SingleEndpointConfig audit;
  private SingleEndpointConfig auth;
  private SingleEndpointConfig bulkUpload;
  private SingleEndpointConfig dataLoader;
  private SingleEndpointConfig duplicatesManagement;
  private SingleEndpointConfig eventsStore;
  private SingleEndpointConfig folderNavigation;
  private SingleEndpointConfig legacy;
  private SingleEndpointConfig materials;
  private SingleEndpointConfig notifications;
  private SingleEndpointConfig ontology;
  private SingleEndpointConfig relationships;
  private SingleEndpointConfig warnings;
  private SingleEndpointConfig workflow;
  private SingleEndpointConfig worklists;
  private SingleEndpointConfig dwhIntegration;

}
