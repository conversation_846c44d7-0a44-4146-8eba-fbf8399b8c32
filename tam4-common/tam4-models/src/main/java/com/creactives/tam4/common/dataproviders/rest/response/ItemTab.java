package com.creactives.tam4.common.dataproviders.rest.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemTab {

  protected String tabLabel;
  private String tabKey;
  private boolean hasMandatoryAttributes;
  private boolean hasTechnicalAttributes;
  private boolean hasLocalizedData;

  private List<FormControl> requests = new ArrayList<>();
  private List<RelationshipDetails> relationships = new ArrayList<>();
  private List<InstanceDetails> instances = new ArrayList<>();
}
