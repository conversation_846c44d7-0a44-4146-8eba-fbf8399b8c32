package com.creactives.tam4.common.dataproviders.rest.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.log4j.Log4j2;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Log4j2
public class BasicFormControl implements Serializable {
  private static final long serialVersionUID = -4822341480628632911L;

  private String id; //attributeName of ontology -> with the time we have inverted the original field functionality
  private String attributeName; //id of the field. contains the internal name (materialKey.client) or the attributeName (if we don't have an internal name -> technical attributes')
  private String label; //used to retrieve the label of the field.
  /*
   * type can be one of:
   *   * dropdown
   *   * textfield
   *   * textfieldNumeric
   *   * textarea
   *   * popup-category-chooser
   *   * alternativeUnitsOfMeasure
   *   * materialValuations
   *   * plants
   *   * storageLocations
   *   * localizedTextarea
   *   * warehouse
   * if empty it's a popup not yet handled
   */
  private String type; //type can be one of: dropdown, textfield, textfieldNumeric, textarea, popup-category-chooser if empty it's a popup not yet handled

  private boolean multiple; //TODO check Legacy how these are populated

  private int textareaSizeRows; //nullable, present for textarea fields. set default to
  private int textareaSizeCols; //nullable, present for textarea fields. set default to
  private int length; //max length of field. user mustn't be allowed to insert more characters.
  private int decimals; //present for type textfieldNumeric. controls how many decimals the user can insert.

  private int order = 0; // Order of the field in the form.
}
