package com.creactives.tam4.common.dataproviders.rest.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.creactives.tam4.common.constants.MassiveRelationshipConstants.DASH_DELIMITER_WITH_SPACES;
import static com.creactives.tam4.common.constants.MassiveRelationshipConstants.UNDERSCORE_DELIMITER;

@NoArgsConstructor
@Data
@AllArgsConstructor
public class ClientLocalizedFieldValue {

  private String client;
  private String keyWithoutClient;
  private String textWithoutConcatenations;

  public String getKey() {
    return client + UNDERSCORE_DELIMITER + keyWithoutClient;
  }

  public String getText() {
    return getKey() + DASH_DELIMITER_WITH_SPACES + textWithoutConcatenations;
  }

}
