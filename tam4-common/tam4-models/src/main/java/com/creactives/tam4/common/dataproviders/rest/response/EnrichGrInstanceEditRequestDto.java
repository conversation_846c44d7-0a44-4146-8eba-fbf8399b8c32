package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import lombok.Data;

/**
 * Oggetto per interrogare il micro servizio ontology e recuperare le changes dopo run della pipe enrichGrInstancePipe
 */

@Data
public class EnrichGrInstanceEditRequestDto {
  /**
   * {@link com.creactives.tam4.messaging.materialdetails.MaterialDetails} popolato con i dati a disposizione
   **/
  private MaterialDetails materialDetails;

  /**
   * Changes nel GR da applicare alle istanze
   **/
  private Changes changes;
}
