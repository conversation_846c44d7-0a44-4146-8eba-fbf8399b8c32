package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

@Service
public class BatchManagementRequirementIndicator extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    changes.setBatchManagementRequirementIndicator(new CalculateChangesUtils(originalMaterialDetails
        .getBasicData()
        .getBatchManagementRequirementIndicator(),
        editedMaterialDetails
            .getBasicData()
            .getBatchManagementRequirementIndicator(),
        false).applyChange());
  }

  @Override
  public String fieldName() {
    return AttributeCodes.BATCHMANAGEMENTREQUIREMENTINDICATOR;
  }
}
