package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.CategoryKey;
import com.creactives.tam4.messaging.Completeness;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.rest.MaterialActionsAuthorization;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * Dto contenente le informazioni del materiale, ovvero attributi ({@link SmartFormControl})
 * associati separati in diversi fogli ({@link SmartItemTab})
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class SmartMaterialDetailsResponseDto {

  /**
   * Attributi del master raggruppati per scheda
   **/
  private List<SmartItemTab> materialSheets;

  /**
   * Categorie relative al materiale
   * FIXME sembra che possa contenere l'albero delle categorie, ma in realtà in SC step2, golden record edit, ..
   * ha solo la foglia!
   */
  private Map<String, List<CategoryKey>> categoriesSheet;

  /**
   * Dettagli del materiale dopo la l'applicazione delle pipe
   **/
  private MaterialDetails changedMaterialDetails;

  /**
   * Client relativo al materiale
   */
  private String client;

  /**
   * Informazioni aggiuntive relative al materiale
   */
  private AdditionalMaterialInformation additionalMaterialInformation;

  /**
   * Informazioni delle autorizzazioni/azioni
   */
  private MaterialActionsAuthorization materialActionsAuthorization;

  /**
   * usato solo per loggare il fatto che ci perdiamo dei dati
   */
  private boolean hasMissingFields = false;

  /**
   * id del draft associato al caricamento
   **/
  private Long tempDraftId;


  @Nullable
  public Completeness getCompleteness() {
    if (changedMaterialDetails != null) {
      return changedMaterialDetails.getCompleteness();
    } else {
//      return Completeness.POOR;
//      ritorno null così da rendere evidente la perdita di informazione
      return null;
    }
  }

}
