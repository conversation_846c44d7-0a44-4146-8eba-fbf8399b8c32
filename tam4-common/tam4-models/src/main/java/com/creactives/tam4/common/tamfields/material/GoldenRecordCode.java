package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

@Service
public class GoldenRecordCode extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    //FIXME per il momento gestiamo la modifica solo attraverso l'uuid
    /*changes.setGoldenRecordCode(        new CalculateChangesUtils(            originalMaterialDetails.getGoldenRecordCode(),
            editedMaterialDetails.getGoldenRecordCode(),
            false).applyChange());*/
  }

  @Override
  public String fieldName() {
    return AttributeCodes.GOLDEN_RECORD_CODE;
  }
}
