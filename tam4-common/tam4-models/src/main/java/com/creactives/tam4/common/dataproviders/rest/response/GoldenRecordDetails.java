package com.creactives.tam4.common.dataproviders.rest.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoldenRecordDetails {
  private List<String> clientIds;
  private boolean createGR;
  private boolean enabled;
  private List<GoldenRecordInstance> instances;

  // next field will be populate only if we are in a more info page
  private String grMaterialId;
  private String grMaterialCode;

}
