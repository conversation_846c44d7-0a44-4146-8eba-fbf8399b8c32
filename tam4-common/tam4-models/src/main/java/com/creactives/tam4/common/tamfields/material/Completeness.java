package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

import static com.creactives.tam4.common.models.ComputeChanges.getCompleteness;

@Service
public class Completeness extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    changes.setCompleteness(getCompleteness(editedMaterialDetails, originalMaterialDetails));
  }

  @Override
  public String fieldName() {
    return AttributeCodes.COMPLETENESS;
  }
}
