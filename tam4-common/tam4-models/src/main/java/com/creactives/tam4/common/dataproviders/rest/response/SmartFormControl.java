package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.types.TamApePageType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Estensione dell'oggetto {@link FormControl} che racchiude le informazioni degli attributi.
 * Aggiunge alcune informazioni per tener traccia soprattutto di valori già presenti
 */
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Log4j2
@ToString(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class SmartFormControl extends FormControl implements Serializable, Cloneable {

  /**
   * Valore già presente o valorizzato in precedenza
   */
  private String value;

  /**
   * Unità di misura già presente o valorizzata in precedenza
   */
  private String unitsOfMeasureSelected;

  /**
   * Source da cui recuperare la lista dei valori per il dropdown
   */
  private String dropdownListSource;

  /**
   * Eventuale attributo con un legame con questo (ad esempio nel caso delle descrizioni)
   */
  private String relatedAttribute;

  /**
   * Tipologia di valore che contiene l'attributo
   */
  private String attributeValueType;

  /**
   * Flag per indicare se il campo è di master (materiale o servizio) -> Smart Creation
   * E' mutuamente esclusivo con il goldenRecordAttribute.
   */
  private boolean masterAttribute;
  /**
   * Flag per indicare se il campo è di golden record -> Smart creation
   * E' mutuamente esclusivo con il masterAttribute.
   */
  private boolean goldenRecordAttribute;

  public SmartFormControl(final FormControl formControl, final String page) {
    this.setId(formControl.getId());
    this.setType(formControl.getType());
    this.setCustomer(formControl.isCustomer());
    this.setLabel(formControl.getLabel());
    //vogliamo forzare il client a non essere editabile in step2 obbligandolo a tornare in step1
    this.setEditable(formControl.isEditable() && (!formControl.getId().equals(AttributeCodes.CLIENT) || !TamApePageType.STEP2_PAGE.equals(page)));
    this.setAttributeName(formControl.getAttributeName());
    this.setTargetTaxonomyName(formControl.getTargetTaxonomyName());
    this.setTableFields(formControl.getTableFields());
    this.setDescriptionLanguages(formControl.getDescriptionLanguages());
    this.setLanguage(formControl.getLanguage());
    this.setCoreAttribute(formControl.isCoreAttribute());
    this.setLocalized(formControl.isLocalized());
    this.setMandatory(formControl.isMandatory());
    this.setMultiple(formControl.isMultiple());
    this.setUnitsOfMeasure(formControl.getUnitsOfMeasure());
    this.setDefaultValue(formControl.getDefaultValue());
    this.setTechnical(formControl.isTechnical());
    this.setTextareaSizeCols(formControl.getTextareaSizeCols());
    this.setTextareaSizeRows(formControl.getTextareaSizeRows());
    this.setLength(formControl.getLength());
    this.setDecimals(formControl.getDecimals());
    this.setEditableDescriptionsLanguage(formControl.getEditableDescriptionsLanguage());
    this.setMandatoryDescriptionsLanguage(formControl.getMandatoryDescriptionsLanguage());
    this.setDropdownListSource(formControl.getSource());
    // se è impostata una source da cui recuperare i valori dei dropdown, non serve valorizzare la lista
    if (StringUtils.isBlank(formControl.getSource())) {
      this.setDropdownValues(formControl.getDropdownValues());
    }
    final boolean grPage = TamApePageType.isGrPage(page);
    this.setGoldenRecordAttribute(grPage);
    this.setCustomerFieldInvalidForClientConfig(formControl.isCustomerFieldInvalidForClientConfig());
    this.setChildren(formControl.getChildren());
  }

  @Override
  public SmartFormControl clone() {
    try {
      return (SmartFormControl) super.clone();
    } catch (final CloneNotSupportedException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   settare un masterAttribute significa negare la controparte goldenRecordAttribute
   */
  public void setMasterAttribute(final boolean masterAttribute) {
    this.masterAttribute = masterAttribute;
    this.goldenRecordAttribute = !masterAttribute;
  }

  /**
   settare un goldenRecordAttribute significa negare la controparte masterAttribute
   */
  public void setGoldenRecordAttribute(final boolean goldenRecordAttribute) {
    this.goldenRecordAttribute = goldenRecordAttribute;
    this.masterAttribute = !goldenRecordAttribute;
  }

}
