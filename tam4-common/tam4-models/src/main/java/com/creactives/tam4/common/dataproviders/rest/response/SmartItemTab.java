package com.creactives.tam4.common.dataproviders.rest.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Questo oggetto è uguale alla classe {@link com.creactives.tam4.common.dataproviders.rest.response.ItemTab} ma contiene una lista di {@link SmartFormControl}
 * al posto di {@link com.creactives.tam4.common.dataproviders.rest.response.FormControl}.
 * E' stato creato per evitare troppi impatti.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(Include.NON_NULL)
public class SmartItemTab {

  protected String tabLabel;
  private String tabKey;
  private Integer order;
  private boolean hasMandatoryAttributes;
  private boolean hasTechnicalAttributes;
  private boolean hasLocalizedData;
  private List<SmartFormControl> requests = new ArrayList<>();
  @Deprecated //usato dal vecchio editor angular
  private List<RelationshipDetails> relationships = new ArrayList<>();
  private GoldenRecordDetails goldenRecordDetails;
}
