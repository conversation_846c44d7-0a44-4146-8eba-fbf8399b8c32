package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import com.creactives.tam4.rest.SmallMasterData;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AdditionalMaterialInformation {
  private UUID materialId;
  private UUID imageId;
  private List<UUID> attachmentsId;
  private String domain;
  private String description;
  private List<MaterialAlternativeUnitOfMeasureDetails> alternativeUnitsOfMeasure;
  private UUID goldenRecord;
  private String goldenRecordCode;
  private boolean semanticallyAnalyzed;
  private SmallMasterData copiedMaterialData = null;
}
