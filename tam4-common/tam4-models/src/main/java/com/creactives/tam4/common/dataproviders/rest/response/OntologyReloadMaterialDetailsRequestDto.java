package com.creactives.tam4.common.dataproviders.rest.response;

import com.creactives.tam4.messaging.CategoryKey;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.rest.MaterialPlantValuations;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Set;

/**
 * Oggetto per interrogare il microservizio ontology e ricaricare i dati dei materiali a seguito della modifica di un attributo
 * o di una categoria
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class OntologyReloadMaterialDetailsRequestDto extends OntologyGetMaterialDetailsRequestDto {

  /**
   * Attributo modificato
   */
  private SmartFormControl attributeChanged;

  /**
   * Categoria modificata
   */
  private CategoryKey categoryChanged;

  /**
   * Dettaglio dei plant associati al materiale
   **/
  private List<MaterialPlantDetails> plantDetails;

  /**
   * Lista dei locales mandatory per il materiale in base ai plant
   **/
  private Set<String> plantLocales;

  /**
   * Dettaglio delle valuations dei plant associati al materiale
   **/
  @Nullable
  private MaterialPlantValuations materialPlantValuations;
}
