package com.creactives.tam4.common.dataproviders.rest.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.LinkedList;
import java.util.Map;

/**
 * Dto contenente le informazioni a seguito del reload delle informazioni del materiale
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class SmartReloadMaterialDetailsResponseDto extends SmartMaterialDetailsResponseDto {

  /**
   * Lista di plant associati al materiale
   **/
  private LinkedList<SmartPlantData> plants;
  private Map<String, SmartPlantData> grPlantInstances;
}
