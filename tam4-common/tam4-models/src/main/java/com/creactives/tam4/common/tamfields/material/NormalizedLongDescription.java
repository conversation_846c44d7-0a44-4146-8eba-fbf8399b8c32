package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class NormalizedLongDescription extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    final Map<String, Change<String>> normLongDescMapChange = CalculateChangesUtils.calculateMapChange(editedMaterialDetails.getDescriptions().getNormalizedLongDescriptions(),
        originalMaterialDetails.getDescriptions().getNormalizedLongDescriptions());
    changes.setNormalizedLongDescriptions(normLongDescMapChange);
  }

  @Override
  public String fieldName() {
    return AttributeCodes.NORM_LONG_DESC;
  }
}
