package com.creactives.tam4.common.dataproviders.rest.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExistingEditProcessInfo {
  private List<ExistingEditProcess> goldenRecordEnrichmentProcesses;
  private List<ExistingEditProcess> instancesEnrichmentProcesses;
  private boolean haveOngoingLinkUnlinkProcessOnInstance;
}
