package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

@Service
public class ManufacturerCode extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    changes.setManufacturerCode(new CalculateChangesUtils(originalMaterialDetails
        .getManufacturerDetails()
        .getManufacturerCode(),
        editedMaterialDetails
            .getManufacturerDetails()
            .getManufacturerCode(),
        false).applyChange());
  }

  @Override
  public String fieldName() {
    return AttributeCodes.MANUFACTURER_CODE;
  }
}
