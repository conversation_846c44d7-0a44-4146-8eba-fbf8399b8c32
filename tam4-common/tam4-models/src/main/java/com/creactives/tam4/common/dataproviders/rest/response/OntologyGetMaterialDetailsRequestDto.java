package com.creactives.tam4.common.dataproviders.rest.response;


import com.creactives.tam4.messaging.CategoryKey;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materials.events.TaxonomyType;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * Oggetto per interrogare il micro servizio ontology e recuperare i dati iniziali del materiale
 */
@Data
public class OntologyGetMaterialDetailsRequestDto {
  /**
   * {@link com.creactives.tam4.messaging.materialdetails.MaterialDetails} popolato con i dati a disposizione
   **/
  private MaterialDetails materialDetails;

  /**
   * Categoria principale da utilizzare per il recupero degli attributi
   **/
  private Map<TaxonomyType, CategoryKey> categories;

  /**
   * Descrizione del materiale inserita dall'utente
   **/
  private String description;

  /**
   * Lingua corrente
   **/
  private String language;

  /**
   * client corrente
   **/
  private String client;

  /**
   * Flag abilitazione golden record
   **/
  private boolean createGoldenRecord;

  /**
   * eventuali lingue da utilizzare
   **/
  private List<String> fallbackLanguages;

  /**
   * {@link com.creactives.tam4.messaging.materialdetails.MaterialDetails} popolato con i dati del GR (in caso di istanza di GR)
   **/
  @Nullable
  private MaterialDetails grMaterialDetails;

}
