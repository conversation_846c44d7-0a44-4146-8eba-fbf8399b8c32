package com.creactives.tam4.common.tamfields.material;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.CalculateChangesUtils;
import com.creactives.tam4.common.utils.strategies.MaterialFieldChangeStrategy;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.springframework.stereotype.Service;

@Service
public class PurchasingUnitOfMeasurement extends TamMaterialField implements MaterialFieldChangeStrategy {

  @Override
  public void computeMaterialFieldChange(final MaterialDetails originalMaterialDetails,
                                         final MaterialDetails editedMaterialDetails, final Changes changes) {
    changes.setPurchasingUnitOfMeasurement(new CalculateChangesUtils(originalMaterialDetails
        .getUnitsOfMeasure()
        .getPurchasingUnitOfMeasurement(),
        editedMaterialDetails
            .getUnitsOfMeasure()
            .getPurchasingUnitOfMeasurement(),
        false).applyChange());
  }

  @Override
  public String fieldName() {
    return AttributeCodes.PURCHASINGUNITOFMEASUREMENT;
  }
}
