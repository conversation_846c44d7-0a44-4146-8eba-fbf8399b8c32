package com.creactives.tam4.common.mappers;

import com.creactives.tam4.common.models.dto.dataloader.AlternativeUnitOfMeasurementDto;
import com.creactives.tam4.common.models.dto.dataloader.creation.v4.MasterDataCreationDtoV4;
import com.creactives.tam4.common.models.dto.dataloader.creation.v4.MaterialMasterDataCreationDtoV4;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MaterialMasterDataCreationDtoV4Test {

  @Test
  void fromCopiesAllSourcePropertiesAndSupportsChaining() {
    final MasterDataCreationDtoV4 src = new MasterDataCreationDtoV4();
    src.setDeletionFlag(true);
    src.setMaterialGroup("GRP1");
    src.setBaseUnitOfMeasurement("EA");
    src.setAuthorizationGroup("AUTH1");
    src.setCrossPlantMaterialStatus("OK");
    src.setRevisionNumber(String.valueOf(42));
    src.setPurchasingMeasurementUnit("KG");
    src.setSemanticallyAnalyzed(false);
    src.setAlternativeUnitOfMeasurements(List.of(new AlternativeUnitOfMeasurementDto(1L, "string", 2L)));
    src.setGeneric(true);

    // Define the extra fields you add via chaining
    final String expectedClient = "CLIENT_X";
    final String expectedMaterialCode = "MAT_CODE";
    final String expectedMaterialType = "TYPE_A";

    final MaterialMasterDataCreationDtoV4 actual = MaterialMasterDataCreationDtoV4
        .from(src)
        .client(expectedClient)
        .materialCode(expectedMaterialCode)
        .materialType(expectedMaterialType)
        .build();

    assertEquals(src.isDeletionFlag(), actual.isDeletionFlag(),
        "deletionFlag should be copied");
    assertEquals(src.getMaterialGroup(), actual.getMaterialGroup(),
        "materialGroup should be copied");
    assertEquals(src.getBaseUnitOfMeasurement(), actual.getBaseUnitOfMeasurement(),
        "baseUnitOfMeasurement should be copied");
    assertEquals(src.getAuthorizationGroup(), actual.getAuthorizationGroup(),
        "authorizationGroup should be copied");
    assertEquals(src.getCrossPlantMaterialStatus(), actual.getCrossPlantMaterialStatus(),
        "crossPlantMaterialStatus should be copied");
    assertEquals(src.getRevisionNumber(), actual.getRevisionNumber(),
        "revisionNumber should be copied");
    assertEquals(src.getPurchasingMeasurementUnit(), actual.getPurchasingMeasurementUnit(),
        "purchasingMeasurementUnit should be copied");
    assertEquals(src.isSemanticallyAnalyzed(), actual.isSemanticallyAnalyzed(),
        "semanticallyAnalyzed should be copied");
    assertEquals(src.isDeletionFlag(), actual.isGeneric(),
        "isGeneric should be copied");
    assertEquals(src.getAlternativeUnitOfMeasurements(), actual.getAlternativeUnitOfMeasurements(),
        "alternativeUnitOfMeasurements should be copied");

    // Assert that chaining still works
    assertEquals(expectedClient, actual.getClient(), "client should be set by chain");
    assertEquals(expectedMaterialCode, actual.getMaterialCode(),
        "materialCode should be set by chain");
    assertEquals(expectedMaterialType, actual.getMaterialType(),
        "materialType should be set by chain");
  }
}
