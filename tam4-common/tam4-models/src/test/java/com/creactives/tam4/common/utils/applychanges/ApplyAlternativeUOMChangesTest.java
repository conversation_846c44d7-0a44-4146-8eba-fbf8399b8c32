package com.creactives.tam4.common.utils.applychanges;


import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materialdetails.UnitsOfMeasure;
import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ApplyAlternativeUOMChangesTest {

  @Test
  @DisplayName("Apply change by removing existing AUOM")
  void applychange_alternativeUOM_whenRemovingExistingAUOM_excludeAUOMFromList() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> materialAlternativeUnitOfMeasureDetails = new ArrayList<>();
    materialAlternativeUnitOfMeasureDetails.add(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").build());
    materialDetails.setUnitsOfMeasure(UnitsOfMeasure.builder().alternativeUnitsOfMeasure(materialAlternativeUnitOfMeasureDetails).build());
    final ArrayList<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitOfMeasureChanges = new ArrayList<>();
    final Change<MaterialAlternativeUnitOfMeasureDetails> removeAUOMChange = new Change<>(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").build(), null);
    alternativeUnitOfMeasureChanges.add(removeAUOMChange);

    ApplyMaterialAlternativeUnitOfMeasureChanges.applyMaterialAlternativeUnitOfMeasureDetailsChanges(alternativeUnitOfMeasureChanges, materialDetails, false);

    final List<MaterialAlternativeUnitOfMeasureDetails> newAUOM = materialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure();
    Assertions.assertThat(newAUOM).doesNotContainNull();
    Assertions.assertThat(newAUOM).isEmpty();

  }

  @Test
  @DisplayName("Apply new value if old is equal to current when ignore old values is false")
  void applychange_alternativeUOM_applyNewValueIfCurrentIsEqualToOldValue_falseIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> materialAlternativeUnitOfMeasureDetails = new ArrayList<>();
    materialAlternativeUnitOfMeasureDetails.add(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ZERO).numerator(BigDecimal.ONE).build());
    materialDetails.setUnitsOfMeasure(UnitsOfMeasure.builder().alternativeUnitsOfMeasure(materialAlternativeUnitOfMeasureDetails).build());
    final ArrayList<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitOfMeasureChanges = new ArrayList<>();
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUOMChange = new Change<>(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ZERO).numerator(BigDecimal.ONE).build(),
        MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ONE).build()
    );
    alternativeUnitOfMeasureChanges.add(alternativeUOMChange);

    ApplyMaterialAlternativeUnitOfMeasureChanges.applyMaterialAlternativeUnitOfMeasureDetailsChanges(alternativeUnitOfMeasureChanges, materialDetails, false);

    final List<MaterialAlternativeUnitOfMeasureDetails> newAUOM = materialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure();
    Assertions.assertThat(newAUOM.get(0).getDenominator()).isEqualTo(BigDecimal.ONE);

  }

  @Test
  @DisplayName("Apply new value if old is not equal to current but ignore old values is true")
  void applychange_alternativeUOM_applyNewValueIfCurrentIsNotEqualToOldValue_whenTrueIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> materialAlternativeUnitOfMeasureDetails = new ArrayList<>();
    materialAlternativeUnitOfMeasureDetails.add(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ZERO).build());
    materialDetails.setUnitsOfMeasure(UnitsOfMeasure.builder().alternativeUnitsOfMeasure(materialAlternativeUnitOfMeasureDetails).build());
    final ArrayList<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitOfMeasureChanges = new ArrayList<>();
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUOMChange = new Change<>(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ONE).build(),
        MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(BigDecimal.ONE).build()
    );
    alternativeUnitOfMeasureChanges.add(alternativeUOMChange);

    ApplyMaterialAlternativeUnitOfMeasureChanges.applyMaterialAlternativeUnitOfMeasureDetailsChanges(alternativeUnitOfMeasureChanges, materialDetails, true);

    final List<MaterialAlternativeUnitOfMeasureDetails> newAUOM = materialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure();
    Assertions.assertThat(newAUOM.get(0).getDenominator()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void applychange_alternativeUOM_applyNullIfCurrentIsNull_falseIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> materialAlternativeUnitOfMeasureDetails = new ArrayList<>();
    materialAlternativeUnitOfMeasureDetails.add(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build());
    materialDetails.setUnitsOfMeasure(UnitsOfMeasure.builder().alternativeUnitsOfMeasure(materialAlternativeUnitOfMeasureDetails).build());
    final ArrayList<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitOfMeasureChanges = new ArrayList<>();
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUOMChange = new Change<>(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build(), MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build());
    alternativeUnitOfMeasureChanges.add(alternativeUOMChange);

    ApplyMaterialAlternativeUnitOfMeasureChanges.applyMaterialAlternativeUnitOfMeasureDetailsChanges(alternativeUnitOfMeasureChanges, materialDetails, false);

    final List<MaterialAlternativeUnitOfMeasureDetails> newAUOM = materialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure();
    Assertions.assertThat(newAUOM.get(0).getDenominator()).isNull();
  }

  @Test
  @DisplayName("Apply null if new value is null and ignore old values is true")
  void applychange_alternativeUOM_applyNullIfCurrentIsNull_trueIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> materialAlternativeUnitOfMeasureDetails = new ArrayList<>();
    materialAlternativeUnitOfMeasureDetails.add(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build());
    materialDetails.setUnitsOfMeasure(UnitsOfMeasure.builder().alternativeUnitsOfMeasure(materialAlternativeUnitOfMeasureDetails).build());
    final ArrayList<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitOfMeasureChanges = new ArrayList<>();
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUOMChange = new Change<>(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build(), MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement("unitMeasure").denominator(null).build());
    alternativeUnitOfMeasureChanges.add(alternativeUOMChange);

    ApplyMaterialAlternativeUnitOfMeasureChanges.applyMaterialAlternativeUnitOfMeasureDetailsChanges(alternativeUnitOfMeasureChanges, materialDetails, true);

    final List<MaterialAlternativeUnitOfMeasureDetails> newAUOM = materialDetails.getUnitsOfMeasure().getAlternativeUnitsOfMeasure();
    Assertions.assertThat(newAUOM.get(0).getDenominator()).isNull();
  }

}
