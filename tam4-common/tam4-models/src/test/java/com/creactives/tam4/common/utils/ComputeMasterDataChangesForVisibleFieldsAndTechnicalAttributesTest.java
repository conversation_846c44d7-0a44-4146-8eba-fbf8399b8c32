package com.creactives.tam4.common.utils;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.constants.attributes.TabCodes;
import com.creactives.tam4.common.dataproviders.rest.response.FormControl;
import com.creactives.tam4.common.dataproviders.rest.response.ItemTab;
import com.creactives.tam4.common.models.rest.MasterDataSnapshot;
import com.creactives.tam4.common.tamfields.material.BasicMaterial;
import com.creactives.tam4.common.tamfields.material.Client;
import com.creactives.tam4.common.tamfields.material.CrossPlantMaterialStatus;
import com.creactives.tam4.common.tamfields.material.DocumentNumber;
import com.creactives.tam4.common.tamfields.material.IndustrySector;
import com.creactives.tam4.common.tamfields.material.MaterialCode;
import com.creactives.tam4.common.tamfields.material.MaterialGroup;
import com.creactives.tam4.common.tamfields.material.MaterialType;
import com.creactives.tam4.common.tamfields.material.ProductDivision;
import com.creactives.tam4.common.tamfields.material.ProductHierarchy;
import com.creactives.tam4.common.tamfields.material.SpecificaTecnica;
import com.creactives.tam4.common.tamfields.material.UnitOfMeasure;
import com.creactives.tam4.common.tamfields.plant.CertificateType;
import com.creactives.tam4.common.tamfields.plant.CheckingGroupAvailabilityCheck;
import com.creactives.tam4.common.tamfields.plant.GoodsReceiptsProcessing;
import com.creactives.tam4.common.tamfields.plant.MaxStockLevel;
import com.creactives.tam4.common.tamfields.plant.MinLotSize;
import com.creactives.tam4.common.tamfields.plant.MrpController;
import com.creactives.tam4.common.tamfields.plant.MrpType;
import com.creactives.tam4.common.tamfields.plant.PlannedDeliveryTime;
import com.creactives.tam4.common.tamfields.plant.Plant;
import com.creactives.tam4.common.tamfields.plant.PlantSpecificMaterialStatus;
import com.creactives.tam4.common.tamfields.plant.ProfitCenter;
import com.creactives.tam4.common.tamfields.plant.PurchasingGroupPlant;
import com.creactives.tam4.common.tamfields.plant.ReorderPoint;
import com.creactives.tam4.common.tamfields.plant.SafetyStockLevel;
import com.creactives.tam4.common.tamfields.plant.SchedulingMarginKey;
import com.creactives.tam4.common.tamfields.plant.price.MovingAveragePrice;
import com.creactives.tam4.common.tamfields.plant.price.StandardPrice;
import com.creactives.tam4.common.tamfields.plant.storagelocation.Warehouse;
import com.creactives.tam4.common.utils.strategies.AttributeStrategyContainer;
import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.attributes.Attribute;
import com.creactives.tam4.messaging.attributes.SingleValueAttribute;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materialdetails.TechnicalAttributes;
import com.creactives.tam4.messaging.materials.MdDomain;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import com.creactives.tam4.utils.LoadResourceFile;
import com.fasterxml.jackson.core.type.TypeReference;
import io.jsonwebtoken.lang.Maps;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComputeMasterDataChangesForVisibleFieldsAndTechnicalAttributesTest {

  public static final MaterialDetails EMPTY_MATERIAL_DETAILS = MaterialDetails.createEmptyMaterialDetails();
  public static final String PLANT = "PLANT";
  public static final String CLIENT = "CLIENT";
  public static final String STORAGE = "storage";
  private static final String PLANT2 = "PLANT2";
  private static final String TABS = "tabs.json";
  private static final String INITIAL = "initial.json";
  private static final String EDITED = "edited.json";
  private final LoadResourceFile loadResourceFile = new LoadResourceFile();
  @Mock
  private ApplicationContext applicationContext;

  private ComputeMasterDataChangesForVisibleFieldsAndTechnicalAttributes computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes;

  private static SingleValueAttribute getSingleValueAttribute(final String editableTechAttrNewValue, final String editableTechAttrValue) {
    return new SingleValueAttribute(editableTechAttrNewValue, editableTechAttrValue);
  }

  @BeforeEach
  public void init() {
    computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes = new ComputeMasterDataChangesForVisibleFieldsAndTechnicalAttributes(getAttributeContainer());
  }

  @Test
  void givenNoPlant_whenAddingVisiblePlantAndWarehouse_thenShouldReturnChangeWithPlantAndWarehouse() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PLANTS).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.WAREHOUSE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, null,
        Collections.singletonList(materialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    final MaterialPlantDetails newMaterialPlantDetails = actual.getNewValue();
    final PlantKey newPlantKey = newMaterialPlantDetails.getPlantKey();
    assertThat(newPlantKey.getCode()).isEqualTo(plantKey.getCode());
    assertThat(newPlantKey.getClient()).isEqualTo(plantKey.getClient());
    final List<MaterialStorageLocationDetails> newStorageLocations = newMaterialPlantDetails.getStorageLocations();
    assertThat(newStorageLocations).hasSize(1);
    assertThat(newStorageLocations.get(0).getStorageLocation()).isEqualTo(STORAGE);
  }

  @Test
  void givenPlantAndWarehouse_whenRemovingWarehouse_thenReturnChangeWithOnlyPlant() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails oldMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();
    final MaterialPlantDetails newMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PLANTS).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.WAREHOUSE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        Collections.singletonList(oldMaterialPlantDetails), Collections.singletonList(newMaterialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> plantDetailsChange = plantDetails.get(0);
    final MaterialPlantDetails newMaterialPlantDetailsFromChanges = plantDetailsChange.getNewValue();
    final PlantKey newPlantKey = newMaterialPlantDetailsFromChanges.getPlantKey();
    assertThat(newPlantKey.getCode()).isEqualTo(plantKey.getCode());
    assertThat(newPlantKey.getClient()).isEqualTo(plantKey.getClient());
    final List<MaterialStorageLocationDetails> newStorageLocations = newMaterialPlantDetailsFromChanges.getStorageLocations();
    assertThat(newStorageLocations).hasSize(0);
  }

  @Test
  void givenNoPlant_whenAddingHiddenPlantAndWarehouse_thenShouldReturnNullPlantChange() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(true).id(AttributeCodes.PLANTS).build());
    requests.add(FormControl.builder().hidden(true).id(AttributeCodes.WAREHOUSE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, null,
        Collections.singletonList(materialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).isNull();
  }

  @Test
  void givenEmptyPlant_whenInsertingVisiblePlantAndWarehouse_thenShouldReturnChangeWithRemoveOfEmptyPlantAndInsertOfNewPlantAndWarehouse() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey)
        .storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PLANTS).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.WAREHOUSE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        Collections.emptyList(),
        Collections.singletonList(materialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getStorageLocations()).hasSize(1);
    assertThat(actual.getNewValue().getStorageLocations().get(0).getStorageLocation()).isEqualTo(STORAGE);
  }

  @Test
  void givenPlantDetailsWithoutPrices_whenAddingVisiblePriceFields_thenShouldReturnChangeWithPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails newMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey)
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey)
            .standardPrice(BigDecimal.ONE).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.STANDARD_PRICE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().plantKey(plantKey).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        oldPlantDetailsList,
        Collections.singletonList(newMaterialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(actual.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void givenPlantDetailsWithEmptyPrices_whenAddingVisiblePriceFields_thenShouldReturnChangeWithPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey)
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).standardPrice(BigDecimal.ONE).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.STANDARD_PRICE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(Collections.emptyList()).plantKey(plantKey).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        oldPlantDetailsList,
        Collections.singletonList(materialPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(actual.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void givenPlantDetailsWithStandardPrices_whenEditingVisibleStandardPrice_thenShouldReturnChangeWithStandardPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey)
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey)
            .standardPrice(BigDecimal.ZERO).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.STANDARD_PRICE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().standardPrice(BigDecimal.ONE)
        .plantKey(plantKey).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        oldPlantDetailsList,
        Collections.singletonList(newPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    final MaterialPlantDetails materialPlantDetails = actual.getNewValue();
    final PlantKey plantKey1 = materialPlantDetails.getPlantKey();
    assertThat(plantKey1.getCode()).isEqualTo(plantKey.getCode());
    assertThat(plantKey1.getClient()).isEqualTo(plantKey.getClient());
    final List<MaterialPriceDetails> materialPriceDetails = materialPlantDetails.getMaterialPriceDetails();
    assertThat(materialPriceDetails).hasSize(1);
    assertThat(materialPriceDetails.get(0).getStandardPrice()).isEqualTo(BigDecimal.ZERO);
    assertThat(actual.getOldValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void givenPlantDetailsWithStandardPrices_whenEditingVisibleStandardPriceAndPlant_thenShouldReturnChangeWithRemovalOfOldPlantAddOfNewAndStandardPrice() {
    final PlantKey plantKey1 = new PlantKey(PLANT, CLIENT);
    final PlantKey plantKey2 = new PlantKey(PLANT2, CLIENT);
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey2)
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey2)
            .standardPrice(BigDecimal.ZERO).build())).build();
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.STANDARD_PRICE).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PLANTS).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().standardPrice(BigDecimal.ONE)
        .plantKey(plantKey1).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey1).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        oldPlantDetailsList,
        Collections.singletonList(newPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(2);

    final Change<MaterialPlantDetails> removalOfOld = plantDetails.get(0);
    assertThat(removalOfOld.getOldValue().getPlantKey().getCode()).isEqualTo(plantKey1.getCode());
    assertThat(removalOfOld.getOldValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);

    final Change<MaterialPlantDetails> addOfNew = plantDetails.get(1);
    assertThat(addOfNew.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey2.getCode());
    assertThat(addOfNew.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(addOfNew.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ZERO);
  }

  @Test
  void givenPlantDetailsWithStandardPriceAndMoving_whenEditingVisibleStandardPriceAndMoving_thenShouldReturnChangeWithOnlyAddOfNewStandardPriceAndMoving() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final BigDecimal ten = BigDecimal.TEN;

    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.STANDARD_PRICE).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PLANTS).build());
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.MOVING_AVERAGE_PRICE).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey).build());

    final BigDecimal one = BigDecimal.ONE;
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey)
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).movingAveragePrice(one)
            .standardPrice(ten).build())).build();
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS,
        oldPlantDetailsList,
        Collections.singletonList(newPlantDetails), tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);

    final Change<MaterialPlantDetails> plantDetailsChange = plantDetails.get(0);
    assertThat(plantDetailsChange.getOldValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    final MaterialPriceDetails materialPriceDetailsOld = plantDetailsChange.getOldValue().getMaterialPriceDetails().get(0);
    final MaterialPriceDetails materialPriceDetailsNew = plantDetailsChange.getNewValue().getMaterialPriceDetails().get(0);
    assertThat(materialPriceDetailsOld.getStandardPrice()).isNull();
    assertThat(materialPriceDetailsNew.getStandardPrice()).isEqualTo(ten);

    assertThat(materialPriceDetailsOld.getMovingAveragePrice()).isNull();
    assertThat(materialPriceDetailsNew.getMovingAveragePrice()).isEqualTo(one);
  }

  @SneakyThrows
  @Test
  void testPlantDataChanged() {
    final String initialMasterdataSnapshotString = get(INITIAL);
    final MasterDataSnapshot initialMasterDataSnapshot = JSONUtils.getObject(initialMasterdataSnapshotString, MasterDataSnapshot.class);
    final MaterialDetails initialMaterialDetails = initialMasterDataSnapshot.getMaterialDetails();
    final List<MaterialPlantDetails> initialPlantDetails = initialMasterDataSnapshot.getPlantDetails();

    final String masterdataSnapshotString = get(EDITED);
    final MasterDataSnapshot masterDataSnapshot = JSONUtils.getObject(masterdataSnapshotString, MasterDataSnapshot.class);
    final MaterialDetails changedMaterialDetails = masterDataSnapshot.getMaterialDetails();
    final List<MaterialPlantDetails> changedPlantDetails = masterDataSnapshot.getPlantDetails();

    final String tabsString = get(TABS);
    final List<ItemTab> tabs = JSONUtils.getObject(tabsString, new TypeReference<>() {
    });


    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(initialMaterialDetails,
        changedMaterialDetails,
        initialPlantDetails,
        changedPlantDetails,
        tabs);
    final List<Change<MaterialPlantDetails>> plantDetails = changesOnlyForVisibleFieldsAndTechnicalAttributes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);

    final Change<MaterialPlantDetails> plantDetailsChange = plantDetails.get(0);
    final Integer leadTimeOld = plantDetailsChange.getOldValue().getLeadTimeInDays();
    final Integer leadTimeNew = plantDetailsChange.getNewValue().getLeadTimeInDays();
    assertThat(leadTimeOld).isEqualTo(15);
    assertThat(leadTimeNew).isEqualTo(16);
    assertThat(plantDetailsChange.getOldValue().getLotSize()).isEqualTo("HB");
    assertThat(plantDetailsChange.getNewValue().getLotSize()).isEqualTo("HBO");
    assertThat(plantDetailsChange.getNewValue().getProfitCenter()).isEqualTo("ABC");
  }

  private String get(final String filePath) throws IOException {
    return loadResourceFile.getContentFromJunitFilesFolder(filePath);
  }

  private AttributeStrategyContainer getAttributeContainer() {
    when(applicationContext.getBeanNamesForType(any(Class.class))).thenReturn(new String[]{});
    final AttributeStrategyContainer attributeStrategyContainer = new AttributeStrategyContainer(applicationContext);
    final Plant plant = new Plant();
    final Client client = new Client();
    final MaterialCode materialCode = new MaterialCode();
    final MaterialType materialType = new MaterialType();
    final IndustrySector industrySector = new IndustrySector();
    final UnitOfMeasure unitOfMeasure = new UnitOfMeasure();
    final MaterialGroup materialGroup = new MaterialGroup();
    final SpecificaTecnica specificaTecnica = new SpecificaTecnica();
    final BasicMaterial basicMaterial = new BasicMaterial();
    final DocumentNumber documentNumber = new DocumentNumber();
    final ProductDivision productDivision = new ProductDivision();
    final CrossPlantMaterialStatus crossPlantMaterialStatus = new CrossPlantMaterialStatus();
    final Warehouse warehouse = new Warehouse();
    final StandardPrice standardPrice = new StandardPrice();
    final MovingAveragePrice movingAveragePrice = new MovingAveragePrice();
    final PlannedDeliveryTime plannedDeliveryTime = new PlannedDeliveryTime();
    final PurchasingGroupPlant purchasingGroup = new PurchasingGroupPlant();
    final MrpType mrpType = new MrpType();
    final ReorderPoint reorderPoint = new ReorderPoint();
    final MrpController mrpController = new MrpController();
    final MaxStockLevel maxStockLevel = new MaxStockLevel();
    final GoodsReceiptsProcessing goodsReceiptsProcessing = new GoodsReceiptsProcessing();
    final SchedulingMarginKey schedulingMarginKey = new SchedulingMarginKey();
    final SafetyStockLevel safetyStockLevel = new SafetyStockLevel();
    final CheckingGroupAvailabilityCheck checkingGroupAvailabilityCheck = new CheckingGroupAvailabilityCheck();
    final ProfitCenter profitCenter = new ProfitCenter();
    final CertificateType certificateType = new CertificateType();
    final PlantSpecificMaterialStatus plantSpecificMaterialStatus = new PlantSpecificMaterialStatus();
    final MinLotSize minLotSize = new MinLotSize();
    final ProductHierarchy productHierarchy = new ProductHierarchy();
    attributeStrategyContainer.setAttributeStrategies(Map.ofEntries(entry(client.fieldName(), client),
        entry(materialCode.fieldName(), materialCode),
        entry(materialType.fieldName(), materialType),
        entry(industrySector.fieldName(), industrySector),
        entry(unitOfMeasure.fieldName(), unitOfMeasure),
        entry(materialGroup.fieldName(), materialGroup),
        entry(specificaTecnica.fieldName(), specificaTecnica),
        entry(basicMaterial.fieldName(), basicMaterial),
        entry(documentNumber.fieldName(), documentNumber),
        entry(productDivision.fieldName(), productDivision),
        entry(crossPlantMaterialStatus.fieldName(), crossPlantMaterialStatus),
        entry(plant.fieldName(), plant),
        entry(warehouse.fieldName(), warehouse),
        entry(standardPrice.fieldName(), standardPrice),
        entry(movingAveragePrice.fieldName(), movingAveragePrice),
        entry(plannedDeliveryTime.fieldName(), plannedDeliveryTime),
        entry(purchasingGroup.fieldName(), purchasingGroup),
        entry(mrpType.fieldName(), mrpType),
        entry(reorderPoint.fieldName(), reorderPoint),
        entry(mrpController.fieldName(), mrpController),
        entry(maxStockLevel.fieldName(), maxStockLevel),
        entry(goodsReceiptsProcessing.fieldName(), goodsReceiptsProcessing),
        entry(schedulingMarginKey.fieldName(), schedulingMarginKey),
        entry(safetyStockLevel.fieldName(), safetyStockLevel),
        entry(checkingGroupAvailabilityCheck.fieldName(), checkingGroupAvailabilityCheck),
        entry(profitCenter.fieldName(), profitCenter),
        entry(certificateType.fieldName(), certificateType),
        entry(plantSpecificMaterialStatus.fieldName(), plantSpecificMaterialStatus),
        entry(minLotSize.fieldName(), minLotSize),
        entry(productHierarchy.fieldName(), productHierarchy)
    ));
    return attributeStrategyContainer;
  }

  @Test
  void givenFilledCustomerFields_whenVisibleAndTheyBecomeNull_thenShouldReturnChangeWithCustomerFieldToNull() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String customerField = "Area";
    requests.add(FormControl.builder().hidden(false).id(customerField).customer(true).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String customerFieldValue = "3";
    originalMaterialDetails.setCustomerFields(Maps.of(customerField, customerFieldValue).build());
    final MaterialDetails editedCustomerFields = originalMaterialDetails.clone();
    editedCustomerFields.setCustomerFields(null);
    //in questo caso è come se dicessi che il campo non esiste più in configurazione
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedCustomerFields,
        null, null, tabs);
    final Map<String, Change<String>> customerFieldsChanges = changesOnlyForVisibleFieldsAndTechnicalAttributes.getCustomerFields();
    assertThat(customerFieldsChanges).hasSize(1);
    assertThat(customerFieldsChanges.containsKey(customerField)).isTrue();
    final Change<String> change = customerFieldsChanges.get(customerField);
    assertThat(change.getNewValue()).isNull();
    assertThat(change.getOldValue()).isEqualTo(customerFieldValue);
  }

  @Test
  void givenFilledCustomerFields_whenHiddenAndTheyBecomeNull_thenShouldReturnNullCustomerChanges() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String customerField = "Area";
    requests.add(FormControl.builder().hidden(true).id(customerField).customer(true).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String customerFieldValue = "3";
    originalMaterialDetails.setCustomerFields(Maps.of(customerField, customerFieldValue).build());
    final MaterialDetails editedCustomerFields = originalMaterialDetails.clone();
    editedCustomerFields.setCustomerFields(null);
    //in questo caso è come se dicessi che il campo non esiste più in configurazione
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedCustomerFields,
        null, null, tabs);
    final Map<String, Change<String>> customerFieldsChanges = changesOnlyForVisibleFieldsAndTechnicalAttributes.getCustomerFields();
    assertThat(customerFieldsChanges).isNull();
    /*assertThat(customerFieldsChanges).hasSize(0);
    assertThat(customerFieldsChanges.containsKey(customerField)).isTrue();
    final Change<String> change = customerFieldsChanges.get(customerField);
    assertThat(change.getNewValue()).isNull();
    assertThat(change.getOldValue()).isEqualTo(customerFieldValue);*/
  }

  @Test
  void givenFilledCustomerFields_whenVisibleAndChangesToNull_thenShouldReturnChangeWithCustomerFieldToNull() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String customerField = "Area";
    requests.add(FormControl.builder().hidden(false).id(customerField).customer(true).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String customerFieldValue = "3";
    originalMaterialDetails.setCustomerFields(Maps.of(customerField, customerFieldValue).build());
    final MaterialDetails editedCustomerFields = originalMaterialDetails.clone();
    editedCustomerFields.setCustomerFields(Maps.of(customerField, (String) null).build());
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedCustomerFields,
        null, null, tabs);
    final Map<String, Change<String>> customerFieldsChanges = changesOnlyForVisibleFieldsAndTechnicalAttributes.getCustomerFields();
    assertThat(customerFieldsChanges).hasSize(1);
    assertThat(customerFieldsChanges.containsKey(customerField)).isTrue();
    final Change<String> change = customerFieldsChanges.get(customerField);
    assertThat(change.getNewValue()).isNull();
    assertThat(change.getOldValue()).isEqualTo(customerFieldValue);
  }

  @Test
  void givenFilledCustomerFields_whenHiddenAndTheyBecomeNull_thenShouldReturnChangeWithoutCustomerFields() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String customerField = "Area";
    requests.add(FormControl.builder().hidden(true).id(customerField).customer(true).build());
    tabs.add(ItemTab.builder().tabKey(TabCodes.PLANT_DATA_TAB).requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String customerFieldValue = "3";
    originalMaterialDetails.setCustomerFields(Maps.of(customerField, customerFieldValue).build());
    final MaterialDetails editedCustomerFields = originalMaterialDetails.clone();
    editedCustomerFields.setCustomerFields(null);
    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedCustomerFields,
        null, null, tabs);
    final Map<String, Change<String>> customerFieldsChanges = changesOnlyForVisibleFieldsAndTechnicalAttributes.getCustomerFields();
    assertThat(customerFieldsChanges).isNull();
  }

  @Test
  void givenFilledCustomerFields_whenOneVisibleOtherHiddenAndBothChanges_thenShouldReturnChangeWithOnlyVisibleCustomerField() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String visibleCustomerField = "VisibleCustomerField";
    final String hiddenCustomerField = "HiddenCustomerField";
    requests.add(FormControl.builder().hidden(false).id(visibleCustomerField).customer(true).build());
    requests.add(FormControl.builder().hidden(true).id(hiddenCustomerField).customer(true).build());
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String visibleCustomerFieldValue = "3";
    final String hiddenCustomerFieldValue = "4";
    final Map<String, String> customerFields = new HashMap<>();
    customerFields.put(visibleCustomerField, visibleCustomerFieldValue);
    customerFields.put(hiddenCustomerField, hiddenCustomerFieldValue);
    originalMaterialDetails.setCustomerFields(customerFields);

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    final String newVisibleCustomerFieldValue = "5";
    final String newHiddenCustomerFieldValue = "6";
    editedMaterialDetails.getCustomerFields().put(visibleCustomerField, newVisibleCustomerFieldValue);
    editedMaterialDetails.getCustomerFields().put(hiddenCustomerField, newHiddenCustomerFieldValue);

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final Map<String, Change<String>> customerFieldsChanges = changesOnlyForVisibleFieldsAndTechnicalAttributes.getCustomerFields();
    assertThat(customerFieldsChanges).hasSize(1);
    assertThat(customerFieldsChanges.containsKey(visibleCustomerField)).isTrue();
    final Change<String> change = customerFieldsChanges.get(visibleCustomerField);
    assertThat(change.getNewValue()).isNull();
    assertThat(change.getOldValue()).isEqualTo(visibleCustomerFieldValue);
  }

  @Test
  void givenFilledTechnicalAttributes_whenOneVisibleOtherHiddenAndBothChanges_thenShouldReturnChangeWithBothTechnicalAttributes() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String visibleTechAttrName = "VisibleTechAttr";
    final String hiddenTechAttrName = "HiddenTechAttr";
    requests.add(FormControl.builder().hidden(false).id(visibleTechAttrName).technical(true).build());
    requests.add(FormControl.builder().hidden(true).id(hiddenTechAttrName).technical(true).build());
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String visibleTechAttrValue = "3";
    final String hiddenTechAttrValue = "4";
    final List<Attribute> technicalAttributes = new ArrayList<>();
    final SingleValueAttribute visibleTechAttr = getSingleValueAttribute(visibleTechAttrName, visibleTechAttrValue);
    technicalAttributes.add(visibleTechAttr);
    final SingleValueAttribute hiddenTechAttr = getSingleValueAttribute(hiddenTechAttrName, hiddenTechAttrValue);
    technicalAttributes.add(hiddenTechAttr);
    originalMaterialDetails.setTechnicalAttributes(TechnicalAttributes.builder().technicalAttributes(technicalAttributes).build());

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    final String visibleTechAttrNewValue = "5";
    final String hiddenTechAttrNewValue = "6";
    final List<Attribute> newTechnicalAttributes = new ArrayList<>();
    final SingleValueAttribute visibleTechAttrNew = getSingleValueAttribute(visibleTechAttrName, visibleTechAttrNewValue);
    newTechnicalAttributes.add(visibleTechAttrNew);
    final SingleValueAttribute hiddenTechAttrNew = getSingleValueAttribute(hiddenTechAttrName, hiddenTechAttrNewValue);
    newTechnicalAttributes.add(hiddenTechAttrNew);
    editedMaterialDetails.setTechnicalAttributes(TechnicalAttributes.builder().technicalAttributes(newTechnicalAttributes).build());

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final List<Change<Attribute>> technicalAttributesChangeList = changesOnlyForVisibleFieldsAndTechnicalAttributes.getTechnicalAttributes();
    assertThat(technicalAttributesChangeList).hasSize(2);
    final Optional<Change<Attribute>> visibleAttrOpt = technicalAttributesChangeList.stream().filter(it -> it.getOldValue().getCode().equals(visibleTechAttrName)).findAny();
    assertThat(visibleAttrOpt.isPresent()).isTrue();
    final Change<Attribute> change1 = visibleAttrOpt.get();
    assertThat(change1.getOldValue()).isEqualTo(visibleTechAttr);
    assertThat(change1.getNewValue()).isEqualTo(visibleTechAttrNew);

    final Optional<Change<Attribute>> hiddenAttrOpt = technicalAttributesChangeList.stream().filter(it -> it.getOldValue().getCode().equals(hiddenTechAttrName)).findAny();
    assertThat(hiddenAttrOpt.isPresent()).isTrue();
    final Change<Attribute> change2 = hiddenAttrOpt.get();
    assertThat(change2.getOldValue()).isEqualTo(hiddenTechAttr);
    assertThat(change2.getNewValue()).isEqualTo(hiddenTechAttrNew);
  }

  @Test
  void givenFilledTechnicalAttributes_whenOneVisibleAndBecomesNull_thenShouldReturnChangeWithVisibleTechnicalAttributes() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    final String visibleTechAttrName = "VisibleTechAttr";
    requests.add(FormControl.builder().hidden(false).id(visibleTechAttrName).technical(true).build());
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String visibleTechAttrValue = "3";
    final List<Attribute> technicalAttributes = new ArrayList<>();
    final SingleValueAttribute visibleTechAttr = getSingleValueAttribute(visibleTechAttrName, visibleTechAttrValue);
    technicalAttributes.add(visibleTechAttr);
    originalMaterialDetails.setTechnicalAttributes(TechnicalAttributes.builder().technicalAttributes(technicalAttributes).build());

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    final String visibleTechAttrNewValue = null;
    final List<Attribute> newTechnicalAttributes = new ArrayList<>();
    final SingleValueAttribute visibleTechAttrNew = getSingleValueAttribute(visibleTechAttrName, visibleTechAttrNewValue);
    newTechnicalAttributes.add(visibleTechAttrNew);
    editedMaterialDetails.setTechnicalAttributes(TechnicalAttributes.builder().technicalAttributes(newTechnicalAttributes).build());

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final List<Change<Attribute>> technicalAttributesChangeList = changesOnlyForVisibleFieldsAndTechnicalAttributes.getTechnicalAttributes();
    assertThat(technicalAttributesChangeList).hasSize(1);
    final Optional<Change<Attribute>> visibleAttrOpt = technicalAttributesChangeList.stream().filter(it -> it.getOldValue().getCode().equals(visibleTechAttrName)).findAny();
    assertThat(visibleAttrOpt.isPresent()).isTrue();
    final Change<Attribute> change = visibleAttrOpt.get();
    assertThat(change.getOldValue()).isEqualTo(visibleTechAttr);
    assertThat(change.getNewValue()).isEqualTo(visibleTechAttrNew);

  }

  @Test
  void givenFilledAttachments_whenAttachBecomesNull_thenShouldReturnChangeWithAttachmentsToNull() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final UUID initialAttach = UUID.randomUUID();
    originalMaterialDetails.setAttachments(Collections.singletonList(initialAttach));

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    editedMaterialDetails.setAttachments(null);

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final List<Change<UUID>> attachmentsChangeList = changesOnlyForVisibleFieldsAndTechnicalAttributes.getAttachments();
    assertThat(attachmentsChangeList).hasSize(1);
    final Change<UUID> change = attachmentsChangeList.get(0);
    assertThat(change.getOldValue()).isEqualTo(initialAttach);
    assertThat(change.getNewValue()).isNull();
  }

  @Test
  void givenFilledAttachments_whenGRSoNotVisibleAndBecomesNull_thenShouldReturnNoChange() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    originalMaterialDetails.setMdDomain(MdDomain.MGR.getCode());
    final UUID initialAttach = UUID.randomUUID();
    originalMaterialDetails.setAttachments(Collections.singletonList(initialAttach));

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    editedMaterialDetails.setAttachments(null);

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final List<Change<UUID>> attachmentsChangeList = changesOnlyForVisibleFieldsAndTechnicalAttributes.getAttachments();
    assertThat(attachmentsChangeList).isNull();
  }

  @Test
  void givenFilledProductHierarchy_whenVisibleAndBecomesNull_thenShouldReturnChangeWithAttachmentsToNull() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(false).id(AttributeCodes.PRODUCT_HIERARCHY).build());
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String initialProductHierarchy = "initial";
    originalMaterialDetails.setProductHierarchy(initialProductHierarchy);

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    editedMaterialDetails.setProductHierarchy(null);

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final Change<String> productHierarchyChange = changesOnlyForVisibleFieldsAndTechnicalAttributes.getProductHierarchy();
    assertThat(productHierarchyChange.getOldValue()).isEqualTo(initialProductHierarchy);
    assertThat(productHierarchyChange.getNewValue()).isNull();
  }

  @Test
  void givenFilledProductHierarchy_whenHiddenAndBecomesNull_thenShouldReturnNoChange() throws CloneNotSupportedException {
    final List<ItemTab> tabs = new ArrayList<>();
    final List<FormControl> requests = new ArrayList<>();
    requests.add(FormControl.builder().hidden(true).id(AttributeCodes.PRODUCT_HIERARCHY).build());
    tabs.add(ItemTab.builder().requests(requests).build());
    final MaterialDetails originalMaterialDetails = EMPTY_MATERIAL_DETAILS;
    final String initialProductHierarchy = "initial";
    originalMaterialDetails.setProductHierarchy(initialProductHierarchy);

    final MaterialDetails editedMaterialDetails = originalMaterialDetails.clone();
    editedMaterialDetails.setProductHierarchy(null);

    final Changes changesOnlyForVisibleFieldsAndTechnicalAttributes = computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.getChangesOnlyForVisibleFieldsAndTechnicalAttributes(originalMaterialDetails, editedMaterialDetails,
        null, null, tabs);
    final Change<String> productHierarchyChange = changesOnlyForVisibleFieldsAndTechnicalAttributes.getProductHierarchy();
    assertThat(productHierarchyChange).isNull();
  }

}
