package com.creactives.tam4.common.models;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ComputeChangesTest {

  public static final MaterialDetails EMPTY_MATERIAL_DETAILS = MaterialDetails.createEmptyMaterialDetails();
  public static final String PLANT = "PLANT";
  public static final String CLIENT = "CLIENT";
  public static final PlantKey PLANT_KEY = new PlantKey(PLANT, CLIENT);
  public static final String STORAGE = "storage";
  private static final String PLANT2 = "PLANT2";

  @Test
  void givenNoPlant_whenAddingPlantAndWarehouse_thenReturnChangeWithPlantAndWarehouse() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, null, Collections.singletonList(materialPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getStorageLocations()).hasSize(1);
    assertThat(actual.getNewValue().getStorageLocations().get(0).getStorageLocation()).isEqualTo(STORAGE);
  }


  @Test
  void givenEmptyPlant_whenInsertingPlantAndWarehouse_thenReturnChangeWithRemoveOfEmptyPlantAndInsertOfNewPlantAndWarehouse() {
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(PLANT_KEY).storageLocations(Collections.singletonList(MaterialStorageLocationDetails.builder().storageLocation(STORAGE).build())).build();

    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, Collections.emptyList(), Collections.singletonList(materialPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(PLANT_KEY.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(PLANT_KEY.getClient());
    assertThat(actual.getNewValue().getStorageLocations()).hasSize(1);
    assertThat(actual.getNewValue().getStorageLocations().get(0).getStorageLocation()).isEqualTo(STORAGE);
  }

  @Test
  void givenPlantDetailsWithoutPrices_whenAddingPriceFields_thenReturnChangeWithPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails newMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).standardPrice(BigDecimal.ONE).build())).build();

    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().plantKey(plantKey).build());
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, oldPlantDetailsList, Collections.singletonList(newMaterialPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(actual.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void givenPlantDetailsWithEmptyPrices_whenAddingPriceFields_thenReturnChangeWithPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails materialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).standardPrice(BigDecimal.ONE).build())).build();

    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(Collections.emptyList()).plantKey(plantKey).build());
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, oldPlantDetailsList, Collections.singletonList(materialPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(actual.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }

  @Test
  void givenPlantDetailsWithStandardPrices_whenEditingStandardPrice_thenReturnChangeWithStandardPrice() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).standardPrice(BigDecimal.ZERO).build())).build();

    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().standardPrice(BigDecimal.ONE).plantKey(plantKey).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey).build());
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, oldPlantDetailsList, Collections.singletonList(newPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);
    final Change<MaterialPlantDetails> actual = plantDetails.get(0);
    assertThat(actual.getNewValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    assertThat(actual.getNewValue().getPlantKey().getClient()).isEqualTo(plantKey.getClient());
    assertThat(actual.getNewValue().getMaterialPriceDetails()).hasSize(1);
    assertThat(actual.getNewValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ZERO);
    assertThat(actual.getOldValue().getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.ONE);
  }


  @Test
  void givenPlantDetailsWithStandardPrices_whenEditingStandardPriceAndPlant_thenReturnChangeWithRemovalOfOldPlantAddOfNewAndStandardPrice() {
    final PlantKey plantKey1 = new PlantKey(PLANT, CLIENT);
    final PlantKey plantKey2 = new PlantKey(PLANT2, CLIENT);
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey2).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey2).standardPrice(BigDecimal.ZERO).build())).build();

    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().standardPrice(BigDecimal.ONE).plantKey(plantKey1).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey1).build());
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, oldPlantDetailsList, Collections.singletonList(newPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(2);

    final Predicate<Change<MaterialPlantDetails>> isRemovalOfOld = change -> change.getOldValue() != null && change.getOldValue().getPlantKey().getCode().equals(plantKey1.getCode());
    final Predicate<Change<MaterialPlantDetails>> isAdditionOfNew = change -> change.getNewValue() != null && change.getNewValue().getPlantKey().getCode().equals(plantKey2.getCode());

// Check if the removal is present
    assertTrue(plantDetails.stream().anyMatch(isRemovalOfOld));
// Check if the addition is present
    assertTrue(plantDetails.stream().anyMatch(isAdditionOfNew));
  }

  @Test
  void givenPlantDetailsWithStandardPriceAndMoving_whenEditingStandardPriceAndMoving_thenReturnChangeWithOnlyAddOfNewStandardPriceAndMoving() {
    final PlantKey plantKey = new PlantKey(PLANT, CLIENT);
    final BigDecimal ten = BigDecimal.TEN;

    final List<MaterialPriceDetails> initialPriceDetails = Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).build());
    final List<MaterialPlantDetails> oldPlantDetailsList = Collections.singletonList(MaterialPlantDetails.builder().materialPriceDetails(initialPriceDetails).plantKey(plantKey).build());

    final BigDecimal one = BigDecimal.ONE;
    final MaterialPlantDetails newPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey).movingAveragePrice(one).standardPrice(ten).build())).build();
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, EMPTY_MATERIAL_DETAILS, oldPlantDetailsList, Collections.singletonList(newPlantDetails));
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertThat(plantDetails).hasSize(1);

    final Change<MaterialPlantDetails> plantDetailsChange = plantDetails.get(0);
    assertThat(plantDetailsChange.getOldValue().getPlantKey().getCode()).isEqualTo(plantKey.getCode());
    final MaterialPriceDetails materialPriceDetailsOld = plantDetailsChange.getOldValue().getMaterialPriceDetails().get(0);
    final MaterialPriceDetails materialPriceDetailsNew = plantDetailsChange.getNewValue().getMaterialPriceDetails().get(0);
    assertThat(materialPriceDetailsOld.getStandardPrice()).isNull();
    assertThat(materialPriceDetailsNew.getStandardPrice()).isEqualTo(ten);

    assertThat(materialPriceDetailsOld.getMovingAveragePrice()).isNull();
    assertThat(materialPriceDetailsNew.getMovingAveragePrice()).isEqualTo(one);
  }

  @Test
  void givenNullAlternativeUnitOfMeasure_whenAddingTwoUnits_thenReturnChangeWithListOfTwoUnitOfMeasureChangeFromNullToValue() {
    final MaterialDetails newMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final String baseUnit = "L";
    final MaterialAlternativeUnitOfMeasureDetails alt1 = MaterialAlternativeUnitOfMeasureDetails.builder()
        .alternativeUnitOfMeasurement("KG")
        .denominator(BigDecimal.TEN)
        .numerator(BigDecimal.ONE)
        .build();
    final MaterialAlternativeUnitOfMeasureDetails alt2 = MaterialAlternativeUnitOfMeasureDetails.builder()
        .alternativeUnitOfMeasurement("PC")
        .denominator(BigDecimal.ONE)
        .numerator(BigDecimal.TEN)
        .build();
    newMaterialDetails.getUnitsOfMeasure().setBaseUnitOfMeasurement(baseUnit);
    newMaterialDetails.getUnitsOfMeasure().setAlternativeUnitsOfMeasure(List.of(alt1, alt2));
    final Changes changes = ComputeChanges.getChanges(EMPTY_MATERIAL_DETAILS, newMaterialDetails, null, null);
    final List<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitsOfMeasure = changes.getAlternativeUnitsOfMeasure();

    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUnitOfMeasureDetailsChange1 = new Change<>(null, alt1);
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUnitOfMeasureDetailsChange2 = new Change<>(null, alt2);
    assertThat(alternativeUnitsOfMeasure)
        .hasSize(2)
        .containsExactlyInAnyOrder(alternativeUnitOfMeasureDetailsChange1,
            alternativeUnitOfMeasureDetailsChange2
        );
  }


  @Test
  void givenTwoAlternativeUnitOfMeasure_whenRemovingOne_thenReturnChangeWithListOfOneChangeFromValueToNull() {
    final MaterialDetails initalMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final String baseUnit = "L";
    final MaterialAlternativeUnitOfMeasureDetails alt1 = MaterialAlternativeUnitOfMeasureDetails.builder()
        .alternativeUnitOfMeasurement("KG")
        .denominator(BigDecimal.TEN)
        .numerator(BigDecimal.ONE)
        .build();
    final MaterialAlternativeUnitOfMeasureDetails alt2 = MaterialAlternativeUnitOfMeasureDetails.builder()
        .alternativeUnitOfMeasurement("PC")
        .denominator(BigDecimal.ONE)
        .numerator(BigDecimal.TEN)
        .build();

    initalMaterialDetails.getUnitsOfMeasure().setBaseUnitOfMeasurement(baseUnit);
    initalMaterialDetails.getUnitsOfMeasure().setAlternativeUnitsOfMeasure(List.of(alt1, alt2));


    final MaterialDetails newMaterialDetails = MaterialDetails.createEmptyMaterialDetails();

    newMaterialDetails.getUnitsOfMeasure().setBaseUnitOfMeasurement(baseUnit);
    newMaterialDetails.getUnitsOfMeasure().setAlternativeUnitsOfMeasure(List.of(alt1));
    final Changes changes = ComputeChanges.getChanges(initalMaterialDetails, newMaterialDetails, null, null);
    final List<Change<MaterialAlternativeUnitOfMeasureDetails>> alternativeUnitsOfMeasure = changes.getAlternativeUnitsOfMeasure();
    final Change<MaterialAlternativeUnitOfMeasureDetails> alternativeUnitOfMeasureDetailsChange = new Change<>(alt2, null);

    assertThat(alternativeUnitsOfMeasure)
        .hasSize(1)
        .containsExactlyInAnyOrder(alternativeUnitOfMeasureDetailsChange
        );
  }
}
