package com.creactives.tam4.common.utils;

import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.models.ComputeMasterDataChangesRequest;
import com.creactives.tam4.common.tamfields.material.AlternativeUnitOfMeasure;
import com.creactives.tam4.common.tamfields.plant.Plant;
import com.creactives.tam4.common.tamfields.plant.price.MovingAveragePrice;
import com.creactives.tam4.common.tamfields.plant.price.StandardPrice;
import com.creactives.tam4.common.tamfields.plant.storagelocation.Warehouse;
import com.creactives.tam4.common.utils.strategies.AttributeStrategyContainer;
import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.google.common.collect.testing.Helpers.assertContains;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComputeMasterDataChangesUseCaseTest {

  private static final String LT = "LT";
  private static final String KG = "KG";
  private static final String CLIENT = "client";
  private static final String PLANTCODE1 = "PLANTCODE1";
  private static final String PLANTCODE2 = "PLANTCODE2";

  @Mock
  private ApplicationContext applicationContext;

  private ComputeMasterDataChangesForVisibleFieldsAndTechnicalAttributes computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes;

  @BeforeEach
  public void init() {
    computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes = new ComputeMasterDataChangesForVisibleFieldsAndTechnicalAttributes(getAttributeContainer());
  }

  private AttributeStrategyContainer getAttributeContainer() {
    when(applicationContext.getBeanNamesForType(any(Class.class))).thenReturn(new String[]{});
    final AttributeStrategyContainer attributeStrategyContainer = new AttributeStrategyContainer(applicationContext);
    final Plant plant = new Plant();
    final Warehouse warehouse = new Warehouse();
    final StandardPrice standardPrice = new StandardPrice();
    final MovingAveragePrice movingAveragePrice = new MovingAveragePrice();
    final AlternativeUnitOfMeasure alternativeUnitOfMeasure = new AlternativeUnitOfMeasure();
    attributeStrategyContainer.setAttributeStrategies(Map.ofEntries(entry(plant.fieldName(), plant),
        entry(warehouse.fieldName(), warehouse), entry(standardPrice.fieldName(), standardPrice),
        entry(movingAveragePrice.fieldName(), movingAveragePrice), entry(alternativeUnitOfMeasure.fieldName(), alternativeUnitOfMeasure)));
    return attributeStrategyContainer;
  }

  @Test
  void whenAlternativeUnitOfMeasureListChange_thenHasNoChanges() {
    final List<MaterialAlternativeUnitOfMeasureDetails> originalUom = List.of(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement(KG).build(),
        MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement(LT).build());
    final MaterialDetails originalMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    originalMaterialDetails.getUnitsOfMeasure().setAlternativeUnitsOfMeasure(originalUom);
    final MaterialDetails editedMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final List<MaterialAlternativeUnitOfMeasureDetails> editedUom = List.of(MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement(LT).build(),
        MaterialAlternativeUnitOfMeasureDetails.builder().alternativeUnitOfMeasurement(KG).build());
    editedMaterialDetails.getUnitsOfMeasure().setAlternativeUnitsOfMeasure(editedUom);
    final Changes changes = Changes.builder().build();

    computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.computeFieldChange(editedMaterialDetails, originalMaterialDetails,
        null, null, AttributeCodes.ALTERNATIVE_UNIT_OF_MEASURE, changes, new ComputeMasterDataChangesRequest());
    Assert.isNull(changes.getAlternativeUnitsOfMeasure());
  }

  @Test
  void whenChangePlant_thenChangesHasRemoveOfOldAndAddNew() {
    final MaterialDetails originalMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final MaterialDetails editedMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final PlantKey plantKey1 = new PlantKey(PLANTCODE1, CLIENT);
    final MaterialPlantDetails oldMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey1).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey1).build())).build();
    final PlantKey plantKey2 = new PlantKey(PLANTCODE2, CLIENT);
    final MaterialPlantDetails newMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey2).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey2).build())).build();
    final Change<MaterialPlantDetails> removeOfOld = Change.<MaterialPlantDetails>builder().oldValue(oldMaterialPlantDetails).build();
    final Change<MaterialPlantDetails> addOfNew = Change.<MaterialPlantDetails>builder().newValue(newMaterialPlantDetails).build();
    final Changes changes = Changes.builder().build();

    computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.computeFieldChange(editedMaterialDetails, originalMaterialDetails,
        Collections.singletonList(oldMaterialPlantDetails), Collections.singletonList(newMaterialPlantDetails), AttributeCodes.PLANTS, changes, new ComputeMasterDataChangesRequest());
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertEquals(plantDetails.size(), 2);
    assertContains(plantDetails, removeOfOld);
    assertContains(plantDetails, addOfNew);
  }

  @Test
  void whenAddPlant_thenChangesHasJustAddNew() {
    final MaterialDetails originalMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final MaterialDetails editedMaterialDetails = MaterialDetails.createEmptyMaterialDetails();
    final MaterialPlantDetails oldMaterialPlantDetails = null;
    final PlantKey plantKey2 = new PlantKey(PLANTCODE2, CLIENT);
    final MaterialPlantDetails newMaterialPlantDetails = MaterialPlantDetails.builder().plantKey(plantKey2).materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().plantKey(plantKey2).build())).build();
    final Change<MaterialPlantDetails> addOfNew = Change.<MaterialPlantDetails>builder().newValue(newMaterialPlantDetails).build();
    final Changes changes = Changes.builder().build();

    computeMasterDataChangesForVisibleFieldsAndTechnicalAttributes.computeFieldChange(editedMaterialDetails, originalMaterialDetails,
        null, Collections.singletonList(newMaterialPlantDetails), AttributeCodes.PLANTS, changes, new ComputeMasterDataChangesRequest());
    final List<Change<MaterialPlantDetails>> plantDetails = changes.getPlantDetails();
    assertEquals(plantDetails.size(), 1);
    assertContains(plantDetails, addOfNew);
  }

}
