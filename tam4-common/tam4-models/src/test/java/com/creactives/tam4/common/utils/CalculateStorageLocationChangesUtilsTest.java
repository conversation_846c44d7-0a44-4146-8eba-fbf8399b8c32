package com.creactives.tam4.common.utils;

import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;

class CalculateStorageLocationChangesUtilsTest {


  @Test
  void giving3storagesOneWithoutBin_whenConvertedToDocumentBackAndForth_thenListShouldRemainTheSame() {
    final String storage1 = "storage1";
    final String storage2 = "storage2";
    final String storage3 = "storage3";
    final String storageBin1 = "storageBin1";
    final String storageBin2 = "storageBin2";
    final List<MaterialStorageLocationDetails> storageLocations = new ArrayList<>();
    final MaterialStorageLocationDetails materialStorageLocationDetails1 = MaterialStorageLocationDetails.builder()
        .storageBin(storageBin1)
        .storageLocation(storage1)
        .build();
    storageLocations.add(materialStorageLocationDetails1);
    final MaterialStorageLocationDetails materialStorageLocationDetails2 = MaterialStorageLocationDetails.builder()
        .storageLocation(storage2)
        .build();
    storageLocations.add(materialStorageLocationDetails2);
    final MaterialStorageLocationDetails materialStorageLocationDetails3 = MaterialStorageLocationDetails.builder()
        .storageBin(storageBin2)
        .storageLocation(storage3)
        .build();
    storageLocations.add(materialStorageLocationDetails3);
    final String warehouses = CalculateStorageLocationChangesUtils.getStorageLocationsString(storageLocations);
    final String storageBins = CalculateStorageLocationChangesUtils.getStorageBinsString(storageLocations);

    final List<MaterialStorageLocationDetails> actualMaterialStorageLocationDetails = CalculateStorageLocationChangesUtils.getMaterialStorageLocationDetails(warehouses, storageBins);
    assertThat(actualMaterialStorageLocationDetails, containsInAnyOrder(materialStorageLocationDetails1,
        materialStorageLocationDetails2,
        materialStorageLocationDetails3
    ));
  }

  @Test
  void givingEmptyStoragesAndBins_whenConvertedToDocumentBackAndForth_thenListShouldRemainEmptyAndNotNull() {
    final List<MaterialStorageLocationDetails> storageLocations = new ArrayList<>();
    final String warehouses = CalculateStorageLocationChangesUtils.getStorageLocationsString(storageLocations);
    final String storageBins = CalculateStorageLocationChangesUtils.getStorageBinsString(storageLocations);

    final List<MaterialStorageLocationDetails> actualMaterialStorageLocationDetails = CalculateStorageLocationChangesUtils.getMaterialStorageLocationDetails(warehouses, storageBins);
    assertThat(actualMaterialStorageLocationDetails, notNullValue());
    assertThat(actualMaterialStorageLocationDetails, hasSize(0));
  }


  @Test
  void givingStoragesAndNoBins_whenConvertedToDocumentBackAndForth_thenListShouldHaveStoragesWithoutBins() {
    final List<MaterialStorageLocationDetails> storageLocations = new ArrayList<>();
    final String storage1 = "storage1";
    final String storage2 = "storage2";
    final String storage3 = "storage3";
    final MaterialStorageLocationDetails materialStorageLocationDetails1 = MaterialStorageLocationDetails.builder()
        .storageLocation(storage1)
        .build();
    storageLocations.add(materialStorageLocationDetails1);
    final MaterialStorageLocationDetails materialStorageLocationDetails2 = MaterialStorageLocationDetails.builder()
        .storageLocation(storage2)
        .build();
    storageLocations.add(materialStorageLocationDetails2);
    final MaterialStorageLocationDetails materialStorageLocationDetails3 = MaterialStorageLocationDetails.builder()
        .storageLocation(storage3)
        .build();
    storageLocations.add(materialStorageLocationDetails3);
    final String warehouses = CalculateStorageLocationChangesUtils.getStorageLocationsString(storageLocations);
    final String storageBins = CalculateStorageLocationChangesUtils.getStorageBinsString(storageLocations);

    final List<MaterialStorageLocationDetails> actualMaterialStorageLocationDetails = CalculateStorageLocationChangesUtils.getMaterialStorageLocationDetails(warehouses, storageBins);
    assertThat(actualMaterialStorageLocationDetails, notNullValue());
    assertThat(actualMaterialStorageLocationDetails, hasSize(3));
  }

  @Test
  void givingStoragesAndNoBins_whenConvertedToDocument_thenListShouldHaveStoragesWithoutBins() {
    final List<MaterialStorageLocationDetails> storageLocationDetails = CalculateStorageLocationChangesUtils.getMaterialStorageLocationDetails("storage1|storage2", "");
    assertThat(storageLocationDetails, hasSize(2));
  }

}
