package com.creactives.tam4.common.models;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.attributes.Attribute;
import com.creactives.tam4.messaging.attributes.SingleValueAttribute;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class CalculateChangesUtilsTest {

  public static final String EN_LANG = "en";
  public static final String CUSTOMER_FIELD = "UNSPSCReduced";

  @Test
  public void testIfChangesOnlyOnNormalizedAndSkip_givesHasNoChanges() {
    final Changes changesOnlyOnNormalized = Changes.builder()
        .normalizedLongDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnNormalized, true);
    assertThat(changed).isFalse();
  }

  @Test
  public void testIfChangesOnlyOnNormalizedAndNoSkip_givesChanges() {
    final Changes changesOnlyOnNormalized = Changes.builder()
        .normalizedLongDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnNormalized, false);
    assertThat(changed).isTrue();
  }

  @Test
  public void testIfChangesNotOnlyOnNormalizedAndSkip_givesChanges() {
    final Changes changesOnlyOnNormalized = Changes.builder()
        .normalizedLongDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .shortDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnNormalized, true);
    assertThat(changed).isTrue();
  }

  @Test
  public void testIfChangesNotOnlyOnNormalizedAndNoSkip_givesChanges() {
    final Changes changesOnlyOnNormalized = Changes.builder()
        .normalizedLongDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .shortDescriptions(Map.of(EN_LANG, Change.<String>builder().oldValue(null).newValue("added").build()))
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnNormalized, false);
    assertThat(changed).isTrue();
  }

  @Test
  public void testChangesOnlyOnDeletionFlagWith_true_boolean() {
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(Change.<Boolean>builder().oldValue(false).newValue(true).build())
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isTrue();
  }

  @Test
  public void testChangesOnlyOnDeletionFlagWith_false_boolean() {
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(Change.<Boolean>builder().oldValue(false).newValue(false).build())
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isFalse();
  }

  @Test
  public void testChangesOnlyOnDeletionFlagWith_S_String() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "S", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isFalse();
  }

  @Test
  public void testChangesOnlyOnDeletionFlagWith_Y_String() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "Y", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isTrue();
  }

  @Test
  public void testChangesOnlyOnDeletionFlagWith_true_String() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "true", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isTrue();
  }

  @Test
  public void havingDeletionFlagToFalse_whenNewValueIsNString_thenChangesShouldBeNone() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "N", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isFalse();
  }

  @Test
  public void havingDeletionFlagToFalse_whenNewValueIsRandomString_thenChangesShouldBeNone() {
    final String random = RandomStringUtils.random(5, true, false);
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, random, false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isFalse();
  }

  @Test
  public void havingDeletionFlagToFalse_whenNewValueIsYString_thenChangesShouldBeFilled() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "Y", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isTrue();
    assertThat(changesOnlyOnDeletionFlag.getDeletionFlag()).isNotNull();
    assertThat(changesOnlyOnDeletionFlag.getDeletionFlag().getOldValue()).isFalse();
    assertThat(changesOnlyOnDeletionFlag.getDeletionFlag().getNewValue()).isTrue();
  }

  @Test
  public void havingDeletionFlagToFalse_whenNewValueIsFalseString_thenChangesShouldBeNone() {
    final Change<Boolean> deletionFlag = new CalculateChangesUtils(false, "false", false, false).applyChange();
    final Changes changesOnlyOnDeletionFlag = Changes.builder()
        .deletionFlag(deletionFlag)
        .build();
    final boolean changed = CalculateChangesUtils.hasChanges(changesOnlyOnDeletionFlag, false);
    assertThat(changed).isFalse();
  }

  @Test
  public void havingMapWithElements_whenNewMapIsNull_thenChangeShouldContainFromValueToNull() {
    final Map<String, String> oldMap = new HashMap<>();
    final String fieldName = "Area";
    final String fieldValue = "6";
    oldMap.put(fieldName, fieldValue);
    final Map<String, String> newMap = null;
    final Map<String, Change<String>> stringChangeMap = CalculateChangesUtils.calculateMapChange(newMap, oldMap);
    assertThat(stringChangeMap).isNotNull();
    assertThat(stringChangeMap.containsKey(fieldName)).isTrue();
    final Change<String> actualChange = stringChangeMap.get(fieldName);
    assertThat(actualChange).isNotNull();
    assertThat(actualChange.getOldValue()).isEqualTo(fieldValue);
    assertThat(actualChange.getNewValue()).isNull();
  }

  @Test
  public void havingMapWithElements_whenNewMapIsEmpty_thenChangeShouldContainFromValueToNull() {
    final Map<String, String> oldMap = new HashMap<>();
    final String fieldName = "Area";
    final String fieldValue = "6";
    oldMap.put(fieldName, fieldValue);

    final Map<String, String> newMap = new HashMap<>();
    final Map<String, Change<String>> stringChangeMap = CalculateChangesUtils.calculateMapChange(newMap, oldMap);
    assertThat(stringChangeMap).isNotNull();
    assertThat(stringChangeMap.containsKey(fieldName)).isTrue();
    final Change<String> actualChange = stringChangeMap.get(fieldName);
    assertThat(actualChange).isNotNull();
    assertThat(actualChange.getOldValue()).isEqualTo(fieldValue);
    assertThat(actualChange.getNewValue()).isNull();
  }

  @Test
  @DisplayName("Given an empty old change and a new populated change, expecting it return the new one")
  public void testIfTheNewChangeIsAddedCorrectly() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> customerFieldChange = new HashMap<>();
    customerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "121415"));
    final Change<String> materialTypeChange = new Change<>("test", "RHO");
    newChanges.setCustomerFields(customerFieldChange);
    newChanges.setMaterialType(materialTypeChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    Assertions.assertEquals(newChanges.getCustomerFields(), mergedChanges.getCustomerFields());
    Assertions.assertEquals("RHO", mergedChanges.getMaterialType().getNewValue());
  }

  @Test
  @DisplayName("Given two different map representing a field, expecting it return a map with 2 values")
  public void testIfMergingDifferentMapOfFieldReturnsOneMapWithTwoValues() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> newCustomerFieldChange = new HashMap<>();
    newCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "121415"));
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put("customerFieldTest", new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);
    newChanges.setCustomerFields(newCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    Assertions.assertEquals(2, mergedChanges.getCustomerFields().size());
  }

  @Test
  @DisplayName("Given two map representing the same field, expecting it return the old map value")
  public void testIfOldMapIsNotOverwrittenByTheNewMap() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> newCustomerFieldChange = new HashMap<>();
    newCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "121415"));
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);
    newChanges.setCustomerFields(newCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    final Map<String, Change<String>> customerFields = mergedChanges.getCustomerFields();
    Assertions.assertEquals(oldCustomerFieldChange.get(CUSTOMER_FIELD).getNewValue(), customerFields.get(CUSTOMER_FIELD).getNewValue());
  }

  @Test
  @DisplayName("Given two map representing the same field and overwrite to true, expecting it return the new map value")
  public void testIfOldMapIsOverwrittenByTheNewMap() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> newCustomerFieldChange = new HashMap<>();
    newCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "121415"));
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);
    newChanges.setCustomerFields(newCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);
    Assertions.assertEquals("121415", mergedChanges.getCustomerFields().get(CUSTOMER_FIELD).getNewValue());
  }

  @Test
  public void givenOldChangeForCustomerFieldToNullAndNewChangesHaveChangeToAValue_whenMergingWithoutOverride_shouldReturnChangeToNull() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> newCustomerFieldChange = new HashMap<>();
    newCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>("oldValue", null));
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);
    newChanges.setCustomerFields(newCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    Assertions.assertEquals(oldCustomerFieldChange.get(CUSTOMER_FIELD).getNewValue(), mergedChanges.getCustomerFields().get(CUSTOMER_FIELD).getNewValue());
  }

  @Test
  public void givenOldChangeForCustomerFieldToNullAndNewChangesHaveChangeToAValue_whenMergingWithOverride_shouldReturnChangeToValue() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = new Changes();
    final Map<String, Change<String>> newCustomerFieldChange = new HashMap<>();
    newCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>("oldValue", null));
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);
    newChanges.setCustomerFields(newCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);
    final Map<String, Change<String>> customerFields = mergedChanges.getCustomerFields();
    Assertions.assertEquals(newCustomerFieldChange.get(CUSTOMER_FIELD).getNewValue(), customerFields.get(CUSTOMER_FIELD).getNewValue());
  }

  @Test
  public void givenOldChangeForCustomerFieldToNullAndNewChangesIsNull_whenMergingWithoutOverride_shouldReturnOldChange() {
    final Changes oldChanges = new Changes();
    final Changes newChanges = null;
    final Map<String, Change<String>> oldCustomerFieldChange = new HashMap<>();
    oldCustomerFieldChange.put(CUSTOMER_FIELD, new Change<>(null, "test"));
    oldChanges.setCustomerFields(oldCustomerFieldChange);

    final Changes mergedChanges = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    final Map<String, Change<String>> customerFields = mergedChanges.getCustomerFields();
    final Change<String> change = customerFields.get(CUSTOMER_FIELD);
    Assertions.assertEquals(oldCustomerFieldChange.get(CUSTOMER_FIELD).getNewValue(), change.getNewValue());
  }

  @Test
  public void givenOldChangeForTechnicalAttributes_whenNewChangesForTechnicalAttribute_shouldReturnOldChange() {
    final Changes oldChanges = new Changes();

    final List<Change<Attribute>> oldChangesTechAttrChange = new ArrayList<>();
    final String attr1 = "attr1";
    final String oldValue = "oldValue";
    final String newValue = "newValue";
    final Attribute oldFirstAttribute = SingleValueAttribute.builder().code(attr1).value(oldValue).build();
    final Attribute newFirstAttribute = SingleValueAttribute.builder().code(attr1).value(newValue).build();
    oldChangesTechAttrChange.add(new Change<>(oldFirstAttribute, newFirstAttribute));
    oldChanges.setTechnicalAttributes(oldChangesTechAttrChange);

    final Changes newChanges = new Changes();
    final List<Change<Attribute>> newChangesTechAttrChange = new ArrayList<>();
    final String attr2 = "attr2";
    final String oldValue2 = "oldValue2";
    final String newValue2 = "newValue2";
    final Attribute oldSecondAttribute = SingleValueAttribute.builder().code(attr2).value(oldValue2).build();
    final Attribute newSecondAttribute = SingleValueAttribute.builder().code(attr2).value(newValue2).build();
    newChangesTechAttrChange.add(new Change<>(oldSecondAttribute, newSecondAttribute));
    newChanges.setTechnicalAttributes(newChangesTechAttrChange);

    final Changes calculatedChange = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);
    final List<Change<Attribute>> technicalAttributesChanges = calculatedChange.getTechnicalAttributes();
    Assertions.assertEquals(2, technicalAttributesChanges.size());
    final Change<Attribute> firstAttributeChange = technicalAttributesChanges.get(0);
    Assertions.assertEquals(newFirstAttribute, firstAttributeChange.getNewValue());
    final Change<Attribute> secondAttributeChange = technicalAttributesChanges.get(1);
    Assertions.assertEquals(newSecondAttribute, secondAttributeChange.getNewValue());
  }

  @Test
  public void testMergeChanges_givenOldChangesAndNewChanges_whenOverwritingSimpleField_thenOldChangesIsUnexpectedlyModified() {
    final Changes oldChanges = new Changes();
    oldChanges.setMaterialCode(new Change<>("originalOldValue", "originalNewValue"));

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(new Change<>("newOldValue", "newNewValue"));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);

    assertEquals(new Change<>("originalOldValue", "originalNewValue"), oldChanges.getMaterialCode(),
        "Given a simple field, when overwriting, then original oldChanges should not be modified (this test FAILS on original method)");
    assertNotNull(merged);
    assertEquals(new Change<>("newOldValue", "newNewValue"), merged.getMaterialCode(),
        "Given a simple field, when overwriting, then merged changes should have the new value");
  }

  @Test
  public void testMergeChanges_givenOldChangesAndNewChanges_whenOverwritingSimpleField_thenOldChangesIsNotModified() {
    final Changes oldChanges = new Changes();
    oldChanges.setMaterialCode(new Change<>("originalOldValue", "originalNewValue"));

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(new Change<>("newOldValue", "newNewValue"));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);

    assertEquals(new Change<>("originalOldValue", "originalNewValue"), oldChanges.getMaterialCode(),
        "Given a simple field, when overwriting, then original oldChanges should not be modified (this test PASSES on modified method)");
    assertNotNull(merged);
    assertEquals(new Change<>("newOldValue", "newNewValue"), merged.getMaterialCode(),
        "Given a simple field, when overwriting, then merged changes should have the new value");
  }

  @Test
  public void testMergeChanges_givenOldValueAndNewNullChange_whenOverwriting_thenSimpleFieldIsNotCorrectlyNullified() {
    final Changes oldChanges = new Changes();
    oldChanges.setMaterialCode(new Change<>("old", "new"));

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(null);

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);

    assertNotNull(merged);
    assertNull(merged.getMaterialCode(),
        "Given old Change and new NULL Change, when overwriting, then field should be null (this test FAILS without refined fix)");
  }

  @Test
  public void testMergeChangesProposedFixRefined_givenOldValueAndNewNullChange_whenOverwriting_thenSimpleFieldIsCorrectlyNullified() {
    final Changes oldChanges = new Changes();
    oldChanges.setMaterialCode(new Change<>("old", "new"));

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(null);

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, true);

    assertNotNull(merged);
    assertNull(merged.getMaterialCode(),
        "Given old Change and new NULL Change, when overwriting, then field should be null (this test PASSES with refined fix)");
  }

  @Test
  public void testMergeChangesProposedFixRefined_givenOldChangeAndNewDifferentChange_whenNotOverwriting_thenOldChangeIsRetained() {
    final Changes oldChanges = new Changes();
    oldChanges.setMaterialCode(new Change<>("old1", "new1"));

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(new Change<>("old2", "new2"));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);

    assertNotNull(merged);
    assertEquals(new Change<>("old1", "new1"), merged.getMaterialCode(),
        "Given old Change and new different Change, when not overwriting, then old Change should be retained");
  }

  @Test
  public void testMergeChangesProposedFixRefined_givenNullOldChangeAndNewChange_whenNotOverwriting_thenNewChangeIsSet() {
    final Changes oldChanges = new Changes();

    final Changes newChanges = new Changes();
    newChanges.setMaterialCode(new Change<>("old", "new"));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);

    assertNotNull(merged);
    assertEquals(new Change<>("old", "new"), merged.getMaterialCode(),
        "Given null old Change and new Change, when not overwriting, then new Change should be set");
  }

  @Test
  public void testMergeChangesProposedFixRefined_givenOldMapAndNewMap_whenNotOverwritingAndNewKeyExists_thenOldMapKeepsValue() {
    final Changes oldChanges = new Changes();
    oldChanges.setShortDescriptions(new HashMap<>(Map.of(EN_LANG, new Change<>("oldDescOld", "oldDescNew"))));

    final Changes newChanges = new Changes();
    newChanges.setShortDescriptions(new HashMap<>(Map.of(EN_LANG, new Change<>("newDescOld", "newDescNew"))));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);

    assertNotNull(merged);
    assertNotNull(merged.getShortDescriptions());
    assertEquals(1, merged.getShortDescriptions().size());
    assertEquals(new Change<>("oldDescOld", "oldDescNew"), merged.getShortDescriptions().get(EN_LANG),
        "Given a key existing in both maps, when not overwriting, then old map's value should be retained.");
  }

  @Test
  public void testMergeChangesProposedFixRefined_givenOldMapAndNewMap_whenNotOverwritingAndNewKeyDoesNotExistInOld_thenNewKeyIsAdded() {
    final Changes oldChanges = new Changes();
    oldChanges.setShortDescriptions(new HashMap<>(Map.of("fr", new Change<>("oldFrOld", "oldFrNew"))));

    final Changes newChanges = new Changes();
    newChanges.setShortDescriptions(new HashMap<>(Map.of(EN_LANG, new Change<>("newEnOld", "newEnNew"))));

    final Changes merged = CalculateChangesUtils.mergeChanges(oldChanges, newChanges, false);

    assertNotNull(merged);
    assertNotNull(merged.getShortDescriptions());
    assertEquals(2, merged.getShortDescriptions().size());
    assertEquals(new Change<>("oldFrOld", "oldFrNew"), merged.getShortDescriptions().get("fr"));
    assertEquals(new Change<>("newEnOld", "newEnNew"), merged.getShortDescriptions().get(EN_LANG),
        "Given a new key not existing in old map, when not overwriting, then new key should be added.");
  }
}
