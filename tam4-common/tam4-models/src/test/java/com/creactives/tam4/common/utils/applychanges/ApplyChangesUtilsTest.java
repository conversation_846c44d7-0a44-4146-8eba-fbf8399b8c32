package com.creactives.tam4.common.utils.applychanges;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Created on 18/03/2020 17:32
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
class ApplyChangesUtilsTest {

  public static final PlantKey PLANT_KEY = new PlantKey("MIR", "702");

  public static List<MaterialPlantDetails> getPlants(final String plantsJson) {
    //noinspection Convert2Diamond
    return getObject(plantsJson, new TypeReference<List<MaterialPlantDetails>>() {
    });
  }

  @SneakyThrows
  public static <T> T getObject(final String jsonString, final TypeReference<T> typeReference) {
    if (jsonString == null) {
      return null;
    }
    final ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    return objectMapper.readValue(jsonString, typeReference);
  }

  @Test
  void testBigDecimalMapper_1_equals() {
    final double value = 1.0;
    final BigDecimal doubleDecimal = BigDecimal.valueOf(value);
    final BigDecimal integerDecimal = new BigDecimal(1);
    final BigDecimal stringWithDecimal = new BigDecimal("1.0");
    final BigDecimal stringDecimal = new BigDecimal("1");

    final MaterialPlantDetails plantDetailsInteger = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(integerDecimal).build())).build();
    final MaterialPlantDetails plantDetailsDouble = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(doubleDecimal).build())).build();
    final MaterialPlantDetails plantDetailsStringDecimal = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(stringWithDecimal).build())).build();
    final MaterialPlantDetails plantDetailsString = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(stringDecimal).build())).build();

    Assertions.assertThat(Objects.equals(plantDetailsInteger, plantDetailsDouble)).isTrue();
    Assertions.assertThat(Objects.equals(plantDetailsStringDecimal, plantDetailsString)).isTrue();
  }

  @Test
  void testBigDecimalMapper_2_equals() {
    final double value = 1234.045607891;
    final BigDecimal doubleDecimal = BigDecimal.valueOf(value);
    final BigDecimal integerDecimal = new BigDecimal("1234.045607891");
    final BigDecimal stringWithDecimal = new BigDecimal("1234.045607891");

    final MaterialPlantDetails plantDetailsInteger = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(integerDecimal).build())).build();
    final MaterialPlantDetails plantDetailsDouble = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(doubleDecimal).build())).build();
    final MaterialPlantDetails plantDetailsStringDecimal = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(stringWithDecimal).build())).build();

    Assertions.assertThat(Objects.equals(plantDetailsInteger, plantDetailsDouble)).isTrue();
    Assertions.assertThat(Objects.equals(plantDetailsDouble, plantDetailsStringDecimal)).isTrue();
  }

  @Test
  void testBigDecimalMapper_1_not_equals() {
    final double value = 1234.12345;
    final BigDecimal doubleDecimal = BigDecimal.valueOf(value);
    final BigDecimal stringWithDecimal = new BigDecimal("1234.12346");

    final MaterialPlantDetails plantDetailsDouble = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(doubleDecimal).build())).build();
    final MaterialPlantDetails plantDetailsStringDecimal = MaterialPlantDetails.builder()
        .materialPriceDetails(Collections.singletonList(MaterialPriceDetails.builder().movingAveragePrice(stringWithDecimal).build())).build();

    Assertions.assertThat(Objects.equals(plantDetailsStringDecimal, plantDetailsDouble)).isFalse();
  }

  @Test
  void havingFilledAttachments_whenChangesHaveOldValueFilledAndNewNull_thenAttachmentsShouldBecomeEmpty() {
    final MaterialDetails materialDetails = MaterialDetails.createEmptyMaterialDetails();
    final UUID attach = UUID.randomUUID();
    materialDetails.setAttachments(Collections.singletonList(attach));
    final List<Change<UUID>> attachmentsChange = Collections.singletonList(new Change<>(attach, null));
    ApplyChangesUtils.compareAndSetAttachments(attachmentsChange, materialDetails, false);
    Assertions.assertThat(materialDetails.getAttachments()).isEmpty();
  }

  @Test
  void havingStorageLocationsNull_whenApplyPlantChanges_ifLegacyInitializeStorageToEmptyList_thenChangesApplied_CREAC_2038() {
    final MaterialPlantDetails initialPlant = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).storageLocations(null).build();
    final List<MaterialPlantDetails> plants = Collections.singletonList(initialPlant);

    final MaterialPlantDetails plantBeforeChangesCalculatedByLegacy = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).storageLocations(Collections.emptyList()).build();
    final MaterialPlantDetails plantAfterChange = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).profitCenter("13011")
        .storageLocations(Collections.emptyList()).build();
    final Change<MaterialPlantDetails> plantDetailsChange = new Change<>(plantBeforeChangesCalculatedByLegacy, plantAfterChange);
    final Changes draftChanges = Changes.builder()
        .plantDetails(Collections.singletonList(plantDetailsChange)).build();
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    Assertions.assertThat(materialPlantDetails.get(0).getPlantKey().getCode()).isEqualTo("MIR");
    Assertions.assertThat(materialPlantDetails.get(0).getProfitCenter()).isEqualTo("13011");
    Assertions.assertThat(materialPlantDetails.get(0).getStorageLocations()).isEmpty();
  }

  @Test
  void havingStorageLocationsNull_whenapplyPlantChanges_ifLegacyInitializeStorageToNull_thenChangeApplied_CREAC_2038() {
    final MaterialPlantDetails initialPlant = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).storageLocations(null).build();
    final List<MaterialPlantDetails> plants = Collections.singletonList(initialPlant);

    final MaterialPlantDetails plantBeforeChangesCalculatedByLegacy = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).storageLocations(null).build();
    final MaterialPlantDetails plantAfterChange = MaterialPlantDetails.builder()
        .plantKey(PLANT_KEY).profitCenter("13011").storageLocations(null).build();
    final Change<MaterialPlantDetails> plantDetailsChange = new Change<>(plantBeforeChangesCalculatedByLegacy, plantAfterChange);
    final Changes draftChanges = Changes.builder()
        .plantDetails(Collections.singletonList(plantDetailsChange)).build();
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    Assertions.assertThat(materialPlantDetails.get(0).getPlantKey().getCode()).isEqualTo("MIR");
    Assertions.assertThat(materialPlantDetails.get(0).getProfitCenter()).isEqualTo("13011");
  }

  @Test
  void applyPlantChange_ChangeValueOverrideOldValue1() {
    final String plantsJSON = "[{\"plantKey\":{\"code\":\"BE01\",\"client\":\"EU\"},\"deletionFlag\":false,\"minimumSafetyStock\":5.0,\"batchManagementFlag\":false,\"storageLocations\":[{\"storageLocation\":\"3000\"}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"BE01\",\"client\":\"EU\"},\"valuationType\":\"\"}],\"supportsMultipleValuations\":false}]";
    final List<MaterialPlantDetails> plants = getPlants(plantsJSON);
    final String changesJson = "{\"ignoreOldValues\":false,\"alternativeUnitsOfMeasure\":[],\"shortDescriptions\":{\"nl\":{\"newValue\":\"SCREW DIN912 D23|33mmXL55mm 6.8 BRSS CD\"}},\"purchaseOrderDescriptions\":{\"nl\":{\"newValue\":\"SCREWS DIN912 D23|33mmXL55mm 6.8 BRASS CADMIUM PLATING HEXAGON SOCKET CYLINDRICAL HEAD\"}},\"internalNoteDescriptions\":{},\"longDescriptions\":{},\"normalizedShortDescriptions\":{},\"normalizedLongDescriptions\":{},\"technicalAttributes\":[{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Diameter\",\"value\":\"23,33\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Diameter\",\"value\":\"23.33\"}},{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Length\",\"value\":\"55\"},\"newValue\":{\"@type\":\"NumericalAttribute\",\"code\":\"0_Length\",\"value\":\"55\",\"unitOfMeasure\":\"mm\"}}],\"attachments\":[],\"plantDetails\":[{\"newValue\":{\"plantKey\":{\"code\":\"BE02\",\"client\":\"EU\"},\"deletionFlag\":false,\"minimumSafetyStock\":5.0,\"batchManagementFlag\":false,\"storageLocations\":[{\"storageLocation\":\"3000\"}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"BE02\",\"client\":\"EU\"},\"valuationType\":\"\"}],\"supportsMultipleValuations\":false}},{\"oldValue\":{\"plantKey\":{\"code\":\"BE01\",\"client\":\"EU\"},\"deletionFlag\":false,\"minimumSafetyStock\":5.0,\"batchManagementFlag\":false,\"storageLocations\":[{\"storageLocation\":\"3000\"}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"BE01\",\"client\":\"EU\"},\"valuationType\":\"\"}],\"supportsMultipleValuations\":false}}]}";
    final Changes draftChanges = getObject(changesJson, new TypeReference<>() {
    });
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    Assertions.assertThat(materialPlantDetails.get(0).getPlantKey().getCode()).isEqualTo("BE02");
  }

  @Test
  void whenEmptyPlantAndAddingOne_ThanNewPlantShouldBePresent() {
    final String plantsJSON = "[]";
    final List<MaterialPlantDetails> plants = getPlants(plantsJSON);
    final String changesJson = "{\"ignoreOldValues\":false,\"client\":null,\"materialCode\":null,\"deletionFlag\":null,\"materialGroup\":null,\"materialType\":null,\"productDivision\":null,\"baseUnitOfMeasurement\":null,\"purchasingUnitOfMeasurement\":null,\"alternativeUnitsOfMeasure\":null,\"materialStatusValidFromDate\":null,\"industrySector\":null,\"oldMaterialNumber\":null,\"documentNumber\":null,\"basicMaterial\":null,\"laboratoryDesignOffice\":null,\"batchManagementRequirementIndicator\":null,\"authorizationGroup\":null,\"crossPlantMaterialStatus\":null,\"crossPlantPurchasingGroup\":null,\"manufacturerPartNumber\":null,\"manufacturerCode\":null,\"genericItemGroup\":null,\"externalMaterialGroup\":null,\"weightUnitOfMeasurement\":null,\"netWeight\":null,\"grossWeight\":null,\"sizeDimension\":null,\"hazardousMaterialNumber\":null,\"shortDescriptions\":null,\"purchaseOrderDescriptions\":null,\"internalNoteDescriptions\":null,\"inspectionDescriptions\":null,\"longDescriptions\":null,\"normalizedShortDescriptions\":null,\"normalizedLongDescriptions\":null,\"technicalAttributes\":[],\"externalAttributes\":null,\"technicalClassification\":null,\"materialGroupClassification\":null,\"enrichedMaterialGroupClassification\":null,\"completeness\":null,\"attachments\":[],\"image\":null,\"plantDetails\":[{\"oldValue\":null,\"newValue\":{\"plantKey\":{\"code\":\"CN11\",\"client\":\"100_NRP\"},\"countryCode\":null,\"currency\":null,\"businessUnit\":null,\"status\":\"97\",\"leadTimeInDays\":1,\"validFromDate\":null,\"deletionFlag\":false,\"lotSize\":\"2\",\"minLotSize\":null,\"seriable\":null,\"reorderPoint\":null,\"safetyStock\":1,\"minimumSafetyStock\":null,\"maximumStockLevel\":1,\"mrpType\":\"ND\",\"mrpGroup\":null,\"purchasingGroup\":null,\"followUpMaterialCode\":null,\"logisticsHandlingGroup\":null,\"mrpController\":\"902\",\"inHouseProductionTime\":null,\"individualColl\":null,\"goodReceiptProcessingTimeInDays\":null,\"controlKeyForQM\":null,\"certificateType\":null,\"batchManagementFlag\":false,\"schedulingMarginKeyForFloats\":null,\"supportsMultipleValuations\":true,\"intrastatCode\":null,\"controlCodeConsumptionTaxesForeignTrade\":null,\"materialCFOPCategory\":null,\"periodIndicator\":null,\"specialProcurementType\":null,\"checkingGroupAvailabilityCheck\":null,\"profitCenter\":null,\"plantOldMaterialNumber\":null,\"lotSizeForProductCosting\":null,\"storageLocations\":[{\"storageLocation\":\"3000\",\"valuatedUnrestrictedUseStock\":null,\"stockInTransfer\":null,\"stockInQualityInspection\":null,\"blockedStock\":null,\"storageBin\":null,\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"CN11\",\"client\":\"100_NRP\"},\"valuationType\":null,\"standardPrice\":5,\"priceUnit\":54,\"usageMaterial\":null,\"originMaterial\":null,\"priceControlIndicator\":\"V\",\"valuationClass\":\"Z181\",\"valuationCategory\":null,\"movingAveragePrice\":null}]}}],\"mdDomain\":null,\"generic\":null,\"customerFields\":null,\"famiglia\":null,\"sottoFamiglia\":null,\"specificaTecnica\":null,\"edizione\":null,\"revisione\":null,\"dataCustom\":null,\"productHierarchy\":null,\"volume\":null,\"volumeUnit\":null,\"internationalArticleNumberEanUpc\":null,\"serviceValuationClass\":null,\"goldenRecordCode\":null}";
    final Changes draftChanges = getObject(changesJson, new TypeReference<>() {
    });
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    Assertions.assertThat(materialPlantDetails.get(0).getPlantKey().getCode()).isEqualTo("CN11");
  }

  @Test
  void applyPlantChange_NoStorageLocation() {
    final String plantsJSON = "[{\"plantKey\":{\"code\":\"D000\",\"client\":\"500_MRP\"},\"countryCode\":\"MX\",\"currency\":\"MXN\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[{\"storageLocation\":\"1000\",\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"D000\",\"client\":\"500_MRP\"},\"valuationType\":\"\",\"valuationClass\":\"4004\",\"movingAveragePrice\":12345.0}]}]";
    final List<MaterialPlantDetails> plants = getPlants(plantsJSON);
    final String changesJson = "{\"ignoreOldValues\":false,\"plantDetails\":[{\"newValue\":{\"plantKey\":{\"code\":\"0001\",\"client\":\"500_MRP\"},\"countryCode\":\"PE\",\"currency\":\"EUR\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"0001\",\"client\":\"500_MRP\"},\"valuationType\":\"\",\"valuationClass\":\"4004\",\"movingAveragePrice\":11234.0}]}},{\"oldValue\":{\"plantKey\":{\"code\":\"D000\",\"client\":\"500_MRP\"},\"countryCode\":\"MX\",\"currency\":\"MXN\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[{\"storageLocation\":\"1000\",\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"D000\",\"client\":\"500_MRP\"},\"valuationType\":\"\",\"valuationClass\":\"4004\",\"movingAveragePrice\":12345.0}]}}]}";
    final Changes draftChanges = getObject(changesJson, new TypeReference<>() {
    });
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    Assertions.assertThat(materialPlantDetails.get(0).getStorageLocations().size()).isEqualTo(0);
  }

  @Test
  void applyPlantChange_ChangeValueOverrideOldValue2() {
    final String plantsJSON = "[{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"countryCode\":\"ZM\",\"currency\":\"ZMW\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"minimumSafetyStock\":6.0,\"maximumStockLevel\":8.0,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[{\"storageLocation\":\"Zam_F_CK\",\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"valuationType\":\"\"}]}]";
    final List<MaterialPlantDetails> plants = getPlants(plantsJSON);
    final String changesJson = "{\"ignoreOldValues\":false,\"normalizedShortDescriptions\":{\"en\":{\"oldValue\":\"MACH SPEC PART AND ACC REXNO X\",\"newValue\":\"MACH SPEC PART AND ACC STRAP P\"}},\"normalizedLongDescriptions\":{\"en\":{\"oldValue\":\"MACHINE SPECIFIC PARTS & ACCESSORIES REXNORD XXXXX\",\"newValue\":\"MACHINE SPECIFIC PARTS & ACCESSORIES STRAPEX ********* STAINLESS STEEL\"}},\"technicalAttributes\":[{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_CatalogueCodeOEM\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_CatalogueCodeOEM\",\"value\":\"*********\"}},{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"0_MachinePartsMaterial\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"0_MachinePartsMaterial\",\"value\":\"Stainless Steel\"}},{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_CatalogueCodeOMM\",\"value\":\"XXXXX\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_CatalogueCodeOMM\",\"value\":\"\"}},{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_OriginalEquipmentManufacturerOEM\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_OriginalEquipmentManufacturerOEM\",\"value\":\"STRAP\"}},{\"oldValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_OriginalMaterialManufacturerOMM\",\"value\":\"REXNO\"},\"newValue\":{\"@type\":\"SingleValueAttribute\",\"code\":\"4_TAM_OriginalMaterialManufacturerOMM\",\"value\":\"\"}}],\"plantDetails\":[{\"oldValue\":{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"countryCode\":\"ZM\",\"currency\":\"ZMW\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"minimumSafetyStock\":6.0,\"maximumStockLevel\":8.0,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[{\"storageLocation\":\"Zam_F_CK\",\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"valuationType\":\"\"}]},\"newValue\":{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"countryCode\":\"ZM\",\"currency\":\"ZMW\",\"businessUnit\":\"BU\",\"deletionFlag\":false,\"minimumSafetyStock\":0.0,\"maximumStockLevel\":0.0,\"batchManagementFlag\":false,\"supportsMultipleValuations\":false,\"storageLocations\":[{\"storageLocation\":\"Zam_F_CK\",\"deletionFlag\":false}],\"materialPriceDetails\":[{\"plantKey\":{\"code\":\"F\",\"client\":\"GA\"},\"valuationType\":\"\",\"movingAveragePrice\":13431.93}]}}]}";
    final Changes draftChanges = getObject(changesJson, new TypeReference<>() {
    });
    final List<MaterialPlantDetails> materialPlantDetails = ApplyChangesUtils.getNewMaterialPlantDetailsWithChanges(draftChanges, plants);
    Assertions.assertThat(materialPlantDetails.size()).isEqualTo(1);
    final MaterialPlantDetails details = materialPlantDetails.get(0);
    Assertions.assertThat(details.getPlantKey().getCode()).isEqualTo("F");

    Assertions.assertThat(details.getMaximumStockLevel().doubleValue()).isEqualTo(0.0);
    Assertions.assertThat(details.getMinimumSafetyStock().doubleValue()).isEqualTo(0.0);
    final List<MaterialPriceDetails> materialPriceDetails = details.getMaterialPriceDetails();
    Assertions.assertThat(materialPriceDetails.size()).isEqualTo(1);
    final MaterialPriceDetails materialPriceDetail = materialPriceDetails.get(0);
    Assertions.assertThat(materialPriceDetail.getMovingAveragePrice().doubleValue()).isEqualTo(13431.93);
  }

}
