package com.creactives.tam4.common.messaging.massiveoperation;

import static com.creactives.tam4.common.constants.MassiveRelationshipConstants.MAX_ROW_EXCEL_SHEET;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ExcelSheetManagerTest {

  public static final String FAKE_SHEET_NAME = "FAKE-SHEET";
  public static final String FAKE_SHEET_NAME_SECOND = "FAKE-SHEET-SECOND";
  public static final String FAKE_SHEET_NAME_OVER_31_CHARACTERS = "FAKE_SHEET_NAME_OVER_31_CHARACTERS";
  SXSSFWorkbook workbook;

  @BeforeEach
  void init() {
    workbook = new SXSSFWorkbook(1000);
  }

  @Test
  @DisplayName("Requesting a not existing sheet by name, a new sheet with requested name is provided")
  void getSheet_requestingNotExistingSheet_returnNewSheet() {
    assertEquals(0, workbook.getNumberOfSheets());
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
  }

  @Test
  @DisplayName("Requesting a existing sheet by name, a sheet with requested name is provided")
  void getSheet_requestingExistingSheet_returnSheet() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
    final Sheet existingSheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());

    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
    assertEquals(existingSheet, sheet);
  }

  @Test
  @DisplayName("Requesting current row for current new sheet, a zero is provided")
  void getCurrRow_requestingCurrentRowForCurrentNewSheet_returnZero() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
    assertEquals(0, excelSheetManager.getCurrRow());
  }

  @Test
  @DisplayName("Increasing current row for current new sheet, a one is provided on requesting current row")
  void increaseCurrRow_increasingCurrentRowForCurrentNewSheet_increaseCurrentRowNumber() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
    assertEquals(0, excelSheetManager.getCurrRow());
    excelSheetManager.increaseCurrRow();
    assertEquals(1, excelSheetManager.getCurrRow());
  }

  @Test
  @DisplayName("Increasing current row for current sheet with one row, a two is provided on requesting current row")
  void increaseCurrRow_increasingCurrentRowForCurrentSheet_returnIncreasedCurrentRowNumber() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
    assertEquals(0, excelSheetManager.getCurrRow());
    excelSheetManager.increaseCurrRow();
    assertEquals(1, excelSheetManager.getCurrRow());

    excelSheetManager.increaseCurrRow();
    assertEquals(2, excelSheetManager.getCurrRow());
  }

  @Test
  @DisplayName("Changing current sheet with getSheet, the current row for the current sheet is provided")
  void getCurrRow_changingCurrentSheet_returnCurrentRowNumberForCurrentSheet() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);

    Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    excelSheetManager.increaseCurrRow();
    excelSheetManager.increaseCurrRow();

    Sheet sheetSecond = excelSheetManager.getSheet(FAKE_SHEET_NAME_SECOND, true);
    excelSheetManager.increaseCurrRow();
    excelSheetManager.increaseCurrRow();
    excelSheetManager.increaseCurrRow();

    assertEquals(2, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME, workbook.getSheetAt(0).getSheetName());
    assertEquals(FAKE_SHEET_NAME_SECOND, workbook.getSheetAt(1).getSheetName());

    sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertEquals(2, excelSheetManager.getCurrRow());

    sheetSecond = excelSheetManager.getSheet(FAKE_SHEET_NAME_SECOND, true);
    assertEquals(3, excelSheetManager.getCurrRow());
  }


  @Test
  @DisplayName("Increasing row over the limit of the sheet, a new sheet is created with index at the end of the name")
  void increaseCurrRow_exceedRowNumberForSheet_aNewSheetIsCreated() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);

    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    for (int i = 0; i < MAX_ROW_EXCEL_SHEET; ++i) {
      excelSheetManager.increaseCurrRow();
    }

    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(MAX_ROW_EXCEL_SHEET, excelSheetManager.getCurrRow());

    excelSheetManager.increaseCurrRow();
    assertEquals(2, workbook.getNumberOfSheets());

    assertEquals(FAKE_SHEET_NAME + " 1", workbook.getSheetAt(1).getSheetName());

  }

  @Test
  @DisplayName("Increasing row over the limit of the sheet, a new sheet is created with index at the end of the name max length 31")
  void increaseCurrRow_exceedRowNumberForSheet_aNewSheetIsCreatedWithNameMaxLength31() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);

    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME_OVER_31_CHARACTERS, true);
    for (int i = 0; i < MAX_ROW_EXCEL_SHEET; ++i) {
      excelSheetManager.increaseCurrRow();
    }
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(MAX_ROW_EXCEL_SHEET, excelSheetManager.getCurrRow());

    excelSheetManager.increaseCurrRow();
    assertEquals(2, workbook.getNumberOfSheets());

    final int maxLength = 31 - 2;
    final int length = Math.min(maxLength, FAKE_SHEET_NAME_OVER_31_CHARACTERS.length());
    final String shortenName = FAKE_SHEET_NAME_OVER_31_CHARACTERS.substring(0, length);
    final String newSheetName = shortenName + StringUtils.SPACE + 1;

    assertEquals(newSheetName, workbook.getSheetAt(1).getSheetName());
  }

  @Test
  @DisplayName("Increasing row over the limit of the sheet, if first row of original sheet is not empty a copy is created in the new sheet")
  void increaseCurrRow_exceedRowNumberForSheet_aNewSheetCanHaveAHeaderCopied() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);

    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);

    final Row row = sheet.createRow(0);
    for (int h = 0; h < 10; ++h) {
      final Cell cell = row.createCell(h);
      cell.setCellValue(h);
    }

    for (int i = 0; i < MAX_ROW_EXCEL_SHEET; ++i) {
      excelSheetManager.increaseCurrRow();
    }
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(MAX_ROW_EXCEL_SHEET, excelSheetManager.getCurrRow());

    excelSheetManager.increaseCurrRow();
    assertEquals(2, workbook.getNumberOfSheets());

    assertEquals(FAKE_SHEET_NAME + " 1", workbook.getSheetAt(1).getSheetName());
    for (int h = 0; h < 10; ++h) {
      assertEquals(workbook.getSheetAt(0).getRow(0).getCell(h).getNumericCellValue(),
          workbook.getSheetAt(1).getRow(0).getCell(h).getNumericCellValue()
      );
    }

  }

  @Test
  @DisplayName("Requesting a sheet with group on sheet, the latest sheet of the group is provided")
  void getSheet_requestingASheetWithAVirtualGroupAssociated_returnTheLatestSheetCreated() {
    final ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);

    final Sheet sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    for (int i = 0; i < MAX_ROW_EXCEL_SHEET; ++i) {
      excelSheetManager.increaseCurrRow();
    }
    assertEquals(1, workbook.getNumberOfSheets());
    assertEquals(MAX_ROW_EXCEL_SHEET, excelSheetManager.getCurrRow());

    excelSheetManager.increaseCurrRow();
    assertEquals(2, workbook.getNumberOfSheets());
    assertEquals(FAKE_SHEET_NAME + " 1", workbook.getSheetAt(1).getSheetName());

    final Sheet sheetLatest = excelSheetManager.getSheet(FAKE_SHEET_NAME, true);
    assertNotEquals(sheetLatest, sheet);
    assertEquals(sheetLatest.getSheetName(), workbook.getSheetAt(1).getSheetName());
  }

//    ---------------------------------------------------------------------------------------------------------------------------------------
//    @Test
//    @DisplayName("")
//    // -Djdk.attach.allowAttachSelf=true
//    void xxxx(){
//        List<VirtualMachineDescriptor> list = VirtualMachine.list();
//        String vmId="";
//        for (VirtualMachineDescriptor item : list){
//            if (StringUtils.startsWith(item.displayName(), "com.intellij.rt.junit.JUnitStarter")){
//                System.out.println("### VM : '" + item.id() + "' - '" + item.displayName() + "'");
//                vmId = item.id();
//            }
//            System.out.println("VM : '" + item.id() + "' - '" + item.displayName() + "'");
//        }
//        System.out.println("############## PRIMA ################");
//        printStats(vmId);
//        System.out.println("############## FINE - PRIMA ################");
//
//        ExcelSheetManager excelSheetManager = new ExcelSheetManager(workbook);
//
//
//        Sheet sheet;
//        for (int indexSheet=0; indexSheet<=250; ++indexSheet ){
//            System.out.println("Sheet : " + indexSheet);
//            sheet = excelSheetManager.getSheet(FAKE_SHEET_NAME + indexSheet);
//            for(int indexRow=0; indexRow<900; ++indexRow) {
//                // Cols max 16.000
//                if (indexRow % 100 == 0){
//                    System.out.println("Row : " + indexRow);
//                }
//                final Row row = sheet.createRow(excelSheetManager.getCurrRow());
//                for (int indexCol=0; indexCol<=16000; ++indexCol){
//                    final Cell cell = row.createCell(indexCol);
//                    cell.setCellType(CellType.STRING);
//                    cell.setCellValue(UUID.randomUUID().toString());
//                }
//                excelSheetManager.increaseCurrRow();
//            }
//            Row row = sheet.createRow(indexSheet);
//        }
//
//        System.out.println("@@@@@@@@@@@@@@@@@@@@@ DOPO @@@@@@@@@@@@@@@@@@@@@");
//        printStats(vmId);
//        System.out.println("@@@@@@@@@@@@@@@@@@@@@ FINE - DOPO @@@@@@@@@@@@@@@");
//
//        assertEquals(1, workbook.getNumberOfSheets());
//        assertEquals(MAX_ROW_EXCEL_SHEET, excelSheetManager.getCurrRow());
//
//        excelSheetManager.increaseCurrRow();
//        assertEquals(2, workbook.getNumberOfSheets());
//
//        assertEquals(FAKE_SHEET_NAME + " 1", workbook.getSheetAt(1).getSheetName());
//
//    }
////    ---------------------------------------------------------------------------------------------------------------------------------------
//
//    @SneakyThrows
//    private static boolean printStats(String id)
//    {
//        try
//        {
//            VirtualMachine vm=VirtualMachine.attach(id);
//            System.out.println("Connected to "+vm.id());
////            System.out.println("System Properties:");
////            for(Map.Entry<?,?> en:vm.getSystemProperties().entrySet())
////                System.out.println("\t"+en.getKey()+" = "+en.getValue());
////            System.out.println();
//            try
//            {
//                MBeanServerConnection sc=connect(vm);
//                MemoryMXBean memoryMXBean =
//                        newPlatformMXBeanProxy(sc, MEMORY_MXBEAN_NAME, MemoryMXBean.class);
//                getRamInfoHtml(memoryMXBean);
//            } catch(IOException ex)
//            {
//                System.out.println("JMX: "+ex);
//            }
//            vm.detach();
//            return true;
//        } catch(AttachNotSupportedException | IOException ex)
//        {
//            ex.printStackTrace();
//        }
//        return false;
//    }
//    // requires Java 8, alternative below the code
//    static MBeanServerConnection connect(VirtualMachine vm) throws IOException
//    {
//        String connectorAddress = vm.startLocalManagementAgent();
//        JMXConnector c= JMXConnectorFactory.connect(new JMXServiceURL(connectorAddress));
//        return c.getMBeanServerConnection();
//    }
//
//    static void getRamInfoHtml(MemoryMXBean memoryMXBean)
//    {
//        System.out.print("Heap:\t");
//        MemoryUsage mu=memoryMXBean.getHeapMemoryUsage();
//        System.out.println(//                "allocated "+mu.getCommitted()+", used "+mu.getUsed()+", max "+mu.getMax());
//        System.out.print("Non-Heap:\t");
//        mu=memoryMXBean.getNonHeapMemoryUsage();
//        System.out.println(//                "allocated "+mu.getCommitted()+", used "+mu.getUsed()+", max "+mu.getMax());
//        System.out.println(//                "Pending Finalizations: "+memoryMXBean.getObjectPendingFinalizationCount());
//    }

}
