package com.creactives.tam4.common.utils.applychanges;


import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.attributes.Attribute;
import com.creactives.tam4.messaging.attributes.SingleValueAttribute;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materialdetails.TechnicalAttributes;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class ApplyAttributesChangesTest {

  @Test
  void applychange_technicalAttributes_applyNullIfCurrentIsNull_falseIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", null, null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", null, null), new SingleValueAttribute("0_ConveyorMaterial", null, null)));

    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, false);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo(null);

  }

  @Test
  void applychange_technicalAttributes_applyNullIfCurrentIsNull_trueIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", null, null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", null, null), new SingleValueAttribute("0_ConveyorMaterial", null, null)));

    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, true);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo(null);

  }

  @Test
  void applychange_technicalAttributes_applyNullIfCurrentIsSomething_falseIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", "sth", null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", "sth", null), new SingleValueAttribute("0_ConveyorMaterial", null, null)));

    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, true);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo(null);

  }

  @Test
  void applychange_technicalAttributes_applySomething2IfCurrentIsSomething1_falseIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", "something1", null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", "something1", null), new SingleValueAttribute("0_ConveyorMaterial", "something2", null)));

    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, false);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo("something2");

  }

  @Test
  @DisplayName("This case should not happen")
  void applychange_technicalAttributes_applySomething2IfCurrentIsSomething1_trueIgnoreOldValues() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", "something1", null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", "something1", null), new SingleValueAttribute("0_ConveyorMaterial", "something2", null)));

    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, true);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo("something2");

  }


  @Test
  void applychange_technicalAttributes_RemoveExistingAttribute_emptyList() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_PressureRange", "11", null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    final Change<Attribute> removeAttributeChange = new Change<>(new SingleValueAttribute("0_PressureRange", "11", null), null);
    technicalAttributesChanges.add(removeAttributeChange);
    ApplyAttributesChanges.applyTechnicalAttributesChanges(technicalAttributesChanges, materialDetails, false);
    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();

    Assertions.assertThat(newTechnicalAttributes).doesNotContainNull();
    Assertions.assertThat(newTechnicalAttributes).isEmpty();
  }

}
