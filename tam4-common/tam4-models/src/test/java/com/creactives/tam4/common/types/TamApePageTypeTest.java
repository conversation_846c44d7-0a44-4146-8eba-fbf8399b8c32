package com.creactives.tam4.common.types;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class TamApePageTypeTest {


  @Test
  void pageGrExistWithUpperCase() {
    final String page = "GREditApproval";
    assertThat(TamApePageType.isGrPage(page)).isTrue();
  }

  @Test
  void pageGrExistWithMixedCase() {
    final String page = "GREditAPProval";
    assertThat(TamApePageType.isGrPage(page)).isTrue();
  }

  @Test
  void pageDoesNotExist() {
    final String page = "pageNotExist";
    assertThat(TamApePageType.isGrPage(page)).isFalse();
  }

}
