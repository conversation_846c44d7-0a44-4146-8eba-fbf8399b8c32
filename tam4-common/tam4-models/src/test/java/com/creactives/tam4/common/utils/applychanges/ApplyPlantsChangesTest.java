package com.creactives.tam4.common.utils.applychanges;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.OperationType;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantChanges;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialPriceChanges;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationChanges;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class ApplyPlantsChangesTest {

  @Test
  public void testChangeIsAppliedForSimpleField() {

    final MaterialPlantDetails plantDetails = MaterialPlantDetails.builder().certificateType("AAA").build();

    final MaterialPlantChanges changes = MaterialPlantChanges.builder().certificateType(Change.<String>builder().oldValue("AAA").newValue("BBB").build()).build();

    final MaterialPlantDetails result = ApplyPlantsChanges.applyPlantChanges(changes, plantDetails);

    Assertions.assertThat(result.getCertificateType()).isEqualTo("BBB");

  }

  @Test
  public void testChangeIsAppliedForStorageLocations() {
    final MaterialPlantDetails plantDetails = MaterialPlantDetails.builder()
        .storageLocations(Arrays.asList(MaterialStorageLocationDetails.builder().storageLocation("A").build(), MaterialStorageLocationDetails.builder().storageLocation("B").storageBin("AAA").build())).build();

    final MaterialPlantChanges changes = MaterialPlantChanges.builder().storageLocations(Arrays.asList(MaterialStorageLocationChanges.builder().operationType(OperationType.ADD).storageLocation("C").build(),
        MaterialStorageLocationChanges.builder().operationType(OperationType.UPDATE).storageLocation("B")
            .storageBin(Change.<String>builder().oldValue("AAA").newValue("BBB").build()).build(),
        MaterialStorageLocationChanges.builder().operationType(OperationType.REMOVE).storageLocation("A").build()
    )).certificateType(Change.<String>builder().oldValue("AAA").newValue("BBB").build()).build();

    final MaterialPlantDetails result = ApplyPlantsChanges.applyPlantChanges(changes, plantDetails);

    assertThat(result.getStorageLocations().size()).isEqualTo(2);
    assertThat(result.getStorageLocations().get(0).getStorageLocation()).isEqualTo("B");
    assertThat(result.getStorageLocations().get(0).getStorageBin()).isEqualTo("BBB");
    assertThat(result.getStorageLocations().get(1).getStorageLocation()).isEqualTo("C");
  }

  @Test
  public void testChangeIsAppliedForMaterialPriceDetails() {
    final MaterialPlantDetails plantDetails = MaterialPlantDetails.builder()
        .materialPriceDetails(Arrays.asList(MaterialPriceDetails.builder().valuationType("A").build(), MaterialPriceDetails.builder().valuationType("B").standardPrice(BigDecimal.valueOf(10)).build())).build();

    final MaterialPlantChanges changes = MaterialPlantChanges.builder().ignoreOldValues(true).materialPriceChanges(Arrays.asList(MaterialPriceChanges.builder().operationType(OperationType.ADD).valuationType("C").build(),
        MaterialPriceChanges.builder().operationType(OperationType.UPDATE).valuationType("B")
            .standardPrice(Change.<BigDecimal>builder().oldValue(BigDecimal.valueOf(10)).newValue(BigDecimal.valueOf(20)).build())
            .build(), MaterialPriceChanges.builder().operationType(OperationType.REMOVE).valuationType("A").build()
    )).certificateType(Change.<String>builder().oldValue("AAA").newValue("BBB").build()).build();

    final MaterialPlantDetails result = ApplyPlantsChanges.applyPlantChanges(changes, plantDetails);

    assertThat(result.getMaterialPriceDetails().size()).isEqualTo(2);
    assertThat(result.getCertificateType()).isEqualTo("BBB");
    assertThat(result.getMaterialPriceDetails().get(0).getValuationType()).isEqualTo("B");
    assertThat(result.getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.valueOf(20));
    assertThat(result.getMaterialPriceDetails().get(1).getValuationType()).isEqualTo("C");
  }


  @Test
  public void testChangeIsAppliedForMaterialPriceDetailsIgnoringPlantKeyWithUpdate() {
    final PlantKey plantKey = new PlantKey("code", "client");
    final MaterialPlantDetails plantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(List.of(MaterialPriceDetails.builder().plantKey(plantKey).valuationType("").standardPrice(BigDecimal.valueOf(10)).build())).build();

    final MaterialPlantChanges changes = MaterialPlantChanges.builder().ignoreOldValues(true)
        .materialPriceChanges(List.of(MaterialPriceChanges.builder().operationType(OperationType.UPDATE).valuationType("").standardPrice(Change.<BigDecimal>builder().oldValue(BigDecimal.valueOf(10)).newValue(BigDecimal.valueOf(20)).build()).build()))
        .build();

    final MaterialPlantDetails result = ApplyPlantsChanges.applyPlantChanges(changes, plantDetails);

    assertThat(result.getMaterialPriceDetails().size()).isEqualTo(1);
    assertThat(result.getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.valueOf(20));
  }

  @Test
  public void testChangeIsAppliedForMaterialPriceDetailsIgnoringPlantKeyWithAdd() {
    final PlantKey plantKey = new PlantKey("code", "client");
    final MaterialPlantDetails plantDetails = MaterialPlantDetails.builder().plantKey(plantKey).materialPriceDetails(Collections.emptyList()).build();

    final MaterialPlantChanges changes = MaterialPlantChanges.builder().ignoreOldValues(true)
        .materialPriceChanges(List.of(MaterialPriceChanges.builder().operationType(OperationType.ADD).valuationType("").standardPrice(Change.<BigDecimal>builder().newValue(BigDecimal.valueOf(20)).build()).build()))
        .build();

    final MaterialPlantDetails result = ApplyPlantsChanges.applyPlantChanges(changes, plantDetails);

    assertThat(result.getMaterialPriceDetails().size()).isEqualTo(1);
    assertThat(result.getMaterialPriceDetails().get(0).getStandardPrice()).isEqualTo(BigDecimal.valueOf(20));
  }
}
