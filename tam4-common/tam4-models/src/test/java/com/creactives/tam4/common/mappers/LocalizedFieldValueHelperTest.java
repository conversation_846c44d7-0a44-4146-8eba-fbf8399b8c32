package com.creactives.tam4.common.mappers;

import com.creactives.tam4.common.dataproviders.rest.response.LocalizedFieldValue;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class LocalizedFieldValueHelperTest {

  @Test
  public void testFilterLocalizedFieldValues_whenLimit0thenReturnEmpty() {
    final List<LocalizedFieldValue> result = LocalizedFieldValueHelper.filterLocalizedFieldValues("queryText",
        List.of(new LocalizedFieldValue("key", "text")), 0);
    Assert.assertEquals(List.of(), result);
  }

  @Test
  public void testFilterLocalizedFieldValues_whenLimitNot0AndQueryTextIsCorrectThenReturnFilteredList() {
    final List<LocalizedFieldValue> result = LocalizedFieldValueHelper.filterLocalizedFieldValues("text",
        List.of(new LocalizedFieldValue("key", "text")), 1);
    Assert.assertEquals(List.of(new LocalizedFieldValue("key", "text")), result);
  }

  @Test
  public void testFilterLocalizedFieldValues_whenLimitNot0ButQueryTextDoesNotMatchThenReturnEmpty() {
    final List<LocalizedFieldValue> result = LocalizedFieldValueHelper.filterLocalizedFieldValues("queryText",
        List.of(new LocalizedFieldValue("key", "text")), 1);
    Assert.assertEquals(List.of(), result);
  }
}
