package com.creactives.tam4.common.utils.applychanges;


import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class ApplyAttachmentsChangesTest {

  @Test
  void applychange_attachments_removeExistingAttachment_excludeAttachFromList() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final UUID initialAttachment = UUID.randomUUID();
    materialDetails.setAttachments(Collections.singletonList(initialAttachment));

    final List<Change<UUID>> attachmentChanges = new ArrayList<>();
    final Change<UUID> removeAttachment = new Change<>(initialAttachment, null);
    attachmentChanges.add(removeAttachment);

    ApplyAttachmentsChanges.applyChangesForAttachments(attachmentChanges, materialDetails, false);

    final List<UUID> newAttachments = materialDetails.getAttachments();

    Assertions.assertThat(newAttachments).doesNotContainNull();
    Assertions.assertThat(newAttachments).isEmpty();
  }

}
