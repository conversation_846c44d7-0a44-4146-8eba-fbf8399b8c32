package com.creactives.tam4.common.utils.applychanges;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.Completeness;
import com.creactives.tam4.messaging.attributes.Attribute;
import com.creactives.tam4.messaging.attributes.SingleValueAttribute;
import com.creactives.tam4.messaging.materialdetails.Dimensions;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materialdetails.MaterialKey;
import com.creactives.tam4.messaging.materialdetails.TechnicalAttributes;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public class ApplyChangeTest {

  @Test
  void applyChange_materialOldValueMatchesChangeOldValue() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", "old"));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("old");
    stringChange.setNewValue("new");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);
    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isEqualTo("new");

  }

  @Test
  void applyChange_technicalAttributes() {
    final MaterialDetails materialDetails = new MaterialDetails();
    final List<Attribute> originalTechnicalAttributes = new ArrayList<>();
    originalTechnicalAttributes.add(new SingleValueAttribute("0_ConveyorMaterial", null, null));
    originalTechnicalAttributes.add(new SingleValueAttribute("0_Diameter", null, null));
    originalTechnicalAttributes.add(new SingleValueAttribute("0_Length", null, null));
    //    originalTechnicalAttributes.add(new SingleValueAttribute("4_TAM_IsAnOEM", null));
    //    originalTechnicalAttributes.add(new SingleValueAttribute("4_TAM_CatalogueCodeOEM", null));
    materialDetails.setTechnicalAttributes(TechnicalAttributes.builder()
        .technicalAttributes(originalTechnicalAttributes)
        .build());

    final List<Change<Attribute>> technicalAttributesChanges = new ArrayList<>();
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_ConveyorMaterial", null, null), new SingleValueAttribute("0_ConveyorMaterial", "34", null)));
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_Diameter", null, null), new SingleValueAttribute("0_Diameter", "22", null)));
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("0_Length", null, null), new SingleValueAttribute("0_Length", "3", null)));
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("4_TAM_IsAnOEM", null, null), new SingleValueAttribute("4_TAM_IsAnOEM", "N", null)));
    technicalAttributesChanges.add(new Change<>(new SingleValueAttribute("4_TAM_CatalogueCodeOEM", null, null), new SingleValueAttribute("4_TAM_CatalogueCodeOEM", "DOMIN", null)));

    ApplyChangesUtils.compareAndSetTechnicalAttributes(technicalAttributesChanges, materialDetails, false);

    final List<Attribute> newTechnicalAttributes = materialDetails.getTechnicalAttributes().getTechnicalAttributes();
    Assertions.assertThat(newTechnicalAttributes.size()).isEqualTo(technicalAttributesChanges.size());

    final Optional<Attribute> conveyAttr = newTechnicalAttributes.stream().filter(it -> "0_ConveyorMaterial".equals(it.getCode())).findAny();
    Assertions.assertThat(conveyAttr.isPresent()).isTrue();
    final Attribute convey = conveyAttr.get();
    Assertions.assertThat(convey).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute conveyS = (SingleValueAttribute) convey;
    Assertions.assertThat(conveyS.getValue()).isEqualTo("34");

    final Optional<Attribute> oemAttr = newTechnicalAttributes.stream().filter(it -> "4_TAM_CatalogueCodeOEM".equals(it.getCode())).findAny();
    Assertions.assertThat(oemAttr.isPresent()).isTrue();
    final Attribute oem = oemAttr.get();
    Assertions.assertThat(oem).isInstanceOf(SingleValueAttribute.class);
    final SingleValueAttribute oemS = (SingleValueAttribute) oem;
    Assertions.assertThat(oemS.getValue()).isEqualTo("DOMIN");
  }

  @Test
  void applyChange_materialOldValueDoesNotMatchChangeOldValue() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", "materialOld"));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("changeOld");
    stringChange.setNewValue("new");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isEqualTo("materialOld");

  }

  @Test
  void applyChange_emptyStringIsConsideredNull() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", null));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("");
    stringChange.setNewValue("new");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isEqualTo("new");

  }

  @Test
  void applyChange_originalCharacteristics() {
    MaterialDetails materialDetails = new MaterialDetails();

    materialDetails = ApplyChangesUtils.getNewMaterialDetailsWithChanges(Changes.builder().externalAttributes(createSingletonMapOf("c1", Change.<String>builder().newValue("v1").build())
    ).build(), materialDetails);

    Assertions.assertThat(materialDetails.getExternalAttributes()).isEqualTo(Map.of("c1", "v1"));

    materialDetails = ApplyChangesUtils.getNewMaterialDetailsWithChanges(Changes.builder().externalAttributes(createSingletonMapOf("c1", Change.<String>builder().oldValue("v1").newValue("v2").build())
    ).build(), materialDetails);

    Assertions.assertThat(materialDetails.getExternalAttributes()).isEqualTo(Map.of("c1", "v2"));
  }

  @Test
  void givenCompletenessPoor_whenChangedFromPoorToFair_thenApplyChangeShouldReturnFair() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setCompleteness(Completeness.POOR);
    final Changes changes = Changes.builder()
        .completeness(new Change<>(Completeness.POOR, Completeness.FAIR)).build();
    final MaterialDetails materialDetailsWithChanges = ApplyChangesUtils.getNewMaterialDetailsWithChanges(changes, materialDetails);

    Assertions.assertThat(materialDetailsWithChanges.getCompleteness()).isEqualTo(Completeness.FAIR);
  }

  private Map<String, Change<String>> createSingletonMapOf(final String attributeName, final Change<String> change) {
    final Map<String, Change<String>> result = new HashMap<>();
    result.put(attributeName, change);
    return result;
  }

  @Test
  void applyChange_emptyListIsConsideredNull() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setCountries(null);
    final Change<List<String>> listChange = new Change<>();
    listChange.setOldValue(Collections.emptyList());
    listChange.setNewValue(List.of("new"));

    final ApplyChange<List<String>> changeApplier = new ApplyChange<>(materialDetails::getCountries, listChange, false);

    materialDetails.setCountries(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getCountries()).isEqualTo(List.of("new"));

  }

  @Test
  void applyChange_ignoreOldValuesIsSetTrue() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", "materialOld"));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("changeOld");
    stringChange.setNewValue("new");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, true);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isEqualTo("new");

  }

  @Test
  void applyChange_materiaNullValueChangeNewValueNotNull() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", null));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue(null);
    stringChange.setNewValue("somethingNotNull");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isEqualTo("somethingNotNull");

  }

  @Test
  void applyChange_materiaNotNullValueChangeNewValueNull() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", "old"));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("old");
    stringChange.setNewValue(null);

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isNull();
  }


  @Test
  void applyChange_materiaNotNullValueChangeNewValueNull2321312() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setMaterialKey(new MaterialKey("", null));
    final Change<String> stringChange = new Change<>();
    stringChange.setOldValue("old");
    stringChange.setNewValue("new");

    final ApplyChange<String> changeApplier = new ApplyChange<>(() -> materialDetails.getMaterialKey().getMaterialCode(), stringChange, false);

    materialDetails.getMaterialKey().setMaterialCode(changeApplier.applyChange().get());

    Assertions.assertThat(materialDetails.getMaterialKey().getMaterialCode()).isNull();
  }

  @Test
  void applyChange_materiaNotNullValueChangeNewValueNull_notEquals() {
    final MaterialDetails materialDetails = new MaterialDetails();
    materialDetails.setDimensions(new Dimensions(new BigDecimal(123L), null, null));
    final Change<BigDecimal> stringChange = new Change<>();
    stringChange.setOldValue(new BigDecimal(123L));
    stringChange.setNewValue(new BigDecimal(234L));

    final ApplyChange<BigDecimal> changeApplier = new ApplyChange<>(() -> materialDetails.getDimensions().getNetWeight(), stringChange, false);

    materialDetails.getDimensions().setNetWeight(changeApplier.applyChange((bigDecimal, bigDecimal2) -> bigDecimal.compareTo(bigDecimal2) == 0).get());

    Assertions.assertThat(materialDetails.getDimensions().getNetWeight()).isEqualTo(new BigDecimal(234L));
  }

  @Test
  void applyChange_applyDifferentBigDecimalObjectsWithoutSpecificApplierChange_returnsNotEquals() {
    final MaterialPlantDetails materialPlantDetails = new MaterialPlantDetails();
    final BigDecimal initialReorderPoint = BigDecimal.valueOf(22.0);
    materialPlantDetails.setReorderPoint(initialReorderPoint);

    final Change<BigDecimal> stringChange = new Change<>();
    final double aDouble = 22.0;

    stringChange.setOldValue(new BigDecimal(aDouble));
    stringChange.setNewValue(new BigDecimal(33));

    final ApplyChange<BigDecimal> changeApplier = new ApplyChange(materialPlantDetails::getReorderPoint, stringChange, false);

    materialPlantDetails.setReorderPoint(changeApplier.applyChange((bigDecimal, bigDecimal2) -> bigDecimal.compareTo(bigDecimal2) == 0).get());

    Assertions.assertThat(materialPlantDetails.getReorderPoint()).isEqualTo(initialReorderPoint);
  }

  @Test
  void applyChange_applyDifferentBigDecimalObjectsWithSpecificApplierChange_returnsEquals() {
    final MaterialPlantDetails materialPlantDetails = new MaterialPlantDetails();
    final BigDecimal initialReorderPoint = BigDecimal.valueOf(22.0);
    materialPlantDetails.setReorderPoint(initialReorderPoint);

    final Change<BigDecimal> stringChange = new Change<>();
    final double aDouble = 22.0;

    stringChange.setOldValue(new BigDecimal(aDouble));
    final BigDecimal newReorderPoint = new BigDecimal(33);
    stringChange.setNewValue(newReorderPoint);

    final ApplyChangeBigDecimal changeApplier = new ApplyChangeBigDecimal(materialPlantDetails::getReorderPoint, stringChange, false);

    materialPlantDetails.setReorderPoint(changeApplier.applyChange((bigDecimal, bigDecimal2) -> bigDecimal.compareTo(bigDecimal2) == 0).get());

    Assertions.assertThat(materialPlantDetails.getReorderPoint()).isEqualTo(newReorderPoint);
  }


  @SneakyThrows
  @Test
  void applyChangesA2A2() {
    final ObjectMapper objectMapper = new ObjectMapper();
    final String detailsJsonFromActiviti = "{\"materialId\":\"2ec1e454-d87f-41a2-bd56-1e8cd2872ee3\",\"materialKey\":{\"client\":\"010\",\"materialCode\":\"61046276\"},\"materialGroup\":\"FF1805070\",\"materialType\":\"ZVAL\",\"manufacturerDetails\":{\"manufacturerCode\":\"\",\"manufacturerPartNumber\":\"\",\"manufacturerName\":\"\"},\"unitsOfMeasure\":{\"baseUnitOfMeasurement\":\"M\",\"alternativeUnitsOfMeasure\":[]},\"basicData\":{\"deletionFlag\":false,\"industrySector\":\"Z\",\"productDivision\":\"P\",\"oldMaterialNumber\":\"\",\"documentNumber\":\"\",\"basicMaterial\":\"\",\"laboratoryDesignOffice\":\"\",\"batchManagementRequirementIndicator\":\"\",\"authorizationGroup\":\"\",\"crossPlantMaterialStatus\":\"S1\",\"crossPlantPurchasingGroup\":\"\",\"genericItemGroup\":\"\",\"externalMaterialGroup\":\"\",\"hazardousMaterialNumber\":\"\"},\"dimensions\":{\"sizeDimension\":\"\"},\"descriptions\":{\"shortDescriptions\":{\"it\":\"TUBI API 5L S.S. X 24\\\" - 609,6 X 9,52\"},\"purchaseOrderDescriptions\":{\"it\":\"TUBI API 5L S.S. DN 24\\\" - 609,6 X 9,52 estr calibr smuss\"},\"internalNoteDescriptions\":{},\"inspectionDescriptions\":{},\"longDescriptions\":{},\"normalizedShortDescriptions\":{},\"normalizedLongDescriptions\":{}},\"technicalAttributes\":{\"technicalAttributes\":[{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Spessore\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_DiametroEsternoTubo\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Materiale\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_DiametroNominale\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_DiametroInternoTubo\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Lunghezza\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Brand\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_NormativaDin\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_TipoRaccordo\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Attacco1\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Attacco3\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Curva\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Attacco2\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_PressioneNominale\"},{\"@type\":\"SingleValueAttribute\",\"code\":\"0_Code\"}]},\"externalAttributes\":{},\"materialGroupClassification\":{\"code\":\"FF1805070\",\"taxonomy\":\"NEW GM A2A\",\"mdDomain\":\"M\"},\"metadata\":{\"revision\":1,\"lastUpdatedDate\":1722499508585,\"createdDate\":1722499508585,\"semanticallyAnalyzed\":true},\"completeness\":\"POOR\",\"countries\":[\"IT\"],\"attachments\":[],\"mdDomain\":\"M\",\"generic\":false,\"autoExtensionToAllPlants\":false,\"autoExtensionClientList\":[\"010\"],\"famiglia\":\"024\",\"sottoFamiglia\":\"000-024\",\"specificaTecnica\":\"TUBIACCHPRES0006\",\"edizione\":\"\",\"revisione\":\"\",\"dataCustom\":1409270400000,\"productHierarchy\":\"\",\"volumeUnit\":\"\",\"internationalArticleNumberEanUpc\":\"\",\"serviceValuationClass\":\"\",\"customerFields\":{},\"categoryChanged\":false}";
    final MaterialDetails details = objectMapper.readValue(detailsJsonFromActiviti, MaterialDetails.class);
    final String changesJson = "{\"ignoreOldValues\":false,\"purchaseOrderDescriptions\":{\"it\":{\"oldValue\":\"TUBI API 5L S.S. X 24\\\" - 609,6 X 9,52\",\"newValue\":\"TUBI API 5L S.S. DN 24\\\" - 609,6 X 9,52 estr calibr smuss\"}},\"completeness\":{\"oldValue\":\"FAIR\",\"newValue\":\"POOR\"},\"specificaTecnica\":{\"newValue\":\"TUBIACCHPRES0006\"},\"dataCustom\":{\"newValue\":1409270400000}}";
    final Changes changes = objectMapper.readValue(changesJson, Changes.class);

    final MaterialDetails detailsWithChanges = ApplyChangesUtils.getNewMaterialDetailsWithChanges(changes, details);
    final String it = "it";
    Assertions.assertThat(detailsWithChanges.getDescriptions().getPurchaseOrderDescriptions().get(it)).isEqualTo(changes.getPurchaseOrderDescriptions().get(it).getNewValue());
  }


  @SneakyThrows
  @Test
  void givenInstance_whenLinkToAnotherGR_thenShouldChangeCodeAndRemoveGoldenRecordUUID() {
    final MaterialDetails instance = MaterialDetails.createEmptyMaterialDetails();
    final String oldGRCode = "code1";
    final String newGRCode = "code2";
    instance.setGoldenRecordAndCode(UUID.randomUUID(), oldGRCode);
    final Changes changes = Changes.builder().goldenRecordCode(new Change<>(oldGRCode, newGRCode)).build();
    final MaterialDetails detailsWithChanges = ApplyChangesUtils.getNewMaterialDetailsWithChanges(changes, instance);
    Assertions.assertThat(detailsWithChanges.getGoldenRecordCode()).isEqualTo(newGRCode);
    Assertions.assertThat(detailsWithChanges.getGoldenRecord()).isNull();
  }

  @SneakyThrows
  @Test
  void givenInstance_whenUnLink_thenShouldAddOnlyGRCode() {
    final MaterialDetails instance = MaterialDetails.createEmptyMaterialDetails();
    final String oldGRCode = "code1";
    final String newGRCode = null;
    instance.setGoldenRecordAndCode(UUID.randomUUID(), oldGRCode);
    final Changes changes = Changes.builder().goldenRecordCode(new Change<>(oldGRCode, newGRCode)).build();
    final MaterialDetails detailsWithChanges = ApplyChangesUtils.getNewMaterialDetailsWithChanges(changes, instance);
    Assertions.assertThat(detailsWithChanges.getGoldenRecordCode()).isEqualTo(newGRCode);
    Assertions.assertThat(detailsWithChanges.getGoldenRecord()).isNull();
  }

  @SneakyThrows
  @Test
  void givenMaterial_whenLink_thenShouldRemoveGoldenRecordCodeAndUUID() {
    final MaterialDetails instance = MaterialDetails.createEmptyMaterialDetails();
    final String newGRCode = "code1";
    final Changes changes = Changes.builder().goldenRecordCode(new Change<>(null, newGRCode)).build();
    final MaterialDetails detailsWithChanges = ApplyChangesUtils.getNewMaterialDetailsWithChanges(changes, instance);
    Assertions.assertThat(detailsWithChanges.getGoldenRecordCode()).isEqualTo(newGRCode);
    Assertions.assertThat(detailsWithChanges.getGoldenRecord()).isNull();
  }

}
