package com.creactives.tam4.common.utils.applychanges;

import com.creactives.tam4.messaging.Change;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class ApplyChangeForMapTest {

  @Test
  @DisplayName("Return new value when current is equal to old and ignore old value is false")
  void applychangeForMap_shortDescription_whenCurrentEqualToOldValue_havingFalseIgnoreOldValues() {
    final Map<String, String> description = new HashMap<>();
    description.put("en", "description");
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("en", new Change<>("description", "newDescription"));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(description, changeMap, false);

    final Map<String, String> newDescription = new HashMap<>();
    newDescription.put("en", "newDescription");
    Assertions.assertThat(updatedMap).isEqualTo(newDescription);
  }

  @Test
  @DisplayName("Return empty map when new value is null while current is equal to old value")
  void applychangeForMap_whenNewValueNull_havingFalseIgnoreValues() {
    final Map<String, String> shortDescription = new HashMap<>();
    shortDescription.put("en", "description");
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("en", new Change<>("description", null));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(shortDescription, changeMap, false);

    Assertions.assertThat(updatedMap).isEqualTo(new HashMap<>());
  }

  @Test
  @DisplayName("Return empty map when new value is null while current  and old value are null")
  void applychangeForMap_whenNewAndOldValueNull_havingFalseIgnoreValues() {
    final Map<String, String> description = new HashMap<>();
    description.put("en", null);
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("en", new Change<>(null, null));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(description, changeMap, false);

    Assertions.assertThat(updatedMap).isEqualTo(new HashMap<>());
  }

  @Test
  @DisplayName("Return new value when current is not equal to old but ignored old is true")
  void applychangeForMap_shortDescription_whenCurrentNotEqualToOldValue_havingTrueIgnoredOldValues() {
    final Map<String, String> description = new HashMap<>();
    description.put("en", "description");
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("en", new Change<>("anotherDescription", "newDescription"));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(description, changeMap, true);

    final Map<String, String> newDescription = new HashMap<>();
    newDescription.put("en", "newDescription");
    Assertions.assertThat(updatedMap).isEqualTo(newDescription);
  }

  @Test
  @DisplayName("Return old value if there is no match between keys and false ignore old values")
  void applyChangeForMap_shortDescription_whenNoEqualKeys_havingFalseIgnoreOldValues() {
    final Map<String, String> description = new HashMap<>();
    description.put("en", "description");
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("it", new Change<>("anotherDescription", "newDescription"));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(description, changeMap, false);

    final Map<String, String> newDescription = new HashMap<>();
    newDescription.put("en", "description");
    Assertions.assertThat(updatedMap).isEqualTo(newDescription);
  }

  @Test
  @DisplayName("Return old and new value if there is no match between keys and ignore old values is true")
  void applyChangeForMap_shortDescription_whenNoEqualKeys_havingTrueIgnoreOldValues() {
    final Map<String, String> description = new HashMap<>();
    description.put("en", "description");
    final Map<String, Change<String>> changeMap = new HashMap<>();
    changeMap.put("it", new Change<>("anotherDescription", "newDescription"));

    final Map<String, String> updatedMap = ApplyChange.applyChangesForMap(description, changeMap, true);

    final Map<String, String> updatedDescription = new HashMap<>();
    updatedDescription.put("en", "description");
    updatedDescription.put("it", "newDescription");
    Assertions.assertThat(updatedMap).isEqualTo(updatedDescription);
  }

}
