package com.creactives.tam4.dataloader.configuration;

import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.io.support.DefaultPropertySourceFactory;
import org.springframework.core.io.support.EncodedResource;

import java.io.IOException;
import java.util.Objects;

@Configuration
@PropertySource(value = "base.yml", factory = BaseConfiguration.Yaml.class)
public class BaseConfiguration {

  public static class Yaml extends DefaultPropertySourceFactory {

    @Override
    public org.springframework.core.env.PropertySource<?> createPropertySource(final String name, final EncodedResource resource) throws IOException {
      if (resource == null) {
        return super.createPropertySource(name, resource);
      }

      final CompositePropertySource compositePropertySource = new CompositePropertySource(Objects.requireNonNull(resource.getResource().getFilename()));
      new YamlPropertySourceLoader().load(resource.getResource().getFilename(), resource.getResource())
          .stream()
          .forEach(compositePropertySource::addPropertySource);
      return compositePropertySource;

    }
  }
}
