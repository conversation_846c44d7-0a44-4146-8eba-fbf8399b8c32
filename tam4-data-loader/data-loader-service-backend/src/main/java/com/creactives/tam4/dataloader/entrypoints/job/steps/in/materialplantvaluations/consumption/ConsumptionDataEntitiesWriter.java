package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialplantvaluations.consumption;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Repository;

import java.util.List;

@RequiredArgsConstructor
@Repository
@StepScope
public class ConsumptionDataEntitiesWriter implements ItemWriter<ConsumptionDataEntity> {

  private final ConsumptionDataStagingRepository consumptionDataStagingRepository;


  @Override
  public void write(final List<? extends ConsumptionDataEntity> list) throws Exception {
    for (final ConsumptionDataEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    consumptionDataStagingRepository.saveAll(list);
  }
}
