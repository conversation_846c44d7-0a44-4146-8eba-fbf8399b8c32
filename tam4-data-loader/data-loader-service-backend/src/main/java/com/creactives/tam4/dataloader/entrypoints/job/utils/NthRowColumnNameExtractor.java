package com.creactives.tam4.dataloader.entrypoints.job.utils;

import org.springframework.batch.item.excel.Sheet;
import org.springframework.batch.item.excel.support.rowset.FirstRowColumnNameExtractor;

public class NthRowColumnNameExtractor extends FirstRowColumnNameExtractor {

  private final int row;

  public NthRowColumnNameExtractor(final int row) {
    super();
    this.row = row;
  }

  @Override
  public String[] getColumnNames(final Sheet sheet) {
    if (row > 1) {
      for (int i = 0; i < row - 1; i++) {
        sheet.nextRow();
      }
    }
    return super.getColumnNames(sheet);
  }
}
