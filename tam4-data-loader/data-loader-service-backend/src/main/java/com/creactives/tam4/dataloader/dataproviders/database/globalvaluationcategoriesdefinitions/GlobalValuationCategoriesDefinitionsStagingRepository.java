package com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GlobalValuationCategoriesDefinitionsStagingRepository extends PagingAndSortingRepository<GlobalValuationCategoryDefinitionsEntity, Long> {

  List<GlobalValuationCategoryDefinitionsEntity> findByClientAndGlobalValuationCategory(String client, String globalValuationCategory);

  List<GlobalValuationCategoryDefinitionsEntity> findByClientAndLanguageAndGlobalValuationCategoryIn(String client, String language, List<String> globalValuationCategories);

  List<GlobalValuationCategoryDefinitionsEntity> findByClientAndGlobalValuationCategoryAndSynchronizationState(String client, String globalValuationCategory, String synchronizationState);

}
