package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.language;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Component
@StepScope
@RequiredArgsConstructor
public class PlantLanguageEntitiesWriter implements ItemWriter<PlantLanguageUpdater> {

  private final NamedParameterJdbcTemplate jdbcTemplate;

  @Override
  public void write(final List<? extends PlantLanguageUpdater> list) throws Exception {
    final Map[] batchParams = list.stream()
        .map(i -> Map.of("languageCode", i.getLanguageCode(),
            "client", i.getClient(),
            "plantCode", i.getPlantCode()))
        .collect(Collectors.toList())
        .toArray(new Map[]{});


    jdbcTemplate.batchUpdate("update plant_definitions_staging "
        + " set language = :languageCode "
        + " where client = :client "
        + " and plant = :plantCode ", batchParams);
  }
}
