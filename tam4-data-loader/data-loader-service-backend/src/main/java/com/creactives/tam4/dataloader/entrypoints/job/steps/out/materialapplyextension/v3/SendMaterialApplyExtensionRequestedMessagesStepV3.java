package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.integration.async.AsyncItemProcessor;
import org.springframework.batch.integration.async.AsyncItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

/**
 * Created on 6/12/2019 5:52 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class SendMaterialApplyExtensionRequestedMessagesStepV3 implements StepConfigurer {

  private static final String SEND_MATERIAL_EXTENDED = "send-material-extended";
  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForMaterialExtensionV3 calculateAggregateForMaterialExtensionV3;
  private final MaterialPlantItemProcessorV3 itemProcessor;
  private final MaterialApplyExtensionRequestProviderV3 itemWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final TaskExecutor taskExecutor;
  private final MaterialsDataStagingItemReader materialsDataStagingItemReader;

  @Value("${data-loader.send-job.materials-chunk-size:1000}")
  private int chunkSize;

  public SendMaterialApplyExtensionRequestedMessagesStepV3(final StepBuilderFactory stepBuilderFactory,
                                                           final CalculateAggregateForMaterialExtensionV3 calculateAggregateForMaterialExtensionV3,
                                                           final MaterialPlantItemProcessorV3 itemProcessor,
                                                           final MaterialApplyExtensionRequestProviderV3 itemWriter,
                                                           final ChunkWriterListener chunkWriterListener,
                                                           final TaskExecutor taskExecutor,
                                                           final MaterialsDataStagingItemReader materialsDataStagingItemReader
  ) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.calculateAggregateForMaterialExtensionV3 = calculateAggregateForMaterialExtensionV3;
    this.itemProcessor = itemProcessor;
    this.itemWriter = itemWriter;
    this.chunkWriterListener = chunkWriterListener;
    this.taskExecutor = taskExecutor;
    this.materialsDataStagingItemReader = materialsDataStagingItemReader;
  }

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get(SEND_MATERIAL_EXTENDED)
        .<MaterialEntity, Future<MaterialApplyExtensionLoadMessage>>chunk(chunkSize)
        .listener(chunkWriterListener)
        .reader(materialsDataStagingItemReader)
        .processor(buildAsyncProcessor())
        .writer(buildAsyncWriter())
        .listener(new StatisticsListener())
        .build();
  }


  private AsyncItemWriter<MaterialApplyExtensionLoadMessage> buildAsyncWriter() {
    final AsyncItemWriter<MaterialApplyExtensionLoadMessage> materialAsyncItemWriter = new AsyncItemWriter<>();
    materialAsyncItemWriter.setDelegate(new CompositeItemWriterBuilder<MaterialApplyExtensionLoadMessage>()
        .delegates(List.of(itemWriter)).build());
    return materialAsyncItemWriter;
  }

  private AsyncItemProcessor<MaterialEntity, MaterialApplyExtensionLoadMessage> buildAsyncProcessor() {
    final AsyncItemProcessor<MaterialEntity, MaterialApplyExtensionLoadMessage> materialEntityFutureAsyncItemProcessor = new AsyncItemProcessor<>();
    materialEntityFutureAsyncItemProcessor.setDelegate(new CompositeItemProcessorBuilder<MaterialEntity, MaterialApplyExtensionLoadMessage>().delegates(List.of(calculateAggregateForMaterialExtensionV3,
        itemProcessor
    )).build());
    materialEntityFutureAsyncItemProcessor.setTaskExecutor(taskExecutor);
    return materialEntityFutureAsyncItemProcessor;
  }
//FIXME: GBRRRT - Add table update!
}
