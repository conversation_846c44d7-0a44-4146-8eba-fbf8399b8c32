package com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@RequiredArgsConstructor
@Configuration
public class MassiveFileUploadJobFactory {


  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final MassiveFileUploadImportStep massiveFileUploadImportStep;

  @Bean
  public JobFactory massiveFileUploadFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(massiveFileUploadImportStep)
        .build("massive-file-upload");
  }

}
