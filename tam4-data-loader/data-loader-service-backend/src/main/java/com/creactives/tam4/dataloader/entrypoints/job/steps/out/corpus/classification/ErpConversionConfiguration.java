package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.classification;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Configuration
@ConfigurationProperties(prefix = "data-loader.erp-conversion")
@Data
@Log4j2
public class ErpConversionConfiguration {

  private Boolean enabled = Boolean.FALSE;

  private List<String> toErpCodeExclusion = new ArrayList<>();

  private SpecialErpConversionConfiguration specialErpConversionConfiguration;

  public SpecialAttributeErpConversion getSpecialAttributeErpConversion(final String materialType, final String classCode, final String originalErpCode) {
    log.debug("getSpecialAttributeErpConversion: materialType {} - classCode {} - originalErpCode {}", materialType, classCode, originalErpCode);
    if (Objects.isNull(specialErpConversionConfiguration)
        || MapUtils.isEmpty(specialErpConversionConfiguration.getMaterialTypeMapAttributes())
        || !specialErpConversionConfiguration.getMaterialTypeMapAttributes().containsKey(materialType)) {
      return null;
    }

    final List<SpecialAttributeErpConversion> allConversions = specialErpConversionConfiguration.getMaterialTypeMapAttributes().get(materialType);

    if (CollectionUtils.isEmpty(allConversions)) {
      return null;
    }

    return allConversions.stream()
        .filter(a -> StringUtils.equalsIgnoreCase(a.getOriginalErpCode(), originalErpCode)
            && StringUtils.equalsIgnoreCase(a.getClassCode(), classCode))
        .findFirst()
        .orElse(null);

  }
}
