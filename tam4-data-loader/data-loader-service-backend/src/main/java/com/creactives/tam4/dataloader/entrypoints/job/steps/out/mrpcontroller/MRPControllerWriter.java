package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpcontroller;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers.MRPControllerStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MRPControllerUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MRPControllerWriter implements ItemWriter<MRPControllerUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

//  private final MRPControllerStagingRepository mrpControllerStagingRepository;

  public MRPControllerWriter(final WriteMessageService sendMessages, final MRPControllerStagingRepository mrpControllerStagingRepository) {
    this.sendMessages = sendMessages;

//    this.mrpControllerStagingRepository = mrpControllerStagingRepository;
  }

  @Override
  public void write(final List<? extends MRPControllerUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
