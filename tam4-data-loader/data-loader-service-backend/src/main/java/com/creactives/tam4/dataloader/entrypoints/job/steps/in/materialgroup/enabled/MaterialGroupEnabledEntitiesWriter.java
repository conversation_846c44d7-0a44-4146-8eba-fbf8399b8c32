package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialgroup.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class MaterialGroupEnabledEntitiesWriter implements ItemWriter<MaterialGroupEntity> {

  private final MaterialGroupStagingRepository materialGroupStagingRepository;

  @Override
  public void write(final List<? extends MaterialGroupEntity> list) throws Exception {
    for (final MaterialGroupEntity entity : list) {
      if (entity.getEnabled()) {
        materialGroupStagingRepository.setEnabled(true, entity.getMaterialGroup(), entity.getClient());
      }
    }

  }
}
