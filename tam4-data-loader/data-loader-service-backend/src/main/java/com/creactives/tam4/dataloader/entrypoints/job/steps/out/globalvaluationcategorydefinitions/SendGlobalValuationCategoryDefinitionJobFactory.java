package com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendGlobalValuationCategoryDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendGlobalValuationCategoryDefinitionAddOrUpdateStep step;

  public SendGlobalValuationCategoryDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                         final StepBuilderFactory stepBuilderFactory,
                                                         final SendGlobalValuationCategoryDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }


  @Bean
  public JobFactory sendGlobalValuationCategoryDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T149T, T149T_Enabled")
        .build("send-global-valuation-category-definition");
  }
}
