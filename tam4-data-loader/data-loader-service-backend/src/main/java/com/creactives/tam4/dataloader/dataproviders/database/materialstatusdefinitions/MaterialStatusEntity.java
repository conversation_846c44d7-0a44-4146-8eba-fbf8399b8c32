package com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "material_status_keys_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T141", description = "Material Status from Materials Management/PPC View")
public class MaterialStatusEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_status_keys_staging_generator")
  @SequenceGenerator(name = "material_status_keys_staging_generator", sequenceName = "material_status_keys_staging_seq", allocationSize = 1000)
  private long id;


  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_status")
  @FieldDoc(required = true, sapField = "MMSTA", description = "Material status from MM/PP view")
  private String materialStatus;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "obsolete")
  private Boolean obsolete;
}
