package com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExternalMaterialGroupStagingRepository extends PagingAndSortingRepository<ExternalMaterialGroupEntity, Long> {

  List<ExternalMaterialGroupEntity> findByClientAndExternalMaterialGroup(String client, String externalMaterialGroup);

  List<ExternalMaterialGroupEntity> findByClientAndExternalMaterialGroupIn(String client, List<String> externalMaterialGroups);

  @Modifying
  @Query("update ExternalMaterialGroupEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update ExternalMaterialGroupEntity set enabled= :enabled WHERE externalMaterialGroup=:externalMaterialGroup AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("externalMaterialGroup") String externalMaterialGroup, @Param("client") String client);

  Page<ExternalMaterialGroupEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<ExternalMaterialGroupEntity> findAllByClient(String client, Pageable pageable);

  Page<ExternalMaterialGroupEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
