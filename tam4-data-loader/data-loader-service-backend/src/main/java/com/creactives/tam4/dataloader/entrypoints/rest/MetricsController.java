package com.creactives.tam4.dataloader.entrypoints.rest;

import com.creactives.tam4.dataloader.configuration.MetricReporterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@Component
@RestController
@RequiredArgsConstructor
public class MetricsController {
  private final MetricReporterService reporter;

  @GetMapping("/data-loader/api/metrics/update")
  public ResponseEntity<Void> updateRefreshRate(@RequestParam(value = "refresh", required = false, defaultValue = "60") final long refreshRateInSeconds) {
    this.reporter.restart(refreshRateInSeconds, TimeUnit.SECONDS);
    return ResponseEntity.ok()
        .build();
  }

}
