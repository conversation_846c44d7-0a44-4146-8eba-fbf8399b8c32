package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitsOfMeasureStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
@RequiredArgsConstructor
@Service
public class SetUnitOfMeasureSynchronizedFields implements ItemProcessor<AggregateDataForUnitOfMeasureDefinition, AggregateDataForUnitOfMeasureDefinition> {

  private final UnitsOfMeasureStagingRepository repository;

  @Override
  @Transactional
  public AggregateDataForUnitOfMeasureDefinition process(final AggregateDataForUnitOfMeasureDefinition aggregateDataForUnitOfMeasureDefinition) throws Exception {

    final List<UnitOfMeasureEntity> unitOfMeasureEntities = repository.findByClientAndUnitOfMeasureAndSynchronizationState(aggregateDataForUnitOfMeasureDefinition.getUnitOfMeasureKeyEntity().getClient(),
        aggregateDataForUnitOfMeasureDefinition.getUnitOfMeasureKeyEntity().getUnitOfMeasure(),
        SyncStatus.PENDING.getCode());
    unitOfMeasureEntities.parallelStream().filter(Objects::nonNull).forEach(entity -> {
      entity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
      entity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
    });
    return aggregateDataForUnitOfMeasureDefinition;
  }
}
