package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 4:41 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MaterialPlantValuationRow implements Serializable {

  private long id;

  @JsonAlias("client")
  private String client;

  @JsonAlias("material_code")
  private String materialCode;

  @JsonAlias("plant_id")
  private String plantId;

  @JsonAlias("valuation_type")
  private String valuationType;

  @JsonAlias("deletion_flag_material")
  private boolean deletionFlagMaterial;

  @JsonAlias("valuation_class")
  private String valuationClass;

  @JsonAlias("valuation_category")
  private String valuationCategory;

  @JsonAlias("inventory_amount")
  private BigDecimal inventoryAmount;

  @JsonAlias("price_control_indicator")
  private String priceControlIndicator;

  @JsonAlias("moving_average_price")
  private BigDecimal movingAveragePrice;

  @JsonAlias("standard_price")
  private BigDecimal standardPrice;

  @JsonAlias("price_unit")
  private Integer priceUnit;

  @JsonAlias("total_value")
  private BigDecimal totalValue;

  @JsonAlias("usage_material")
  private String usageMaterial;

  @JsonAlias("origin_material")
  private String originMaterial;

  @JsonAlias("created_on")
  private Timestamp createdOn;

  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;

  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;

  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @JsonAlias("synchronization_state")
  private String synchronizationState;
}
