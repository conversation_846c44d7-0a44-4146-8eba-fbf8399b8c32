package com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QMCertificateCategoryStagingRepository extends PagingAndSortingRepository<QMCertificateCategoryEntity, Long> {

  List<QMCertificateCategoryEntity> findByClientAndCertificateType(String client, String certificateType);
  @Modifying
  @Query("update QMCertificateCategoryEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update QMCertificateCategoryEntity set enabled= :enabled WHERE certificateType=:certificateType AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("certificateType") String certificateType, @Param("client") String client);

  List<QMCertificateCategoryEntity> findByClientAndCertificateTypeAndSynchronizationState(String client, String certificateType, String status);

  Page<QMCertificateCategoryEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<QMCertificateCategoryEntity> findAllByClient(String client, Pageable pageable);

  Page<QMCertificateCategoryEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
