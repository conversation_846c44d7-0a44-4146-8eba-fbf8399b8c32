package com.creactives.tam4.dataloader.configuration;

import com.google.common.base.Preconditions;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Configuration
@ConfigurationProperties(prefix = "data-loader.clients")
@Data
public class ClientsConfiguration {

  private List<Client> clients;
  private boolean skipValidation = false;
  private String currentClientCode;

  public Client forCode(final String inputCode) {
    return clients.stream()
        .filter(it -> it.getCode().equals(inputCode))
        .findAny()
        .orElseThrow(() -> new IllegalArgumentException("Could not find configured client with code " + inputCode));
  }

  @PostConstruct
  public void validate() {
    Preconditions.checkArgument(CollectionUtils.isNotEmpty(clients), "Must configure at least one client!");
    final Set<String> uniqueCodes = clients.stream().map(Client::getCode).collect(Collectors.toSet());
    Preconditions.checkArgument(clients.size() == uniqueCodes.size(),
        "Configured client codes must be unique! One of the following codes has been defined multiple times: " + uniqueCodes
    );

    final Set<String> uniqueFolders = clients.stream().map(Client::getFolderName).collect(Collectors.toSet());
    Preconditions.checkArgument(clients.size() == uniqueFolders.size(),
        "Configured client folders must be unique! One of the following folders has been defined multiple times: " + uniqueFolders
    );
  }
}
