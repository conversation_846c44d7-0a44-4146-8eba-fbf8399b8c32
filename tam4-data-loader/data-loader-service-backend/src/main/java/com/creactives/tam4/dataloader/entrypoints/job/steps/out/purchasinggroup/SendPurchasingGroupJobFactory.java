package com.creactives.tam4.dataloader.entrypoints.job.steps.out.purchasinggroup;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendPurchasingGroupJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendPurchasingGroupAddOrUpdateStep step;

  public SendPurchasingGroupJobFactory(final JobBuilderFactory jobBuilderFactory, final StepBuilderFactory stepBuilderFactory, final SendPurchasingGroupAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendPurchasingGroupFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T024")
        .build("send-purchasing-group");
  }
}
