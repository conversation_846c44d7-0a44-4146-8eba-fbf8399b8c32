package com.creactives.tam4.dataloader.dataproviders.database;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.approvedmanufacturerparts.ApprovedManufacturedPartsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.basicmaterial.BasicMaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.businessadress.BusinessAddressEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristics.CharacteristicsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn.CharacteristicsCABNEntity;
import com.creactives.tam4.dataloader.dataproviders.database.client.ClientEntity;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.currency.CurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup.DefinitionExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.dimension.DimensionTextEntity;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial.HazardousMaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.laboratory.LaboratoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginEntity;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialconsumption.MaterialConsumptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions.MaterialGroupDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition.MaterialTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mmvmaterialStatus.MMVMaterialStatusEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers.MRPControllersEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition.MRPTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentheader.PurchasingDocumentHeaderEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentitem.PurchasingDocumentItemEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup.PurchasingGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions.QMCertificateCategoryDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions.QMControlDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmrecord.QMRecordsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.serialnumber.SerialNumberManagementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierEntity;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.ISOUnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.ISOUnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationarea.ValuationAreaEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclass.ValuationClassesEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsEntity;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.io.FileWriter;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;

public class GenerateDoc {
  private static <T> List<Node<T>> sort(final List<Node<T>> graph) {
    final ArrayList<Node<T>> order = new ArrayList<>();

    if (graph == null) {
      return order;
    }

    final Map<Node<T>, Integer> indegree = getIndegree(graph);

    final Queue<Node<T>> queue = new LinkedList<>();

    for (final Node<T> node : graph) {
      if (indegree.get(node) == 0) {
        queue.offer(node);
        order.add(node);
      }
    }

    while (!queue.isEmpty()) {
      final Node<T> node = queue.poll();
      for (final Node<T> neighbor : node.neighbors) {
        indegree.put(neighbor, indegree.get(neighbor) - 1);
        if (indegree.get(neighbor) == 0) {
          queue.offer(neighbor);
          order.add(neighbor);
        }
      }
    }
    //  graph is cyclic or not
    if (order.size() == graph.size()) {
      return order;
    }
    throw new IllegalStateException("Cyclic dependency detected");
  }

  private static <T> Map<Node<T>, Integer> getIndegree(final Iterable<Node<T>> graph) {
    final Map<Node<T>, Integer> indegree = new HashMap<>();
    for (final Node<T> node : graph) {
      indegree.put(node, 0);
    }

    for (final Node<T> node : graph) {
      for (final Node<T> neighbor : node.neighbors) {
        indegree.put(neighbor, indegree.get(neighbor) + 1);
      }
    }
    return indegree;
  }

  @SneakyThrows
  public static void main(final String[] args) {
    final FileWriter fw = new FileWriter("docs.md");

    writeDoc(fw, ClientEntity.class, "T000");
    writeDoc(fw, AlternativeUnitsOfMeasureEntity.class, "MARM");
    writeDoc(fw, ApprovedManufacturedPartsEntity.class, "AMPL");
    writeDoc(fw, BasicMaterialEntity.class, "TWSPR");
    writeDoc(fw, BusinessAddressEntity.class, "ADRC");
    writeDoc(fw, CharacteristicsCABNEntity.class, "CABN");
    writeDoc(fw, CharacteristicsEntity.class, "AUSP");
    writeDoc(fw, CompanyEntity.class, "T001");
//    writeDoc(fw, CompanyTextEntity.class, "T001T"); // are we really using this???
    writeDoc(fw, ConsumptionDataEntity.class, "Report");
    writeDoc(fw, CountryDefinitionsEntity.class, "T005");
//    writeDoc(fw, CountryDefinitionsKeyEntity.class, "T005T");
    writeDoc(fw, CurrencyEntity.class, "TCURC");
    writeDoc(fw, DefinitionExternalMaterialGroupEntity.class, "TWEWT");

    writeDoc(fw, DimensionTextEntity.class, "T006T");
    writeDoc(fw, ExternalMaterialGroupEntity.class, "TWEW");
    writeDoc(fw, GlobalValuationCategoryDefinitionsEntity.class, "T149T");
    writeDoc(fw, HazardousMaterialEntity.class, "MGEF");
    writeDoc(fw, ISOUnitOfMeasureEntity.class, "T006J");
    writeDoc(fw, ISOUnitsOfMeasurementEntity.class, "T006I");
    writeDoc(fw, IndustrySectorDefinitionEntity.class, "T137T");// x
    writeDoc(fw, IndustrySectorEntity.class, "T137");// x
    writeDoc(fw, ItemCategoryGroupDefinitionsEntity.class, "TPTMT");
    writeDoc(fw, ItemCategoryGroupDefinitionsKeyEntity.class, "TPTM");
    writeDoc(fw, LaboratoryEntity.class, "T024L");
    writeDoc(fw, LogisticsHandlingGroupEntity.class, "TLOGT");
    writeDoc(fw, LotSizeDefinitionEntity.class, "T439T");
    writeDoc(fw, MMVMaterialStatusEntity.class, "T141");
    writeDoc(fw, MRPControllersEntity.class, "T024D");
    writeDoc(fw, MRPMaterialLevelEntity.class, "T438M");
    writeDoc(fw, MRPTypeDefinitionEntity.class, "T438T");
    writeDoc(fw, MRPTypeEntity.class, "T438A");
    writeDoc(fw, MaterialConsumptionEntity.class, "Report");
    writeDoc(fw, MaterialEntity.class, "MARA");
    writeDoc(fw, MaterialGroupDefinitionEntity.class, "T023T");
    writeDoc(fw, MaterialGroupEntity.class, "T023");
    writeDoc(fw, MaterialPlantEntity.class, "MARC");
    writeDoc(fw, MaterialPlantValuationEntity.class, "MBEW");
    writeDoc(fw, MaterialStatusDefinitionsEntity.class, "T141T");
    writeDoc(fw, MaterialStatusEntity.class, "T141");
    writeDoc(fw, MaterialStorageLocationEntity.class, "MARD");
//    writeDoc(fw, MaterialToIgnore.class, "");
//    writeDoc(fw, MaterialToIncludeInSemantics.class, "");
    writeDoc(fw, MaterialTypeDefinitionEntity.class, "T134T");
    writeDoc(fw, MaterialTypesEntity.class, "T134");
    writeDoc(fw, LongDescriptionEntity.class, "Report");
    writeDoc(fw, POHistoryEntity.class, "Report");
//    writeDoc(fw, PlantCurrencyEntity.class, "");
    writeDoc(fw, PlantEntity.class, "T001W");
    writeDoc(fw, ProductDivisionEntity.class, "TSPAT");
    writeDoc(fw, ProductDivisionKeyEntity.class, "TSPA");
    writeDoc(fw, PurchasingDocumentHeaderEntity.class, "EKKO");
    writeDoc(fw, PurchasingDocumentItemEntity.class, "EKPO");
    writeDoc(fw, PurchasingGroupEntity.class, "T024");
    writeDoc(fw, QMCertificateCategoryDefinitionEntity.class, "TQ05T");
    writeDoc(fw, QMCertificateCategoryEntity.class, "TQ05");
    writeDoc(fw, QMControlDefinitionsEntity.class, "TQ08T");
    writeDoc(fw, QMControlEntity.class, "TQ08");
    writeDoc(fw, QMRecordsEntity.class, "QINF");
    writeDoc(fw, SchedulingMarginEntity.class, "T436A");
    writeDoc(fw, SerialNumberManagementEntity.class, "T377P");
    writeDoc(fw, ShortDescriptionEntity.class, "MAKT");
    writeDoc(fw, StorageLocationEntity.class, "T001L");
    writeDoc(fw, SupplierEntity.class, "LFA1");
    writeDoc(fw, UnitOfMeasureEntity.class, "T006A");
//    writeDoc(fw, UnitOfMeasureKeyEntity.class, "T006");
    writeDoc(fw, UnitsOfMeasurementEntity.class, "T006");
    writeDoc(fw, ValuationAreaEntity.class, "T001K");
    writeDoc(fw, ValuationClassDefinitionsEntity.class, "T025T");
//    writeDoc(fw, ValuationClassDefinitionsKeyEntity.class, "T025");
    writeDoc(fw, ValuationClassesEntity.class, "T025");
    fw.flush();
    fw.close();
  }

  @SneakyThrows
  static void writeDoc(final Appendable appendable, final Class<?> entity, final String sapTableName) {
    final ClassDoc classDoc = entity.getAnnotation(ClassDoc.class);

    final String name = entity.getSimpleName()
        .replaceAll("[Dd]efinitions?", "Text")
        .replaceAll("[Ee]ntity", "");

    final String collect = Arrays.stream(name.split("(?<!(^|[A-Z]))(?=[A-Z])|(?<!^)(?=[A-Z][a-z])")).collect(Collectors.joining(" "));
    final String endpoint = Arrays.stream(name.split("(?<!(^|[A-Z]))(?=[A-Z])|(?<!^)(?=[A-Z][a-z])")).collect(Collectors.joining("-")).toLowerCase(Locale.ROOT);
    appendable.append("## ").append(classDoc.description()).append(" Endpoint (/external/api/synchronize/").append(endpoint).append(")")
        .append(System.lineSeparator())
        .append(System.lineSeparator())
        .append("This endpoint is used to synchronize the content of ")
        .append(collect).append(" table.");
    if (StringUtils.isNotBlank(classDoc.sapTable())) {
      appendable
          .append(" In SAP, the equivalent table is ").append(sapTableName).append(". ");
    }
    if (StringUtils.isNotBlank(classDoc.additionalDescription())) {
      appendable.append(classDoc.additionalDescription());
    } else {
      appendable.append("The endpoint expects to receive the complete snapshot of available data.");
    }
    appendable
        .append(System.lineSeparator())
        .append(System.lineSeparator())
        .append("#### Parameters")
        .append(System.lineSeparator())
        .append(System.lineSeparator())
        .append("| Parameter name | Reference | Description |").append(System.lineSeparator())
        .append("|----------------|-----------|-------------|").append(System.lineSeparator());

    for (final Field field : entity.getDeclaredFields()) {
      final String fieldName = field.getName();
      final FieldDoc documentation = field.getAnnotation(FieldDoc.class);
      if (documentation != null) {
        final boolean required = documentation.required();
        final String reference = documentation.referenceTable();
        final String sapField = documentation.sapField();
        final String description = documentation.description();
        appendable
            .append("|")
            .append(fieldName)
            .append(required ? " <sup>* required</sup>" : "")
            .append(" ")
            .append(field.getType().getSimpleName())
            .append("|")
            .append(reference).append("|")
            .append(description);
        if (sapField.contains("/")) {
          appendable.append(" (").append(sapField).append(")");
        } else if (StringUtils.isNotBlank(sapField)) {
          appendable.append(" (").append(sapTableName).append("/").append(sapField).append(")");
        }
        appendable.append("|")
            .append(System.lineSeparator());
      }
    }
    appendable.append(System.lineSeparator());
    appendable.append(System.lineSeparator());
  }

  private static String uppercaseFirst(final String collect) {
    return Character.toUpperCase(collect.charAt(0)) + collect.substring(1);
  }

  private static class Node<T> {
    private final T label;
    private final List<Node<T>> neighbors;

    public Node(final T label) {
      this.label = label;
      neighbors = new ArrayList<>();
    }

    public void addDependency(final Node<T> d) {
      neighbors.add(d);
    }

    public T getLabel() {
      return label;
    }

    @Override
    public boolean equals(final Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      final Node<?> node = (Node<?>) o;
      return Objects.equals(label, node.label);
    }

    @Override
    public int hashCode() {
      return Objects.hash(label);
    }
  }

}
