package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureEntity;
import com.creactives.tam4.dataloader.entrypoints.job.utils.CachedTableStepListener;
import com.creactives.tam4.messaging.materialdetails.BasicData;
import com.creactives.tam4.messaging.materialdetails.Descriptions;
import com.creactives.tam4.messaging.materialdetails.Dimensions;
import com.creactives.tam4.messaging.materialdetails.ManufacturerDetails;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import com.creactives.tam4.messaging.materialdetails.MaterialKey;
import com.creactives.tam4.messaging.materialdetails.Metadata;
import com.creactives.tam4.messaging.materialdetails.TechnicalAttributes;
import com.creactives.tam4.messaging.materialdetails.UnitsOfMeasure;
import com.creactives.tam4.messaging.materials.MaterialAlternativeUnitOfMeasureDetails;
import com.creactives.tam4.messaging.materials.commands.MaterialCreationRequestMessage;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;


/**
 * Created on 6/12/2019 7:19 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@Component
@Log4j2
@StepScope
public class ConvertAggregateToMaterialCreationRequestItemProcessor implements
    ItemProcessor<AggregateDataForMaterialCreated, MaterialCreationRequestMessage> {

  private final MaterialCodeNormalizer materialCodeNormalizer;
  private final Map<String, String> supplierNamesCache;
//  private final StepExecution stepExecution;

  public ConvertAggregateToMaterialCreationRequestItemProcessor(final MaterialCodeNormalizer materialCodeNormalizer,
                                                                @Value("#{stepExecution}") final StepExecution stepExecution) {
    this.supplierNamesCache = CachedTableStepListener.extractValueCache(stepExecution, "suppliers_definitions_staging", List.of("client","code"), "name");
    this.materialCodeNormalizer = materialCodeNormalizer;
  }

  private static <T, K, V> Collector<T, ?, Map<K, V>> toMap(final Function<T, K> extractKey,
                                                            final Function<T, V> extractValue) {
    return Collectors.groupingBy(extractKey,
        Collectors.mapping(extractValue,
            Collectors.reducing(null, (left, right) -> right)
        )
    );
  }

  private static Map<String, String> nullToEmpty(final Map<String, String> normalizedShortDescriptions) {
    if (normalizedShortDescriptions == null) {
      return Maps.newHashMap();
    }
    return normalizedShortDescriptions;
  }

  @Override
  @SneakyThrows
  public MaterialCreationRequestMessage process(final AggregateDataForMaterialCreated aggregate) {
    final MaterialEntity materialEntity = aggregate.getMaterialEntity();
    final Map<String, String> externalAttributes = new HashMap<>();
    aggregate.getCharacteristics().asMap()
        .forEach((key, value) -> externalAttributes.put(key, StringUtils.join(value, ", ")));

    final MaterialDetails materialDetails = MaterialDetails.builder()
        .materialKey(MaterialKey.builder().client(materialEntity.getClient())
            .materialCode(materialCodeNormalizer.removeZeroes(materialEntity.getMaterialCode()))
            .build())
        .basicData(BasicData.builder()
            .deletionFlag(materialEntity.isDeletionFlag())
            .productDivision(materialEntity.getProductDivision())
            .industrySector(materialEntity.getIndustrySector())
            .oldMaterialNumber(materialEntity.getOldMaterialNumber())
            .documentNumber(materialEntity.getDocumentNumber())
            .basicMaterial(materialEntity.getBasicMaterial())
            .laboratoryDesignOffice(materialEntity.getLaboratory())
            .batchManagementRequirementIndicator(Optional.of(materialEntity.isBatchManagementRequirementIndicator())
                .map(Object::toString).orElse(null)) //fixme should be switched to boolean also in MaterialCreationRequestMessage
//            .materialStatusValidFromDate(materialEntity.getMaterialStatusValidFromDate())
            .materialStatusValidFromDate(materialEntity.getMaterialStatusValidFromDate() == null ? null : new Date(materialEntity.getMaterialStatusValidFromDate()))
            .authorizationGroup(materialEntity.getAuthorizationGroup())
            .crossPlantMaterialStatus(materialEntity.getCrossPlantMaterialStatus())
            .crossPlantPurchasingGroup(null) // this field cannot be populated by the data loader
            .genericItemGroup(materialEntity.getGenericItemGroup())
            .externalMaterialGroup(materialEntity.getExternalMaterialGroup())
            .hazardousMaterialNumber(materialEntity.getHazardousMaterialNumber())
            .build())
        .materialGroup(materialEntity.getMaterialGroup())
        .materialType(materialEntity.getMaterialType())
        .unitsOfMeasure(UnitsOfMeasure.builder()
            .baseUnitOfMeasurement(materialEntity.getBaseUnitOfMeasurement())
            .purchasingUnitOfMeasurement(materialEntity.getPurchasingMeasurementUnits())
            .weightUnitOfMeasurement(materialEntity.getWeightUnit())
            .alternativeUnitsOfMeasure(convertAlternativeUnitsOfMeasure(aggregate.getAlternativeUnitsOfMeasureEntities()))
            .build())
        .manufacturerDetails(ManufacturerDetails.builder()
            .manufacturerPartNumber(materialEntity.getManufacturerPartNumber())
            .manufacturerCode(materialEntity.getManufacturerCode())
            .manufacturerName(CachedTableStepListener.getValueFromCache(supplierNamesCache,materialEntity.getClient(), materialEntity.getManufacturerCode()))
            .build())
        .dimensions(Dimensions.builder()
            .netWeight(materialEntity.getNetWeight())
            .grossWeight(materialEntity.getGrossWeight())
            .sizeDimension(materialEntity.getSizeDimension())
            .build())
        .descriptions(Descriptions.builder()
            .shortDescriptions(nullToEmpty(convertShortDescriptions(aggregate.getShortDescriptionEntities())))
            .purchaseOrderDescriptions(nullToEmpty(convertLongDescriptions(aggregate.getPoDescriptionEntities())))
            .internalNoteDescriptions(nullToEmpty(convertLongDescriptions(aggregate.getInternalNoteDescriptionEntities())))
            .inspectionDescriptions(nullToEmpty(convertLongDescriptions(aggregate.getInspectionDescriptionEntities())))
            .longDescriptions(nullToEmpty(convertLongDescriptions(aggregate.getLongDescriptionEntities())))
//            .normalizedShortDescriptions(nullToEmpty(normalizedDescriptions != null ? normalizedDescriptions.getNormalizedShortDescriptions()
//                : null))
//            .normalizedLongDescriptions(nullToEmpty(normalizedDescriptions != null ? normalizedDescriptions.getNormalizedLongDescriptions()
//                : null))
            .build())
//        .technicalAttributes(aggregate.getTechnicalAttributes())
        .technicalAttributes(TechnicalAttributes.builder().build())
        .externalAttributes(externalAttributes)
//        .completeness(convertCompleteness(aggregate.getCompletenessLevel()))
//        .technicalClassification(aggregate.getTechnicalClassification() != null ?
//            new CategoryKey(aggregate.getTechnicalClassification().getName(),
//                aggregate.getTechnicalClassification().getTaxonomy().getName()
//            ) : null
//        )
//        .materialGroupClassification(aggregate.getMaterialGroupClassification() != null ?
//            new CategoryKey(aggregate.getMaterialGroupClassification().getName(),
//                aggregate.getMaterialGroupClassification().getTaxonomy().getName()
//            ) : null
//        )
//        .enrichedMaterialGroupClassification(aggregate.getEnrichedMaterialGroupClassification() != null ?
//            new CategoryKey(aggregate.getEnrichedMaterialGroupClassification().getName(),
//                aggregate.getEnrichedMaterialGroupClassification().getTaxonomy().getName()
//            ) : null
//        )
        .metadata(Metadata.builder()
            .semanticallyAnalyzed(materialEntity.isSemanticallyAnalyzed())
            .build())
        .mdDomain(materialEntity.getMdDomain())
        .generic(aggregate.getGenericMasterData())

        //FIXME: Custom fields required by A2A T4-1756
        .famiglia(materialEntity.getFamiglia())
        .sottoFamiglia(materialEntity.getSottoFamiglia())
        .specificaTecnica(materialEntity.getSpecificaTecnica())
        .edizione(materialEntity.getEdizione())
        .revisione(materialEntity.getRevisione())
//        .dataCustom(materialEntity.getDataCustom())
        .dataCustom(materialEntity.getDataCustom() == null ? null : new Date(materialEntity.getDataCustom()))

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(materialEntity.getProductHierarchy())
        .volume(Optional.ofNullable(materialEntity.getVolume()).map(BigDecimal::doubleValue)
            .orElse(null))
        .volumeUnit(materialEntity.getVolumeUnit())
        .internationalArticleNumberEanUpc(materialEntity.getInternationalArticleNumberEanUpc())
        .serviceValuationClass(materialEntity.getServiceValuationClass())
        .build();
    return new MaterialCreationRequestMessage(materialDetails);
  }

  private List<MaterialAlternativeUnitOfMeasureDetails> convertAlternativeUnitsOfMeasure(final List<AlternativeUnitsOfMeasureEntity> alternativeUnitsOfMeasureEntities) {
    return alternativeUnitsOfMeasureEntities.stream()
        .map(it -> {
              final MaterialAlternativeUnitOfMeasureDetails alternativeUM = new MaterialAlternativeUnitOfMeasureDetails();
              alternativeUM.setAlternativeUnitOfMeasurement(it.getAlternativeUnitOfMeasurement());
              alternativeUM.setDenominator(it.getDenominator());
              alternativeUM.setNumerator(it.getNumerator());
              return alternativeUM;
            }
        )
        .collect(Collectors.toList());
  }

  private Map<String, String> convertShortDescriptions(final Collection<ShortDescriptionEntity> shortDescriptionEntities) {
    if (shortDescriptionEntities != null) {
      return shortDescriptionEntities.stream()
          .filter(shortDescriptionEntity -> shortDescriptionEntity.getLanguage() != null)
          .filter(shortDescriptionEntity -> shortDescriptionEntity.getDescription() != null)
          .collect(toMap(ShortDescriptionEntity::getLanguage,
              ShortDescriptionEntity::getDescription
          ));
    }
    return null;
  }

  private Map<String, String> convertLongDescriptions(final Collection<LongDescriptionEntity> longDescriptionEntities) {
    if (longDescriptionEntities != null) {
      return longDescriptionEntities.stream()
          .filter(descriptionEntity -> descriptionEntity.getLongDescription() != null)
          .collect(toMap(LongDescriptionEntity::getLanguage,
              LongDescriptionEntity::getLongDescription
          ));
    }
    return null;
  }
}
