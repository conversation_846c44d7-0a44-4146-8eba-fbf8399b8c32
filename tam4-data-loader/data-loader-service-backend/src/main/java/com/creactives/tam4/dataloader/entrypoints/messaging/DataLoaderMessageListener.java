package com.creactives.tam4.dataloader.entrypoints.messaging;

import com.codahale.metrics.annotation.Timed;
import com.creactives.rabbitmq.RabbitQuickListener;
import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.core.usecase.events.EventConfirmedUseCase;
import com.creactives.tam4.dataloader.core.usecase.events.EventRejectedUseCase;
import com.creactives.tam4.messaging.warnings.commands.MaterialWarningCreateConfirmedMessage;
import com.creactives.tam4.messaging.warnings.commands.MaterialWarningCreateRejectedMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.creactives.tam4.common.utils.LoggingUtils.logReceivedMessageNotHandled;

@RequiredArgsConstructor
@Log4j2
@Service
@Transactional
@RabbitQuickListener(queues = MessagingConfiguration.DATA_LOADER_INBOUND_QUEUE_NAME, bufferCount = 500)
@ConditionalOnProperty(name = "data-loader.message-listener.active", havingValue = "true", matchIfMissing = false)
public class DataLoaderMessageListener {

  private final EventConfirmedUseCase eventConfirmedUseCase;
  private final EventRejectedUseCase eventRejectedUseCase;

  @RabbitHandler(isDefault = true)
  public void consumeMessage(final Object message) {
    logReceivedMessageNotHandled(message);
  }

  @Timed
  @RabbitHandler
  public void consumeMaterialWarningCreateRejectedMessage(@Payload final MaterialWarningCreateRejectedMessage message) {
    eventRejectedUseCase.updateEvent(message.getExternalCorrelationId(), message.getReason(), message.getRejectedDate());
  }

  @Timed
  @RabbitHandler
  public void consumeMaterialWarningCreateConfirmedMessage(@Payload final MaterialWarningCreateConfirmedMessage message) {
    eventConfirmedUseCase.updateEvent(message.getExternalCorrelationId(), message.getConfirmedDate());
  }


}
