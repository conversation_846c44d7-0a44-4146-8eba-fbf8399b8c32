package com.creactives.tam4.dataloader.entrypoints.job.steps.in.uma;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created on 6/11/2019 4:31 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@StepScope
public class AlternativeUnitsOfMeasureEntitiesWriter implements ItemWriter<AlternativeUnitsOfMeasureEntity> {

  private final AlternativeUnitsOfMeasureStagingRepository repository;

  @Override
  public void write(final List<? extends AlternativeUnitsOfMeasureEntity> items) {
    for (final AlternativeUnitsOfMeasureEntity entity :
        items) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    repository.saveAll(items);
  }
}
