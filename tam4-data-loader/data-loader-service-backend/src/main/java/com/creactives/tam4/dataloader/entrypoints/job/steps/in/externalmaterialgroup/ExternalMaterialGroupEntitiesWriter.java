package com.creactives.tam4.dataloader.entrypoints.job.steps.in.externalmaterialgroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class ExternalMaterialGroupEntitiesWriter implements ItemWriter<ExternalMaterialGroupEntity> {

  private final ExternalMaterialGroupStagingRepository externalMaterialGroupStagingRepository;

  @Override
  public void write(final List<? extends ExternalMaterialGroupEntity> list) throws Exception {
    for (final ExternalMaterialGroupEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    externalMaterialGroupStagingRepository.saveAll(list);
  }
}
