package com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface HazardousMaterialStagingRepository extends PagingAndSortingRepository<HazardousMaterialEntity, Long> {

  List<HazardousMaterialEntity> findByClientAndRegionCodeAndHazardousMaterialNumberIn(String client, String regionCode, List<String> hazardousMaterialNumbers);

  Page<HazardousMaterialEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<HazardousMaterialEntity> findAllByClient(String client, Pageable pageable);

  Page<HazardousMaterialEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
