package com.creactives.tam4.dataloader.entrypoints.job.steps.out.addcharateristics;

import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.AggregateDataForMaterialCreated;
import com.creactives.tam4.dataloader.entrypoints.job.utils.CacheTableConfig;
import com.creactives.tam4.dataloader.entrypoints.job.utils.CachedTableStepListener;
import com.creactives.tam4.messaging.materials.commands.MaterialAddCharacteristicsMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.integration.async.AsyncItemProcessor;
import org.springframework.batch.integration.async.AsyncItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.concurrent.Future;

/**
 * Created on 6/12/2019 5:52 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class SendMaterialAddCharacteristicsMessagesStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final MaterialAddCharacteristicsReader materialRepositoryItemReader;
  private final ConvertAggregateToMaterialAddCharacteristicsItemProcessor convertAggregateToMaterialAddCharacteristicsItemProcessor;
  private final MaterialAddCharacteristicsMessageProvider itemWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final TaskExecutor taskExecutor;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get("send-material-add-characteristics")
        .<AggregateDataForMaterialCreated, Future<MaterialAddCharacteristicsMessage>>chunk(10000)
        .listener(chunkWriterListener)
        .reader(materialRepositoryItemReader)
        .processor(buildAsyncProcessor())
        .writer(buildAsyncWriter())
        .listener(new StatisticsListener())
        .listener(loadCachesForStep())
        .build();
  }


  private AsyncItemWriter<MaterialAddCharacteristicsMessage> buildAsyncWriter() {
    final AsyncItemWriter<MaterialAddCharacteristicsMessage> materialAsyncItemWriter = new AsyncItemWriter<>();
    materialAsyncItemWriter.setDelegate(itemWriter);
    return materialAsyncItemWriter;
  }

  private AsyncItemProcessor<AggregateDataForMaterialCreated, MaterialAddCharacteristicsMessage> buildAsyncProcessor() {
    final AsyncItemProcessor<AggregateDataForMaterialCreated, MaterialAddCharacteristicsMessage> materialEntityFutureAsyncItemProcessor = new AsyncItemProcessor<>();
    materialEntityFutureAsyncItemProcessor.setDelegate(new CompositeItemProcessorBuilder<AggregateDataForMaterialCreated, MaterialAddCharacteristicsMessage>().delegates(List.of( // setSyncronizedFields,
        convertAggregateToMaterialAddCharacteristicsItemProcessor
    )).build());
    materialEntityFutureAsyncItemProcessor.setTaskExecutor(taskExecutor);
    return materialEntityFutureAsyncItemProcessor;
  }

  public CachedTableStepListener loadCachesForStep() {
      final String query = "select client, code, name from suppliers_definitions_staging";

      return new CachedTableStepListener(new NamedParameterJdbcTemplate(dataSource),
          List.of(CacheTableConfig
              .builder()
              .tableName("suppliers_definitions_staging")
              .query(query)
              .keyFields(List.of("client","code"))
              .build()));
  }


}
