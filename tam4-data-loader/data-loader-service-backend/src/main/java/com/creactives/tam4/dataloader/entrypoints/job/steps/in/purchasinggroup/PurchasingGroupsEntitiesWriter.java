package com.creactives.tam4.dataloader.entrypoints.job.steps.in.purchasinggroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup.PurchasingGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup.PurchasingGroupsStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class PurchasingGroupsEntitiesWriter implements ItemWriter<PurchasingGroupEntity> {

  private final PurchasingGroupsStagingRepository purchasingGroupsStagingRepository;

  @Override
  public void write(final List<? extends PurchasingGroupEntity> list) throws Exception {
    for (final PurchasingGroupEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    purchasingGroupsStagingRepository.saveAll(list);
  }
}
