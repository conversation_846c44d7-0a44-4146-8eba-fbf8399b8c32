package com.creactives.tam4.dataloader.dataproviders.database.productdivision;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductDivisionKeyStagingRepository extends PagingAndSortingRepository<ProductDivisionKeyEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<ProductDivisionKeyEntity> findByClientAndDivision(String client, String division);

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  List<ProductDivisionKeyEntity> findByClientAndDivisionIn(String client, List<String> divisions);

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  <S extends ProductDivisionKeyEntity> @NotNull S save(@NotNull S entity);

  Page<ProductDivisionKeyEntity> findAllByClient(String client, Pageable pageable);
}
