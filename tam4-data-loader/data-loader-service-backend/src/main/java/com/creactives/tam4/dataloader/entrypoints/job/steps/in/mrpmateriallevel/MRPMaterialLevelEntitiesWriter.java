package com.creactives.tam4.dataloader.entrypoints.job.steps.in.mrpmateriallevel;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class MRPMaterialLevelEntitiesWriter implements ItemWriter<MRPMaterialLevelEntity> {

  private final MRPMaterialLevelStagingRepository mrpMaterialLevelStagingRepository;

  @Override
  public void write(final List<? extends MRPMaterialLevelEntity> list) throws Exception {
    for (final MRPMaterialLevelEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    mrpMaterialLevelStagingRepository.saveAll(list);
  }
}
