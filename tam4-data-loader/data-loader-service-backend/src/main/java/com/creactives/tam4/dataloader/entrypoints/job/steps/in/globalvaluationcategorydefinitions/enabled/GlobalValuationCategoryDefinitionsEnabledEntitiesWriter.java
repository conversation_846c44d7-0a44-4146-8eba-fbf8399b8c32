package com.creactives.tam4.dataloader.entrypoints.job.steps.in.globalvaluationcategorydefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoriesKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsKeyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class GlobalValuationCategoryDefinitionsEnabledEntitiesWriter implements ItemWriter<GlobalValuationCategoryDefinitionsKeyEntity> {

  private final GlobalValuationCategoriesKeysStagingRepository globalValuationCategoriesKeysStagingRepository;

  @Override
  public void write(final List<? extends GlobalValuationCategoryDefinitionsKeyEntity> list) throws Exception {
    for (final GlobalValuationCategoryDefinitionsKeyEntity entity : list) {
      if (entity.getEnabled()) {
        globalValuationCategoriesKeysStagingRepository.setEnabled(entity.getEnabled(), entity.getGlobalValuationCategory(), entity.getClient());
      }
    }
  }
}
