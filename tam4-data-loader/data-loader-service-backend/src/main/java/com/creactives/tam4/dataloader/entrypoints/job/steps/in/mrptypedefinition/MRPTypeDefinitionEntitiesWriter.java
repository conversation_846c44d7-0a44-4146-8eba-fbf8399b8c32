package com.creactives.tam4.dataloader.entrypoints.job.steps.in.mrptypedefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition.MRPTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition.MRPTypeDefinitionStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class MRPTypeDefinitionEntitiesWriter implements ItemWriter<MRPTypeDefinitionEntity> {

  private final MRPTypeDefinitionStagingRepository mrpTypeDefinitionStagingRepository;
  private final MRPTypeStagingRepository mrpTypeStagingRepository;

  @Override
  public void write(final List<? extends MRPTypeDefinitionEntity> list) throws Exception {
    for (final MRPTypeDefinitionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    mrpTypeDefinitionStagingRepository.saveAll(list);
    for (final MRPTypeDefinitionEntity item : list) {
      if (mrpTypeStagingRepository.findByClientAndMrpType(item.getClient(), item.getMrpType()).isEmpty()) {
        mrpTypeStagingRepository.save(MRPTypeEntity.builder()
            .client(item.getClient())
            .mrpType(item.getMrpType())
            .createdOn(new Timestamp(System.currentTimeMillis()))
            .build());
      }
    }
  }
}
