package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcertificatecategory;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendQMCertificateCategoryJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendQMCertificateCategoryAddOrUpdateStep step;

  public SendQMCertificateCategoryJobFactory(final JobBuilderFactory jobBuilderFactory,
                                             final StepBuilderFactory stepBuilderFactory,
                                             final SendQMCertificateCategoryAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendQMCertificateCategoryFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: TQ05T")
        .build("send-qm-certificate-category");
  }
}
