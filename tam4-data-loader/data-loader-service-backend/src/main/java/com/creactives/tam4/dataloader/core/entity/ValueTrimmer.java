package com.creactives.tam4.dataloader.core.entity;

import java.util.function.Function;

/**
 * Created on 6/13/2019 11:29 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
public class ValueTrimmer implements Function<String, String> {

  @Override
  public String apply(final String s) {
    if (s != null) {
      return s.trim();
    }
    return null;
  }
}
