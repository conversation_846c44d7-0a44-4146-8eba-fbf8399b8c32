package com.creactives.tam4.dataloader.dataproviders.database.marginkey;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "float_scheduling_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SchedulingMarginEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "float_scheduling_definitions_staging_generator")
  @SequenceGenerator(name = "float_scheduling_definitions_staging_generator", sequenceName = "float_scheduling_definitions_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "plant")
  private String plant;

  @Column(name = "sched_marg_key")
  private String schedMargKey;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}

