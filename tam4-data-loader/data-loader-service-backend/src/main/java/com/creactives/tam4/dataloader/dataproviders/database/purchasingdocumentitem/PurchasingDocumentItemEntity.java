package com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentitem;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "purchasing_document_item_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PurchasingDocumentItemEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "purchasing_document_item_staging_generator")
  @SequenceGenerator(name = "purchasing_document_item_staging_generator", sequenceName = "purchasing_document_item_staging_seq", allocationSize = 1000)
  private long id;

  @Column
  private String client;

  @Column
  private String materialCode;

  @Column
  private String plant;

  @Column
  private Double purchaseOrderQuantity;

  @Column
  private Double netOrderValue;

  @Column
  private String purchasingDocumentNr;

  @Column
  private String itemNr;

  @Column
  private String deletionIndicator;

  @Column
  private String rfqStatus;

  @Column
  private String shortText;

  @Column
  private String materialNumber;

  @Column
  private String storageLocation;

  @Column
  private String materialGroup;

  @Column
  private String purchasingRecordNr;

  @Column
  private String targetQuantity;

  @Column
  private String purchasingUnitOfMeasure;

  @Column
  private String orderPriceUnit;

  @Column
  private String conversionNumeratorOrderUnit;

  @Column
  private String conversionNumeratorBaseUnit;

  @Column
  private String conversionDenominatorBaseUnit;

  @Column
  private String conversionDenominatorOrderUnit;

  @Column
  private String purchasingGroupNetPrice;

  @Column
  private String priceUnit;

  @Column
  private String grossOrder;

  @Column
  private String deliveryIndicator;

  @Column
  private String itemCategory;

  @Column
  private String accountAssignmentCategory;

  @Column
  private String invoiceReceiptIndicator;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
