package com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "plants_additional_languages_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PlantsAdditionalLanguagesStagingEntity {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "plant_languages_entity_generator")
  @SequenceGenerator(name = "plant_languages_entity_generator", sequenceName = "plant_languages_entity_seq", allocationSize = 1000)
  private long id;


  @Column
  private String languageCode;

  @Column
  private String client;

  @Column
  private String plant;


}
