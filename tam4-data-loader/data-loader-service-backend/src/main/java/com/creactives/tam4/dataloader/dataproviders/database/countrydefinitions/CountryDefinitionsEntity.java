package com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "country_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T005", description = "Countries")
public class CountryDefinitionsEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "country_definitions_staging_generator")
  @SequenceGenerator(name = "country_definitions_staging_generator", sequenceName = "country_definitions_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(sapField = "MANDT", description = "Client", referenceTable = "T000")
  private String client;

  @Column(name = "country")
  @FieldDoc(sapField = "LAND1", description = "Country Key")
  private String country;

  @Column(name = "description")
  @FieldDoc(sapField = "INTCA", description = "Country ISO code")
  private String description;

  @Column(name = "language")
  @FieldDoc(sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column(name = "long_description")
  @FieldDoc(sapField = "INTCA3", description = "ISO country code 3 char")
  private String longDescription;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
