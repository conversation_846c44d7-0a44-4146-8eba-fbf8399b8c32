package com.creactives.tam4.dataloader.dataproviders.database.materialgroup;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialGroupStagingRepository extends PagingAndSortingRepository<MaterialGroupEntity, Long> {

  List<MaterialGroupEntity> findByClientAndMaterialGroup(String client, String materialGroup);

  List<MaterialGroupEntity> findByClientAndMaterialGroupIn(String client, List<String> materialGroup);

  @Modifying
  @Query("update MaterialGroupEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update MaterialGroupEntity set enabled= :enabled WHERE materialGroup=:materialGroup AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("materialGroup") String materialGroup, @Param("client") String client);

  Page<MaterialGroupEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MaterialGroupEntity> findAllByClient(String client, Pageable pageable);

  Page<MaterialGroupEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
