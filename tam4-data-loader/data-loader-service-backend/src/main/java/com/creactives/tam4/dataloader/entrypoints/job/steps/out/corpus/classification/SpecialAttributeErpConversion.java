package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.classification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpecialAttributeErpConversion implements Serializable {

  //  private String materialType;
  private String classCode;
  private String originalErpCode;
  private String customErpCode;
}
