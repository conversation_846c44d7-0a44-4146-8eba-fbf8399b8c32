package com.creactives.tam4.dataloader.entrypoints.job.steps.out.lotsizedefinition;


import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionStagingRepository;
import com.creactives.tam4.messaging.materials.commands.LotSizeDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class ConvertEntityToLotSizeDefinitionRequestItemProcessor implements ItemProcessor<LotSizeDefinitionEntity, LotSizeDefinitionUpsertRequestMessage> {

  private final LotSizeDefinitionStagingRepository lotSizeDefinitionStagingRepository;

  @Override
  public LotSizeDefinitionUpsertRequestMessage process(final LotSizeDefinitionEntity lotSizeEntity) throws Exception {
    final List<LotSizeDefinitionEntity> lotSizeDefinitionEntities = lotSizeDefinitionStagingRepository
        .findByClientAndLotSize(lotSizeEntity.getClient(), lotSizeEntity.getLotSize());
    return LotSizeDefinitionUpsertRequestMessage.builder()
        .client(lotSizeEntity.getClient())
        .descriptions(lotSizeDefinitionEntities.stream()
            .filter(lotSizeDefinitionEntity -> lotSizeDefinitionEntity.getLanguage() != null)
            .collect(Collectors.toMap(LotSizeDefinitionEntity::getLanguage,
                    LotSizeDefinitionEntity::getDescription
                )
            )
        )
        .lotSize(lotSizeEntity.getLotSize())
        .build();
  }
}
