package com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForCountryDefinition {

  private CountryDefinitionsKeyEntity countryDefinitionsKeyEntity;
  private List<CountryDefinitionsEntity> countryEntities;
}
