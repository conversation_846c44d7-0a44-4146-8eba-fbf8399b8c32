package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.language;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Log4j2
@Component
public class PlantAdditionalLanguageEntitiesWriter implements ItemWriter<PlantsAdditionalLanguagesStagingEntity> {

  private final PlantsAdditionalLanguagesStagingRepository plantsAdditionalLanguagesStagingRepository;

  @Override
  public void write(final List<? extends PlantsAdditionalLanguagesStagingEntity> list) throws Exception {
    final List<? extends PlantsAdditionalLanguagesStagingEntity> filteredPlants = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    plantsAdditionalLanguagesStagingRepository.saveAll(filteredPlants);
  }
}
