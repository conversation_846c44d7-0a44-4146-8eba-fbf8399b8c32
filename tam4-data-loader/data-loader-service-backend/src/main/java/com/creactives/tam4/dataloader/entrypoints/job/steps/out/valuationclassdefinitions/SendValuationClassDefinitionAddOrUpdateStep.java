package com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.ValuationClassDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class SendValuationClassDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForValuationClassDefinition calculateAggregateForValuationClassDefinition;
  private final ConvertAggregateToValuationClassDefinitionRequestItemProcessor convertAggregateToValuationClassDefinitionRequestItemProcessor;
  private final ValuationClassDefinitionWriter valuationClassDefinitionWriter;
  private final ValuationClassDefinitionsKeysItemReader valuationClassDefinitionsKeysItemReader;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-valuation-class-definition-add-or-update")
        .<ValuationClassDefinitionsKeyEntity, ValuationClassDefinitionUpsertRequestMessage>chunk(1000)
        .reader(valuationClassDefinitionsKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<ValuationClassDefinitionsKeyEntity, ValuationClassDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForValuationClassDefinition,
                convertAggregateToValuationClassDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<ValuationClassDefinitionUpsertRequestMessage>().delegates(List.of(valuationClassDefinitionWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<ValuationClassDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "valuation_class_definitions_staging", "valuation_class",
        (valuationClassDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, valuationClassDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, valuationClassDefinitionUpsertRequestMessage.getValuationClass());
        });

  }

}
