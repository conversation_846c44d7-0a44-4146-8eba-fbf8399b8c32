package com.creactives.tam4.dataloader.entrypoints.job.utils;

import com.creactives.tam4.common.utils.StopWatchUtils;
import java.util.Arrays;
import java.util.HashMap;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
public class CachedTableStepListener implements StepExecutionListener {

  private static final String SEPARATOR = "§";
  private final NamedParameterJdbcTemplate jdbcTemplate;
  private final List<CacheTableConfig> tablesToCache;

  public CachedTableStepListener(final NamedParameterJdbcTemplate jdbcTemplate, final List<CacheTableConfig> tablesToCache) {
    this.jdbcTemplate = jdbcTemplate;
    this.tablesToCache = tablesToCache;
  }

  public static Map<String, Map<String, Object>> getCacheFromContext(final StepExecution stepExecution, final String tableName) {
    final Map<String, Map<String, Object>> tableCache = (Map<String, Map<String, Object>>) stepExecution.getExecutionContext().get(tableName);

    if (tableCache == null) {
      return Collections.emptyMap();
    }

    return tableCache;
  }

  public static <T> Map<String, T> extractValueCache(final StepExecution stepExecution, final String tableName, final List<String> codeKey, final String valueKey) {
    final Map<String, Map<String, Object>> tableCache = getCacheFromContext(stepExecution, tableName);

    if (tableCache.isEmpty()) {
      return Collections.emptyMap();
    }

    return tableCache.values().stream().collect(Collectors.toMap(
        it -> {
        final StringBuilder k = new StringBuilder("§");
        codeKey.stream().forEach(
            ck -> k.append(it.get(ck))
        );
        return k.toString();
            },
        it -> (T) it.get(valueKey)));
  }

  public static <T> T getValueFromCache(Map<String, T> cache, String... keyValues){
    final StringBuilder k = new StringBuilder(SEPARATOR);
    Arrays.stream(keyValues).forEach(key -> k.append(key));
    return cache.get(k.toString());
  }

  @Override
  public void beforeStep(final StepExecution stepExecution) {
    log.info("Initializing cached tables - START");

    if (CollectionUtils.isNotEmpty(tablesToCache)) {
      tablesToCache.forEach(c -> {
        final String cacheKey = stepExecution.getStepName() + "::" + stepExecution.getId() + "::" + c.getTableName();
        StopWatchUtils.startStopwatch(cacheKey);
        stepExecution.getExecutionContext().put(c.getTableName(), loadCache(c, stepExecution));
        StopWatchUtils.stopAndPrint(cacheKey, "INFO");
      });
    }

    log.info("Initializing cached tables - END");
  }

    public Map<String, Map<String, Object>> loadCache(final CacheTableConfig tableConfig, final StepExecution stepExecution) {

    final Map<String, Object> params;
    if (CollectionUtils.isNotEmpty(tableConfig.getInputParamNames())){
      params = new HashMap<>(tableConfig.getInputParamNames().size());
      for (final String paramName : tableConfig.getInputParamNames()) {
        params.put(paramName, stepExecution.getExecutionContext().get(paramName));
      }
    }else{
      params = Collections.emptyMap();
    }

    final List<Map<String, Object>> res = jdbcTemplate.queryForList(tableConfig.getQuery(), params);

    if (CollectionUtils.isEmpty(res)) {
      return Collections.emptyMap();
    }

    return res.stream().collect(Collectors.toMap(v -> {
      final StringBuilder k = new StringBuilder("§");
      tableConfig.getKeyFields().forEach(kf -> k.append(v.get(kf)));
      return k.toString();
    }, v -> v));
  }

  @Override
  public ExitStatus afterStep(final StepExecution stepExecution) {
    log.info("Cleanup cached tables - START");
//    cachedTables.clear();
    log.info("Cleanup cached tables - END");

    return stepExecution.getExitStatus();
  }

}
