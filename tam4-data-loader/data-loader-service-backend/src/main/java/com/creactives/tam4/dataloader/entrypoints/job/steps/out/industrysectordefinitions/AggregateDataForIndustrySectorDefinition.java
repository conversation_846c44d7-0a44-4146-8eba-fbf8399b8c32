package com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForIndustrySectorDefinition {

  private IndustrySectorEntity industrySectorEntity;
  private List<IndustrySectorDefinitionEntity> industrySectorEntities;
}
