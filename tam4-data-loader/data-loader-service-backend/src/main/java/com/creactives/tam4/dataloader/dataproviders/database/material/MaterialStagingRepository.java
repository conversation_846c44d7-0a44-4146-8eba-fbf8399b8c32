package com.creactives.tam4.dataloader.dataproviders.database.material;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface MaterialStagingRepository extends PagingAndSortingRepository<MaterialEntity, Long> {

  Optional<MaterialEntity> findByClientAndMaterialCode(String client, String materialCode);

  List<MaterialEntity> findByClientAndMaterialCodeIn(String client, List<String> materialCode);

  long countByClient(String client);

  long countByClientAndIgnore(final String client, boolean isIgnored);

  long countByClientAndSemanticallyAnalyzed(String client, boolean isAnalysed);

  @SuppressWarnings("unused")
  Page<MaterialEntity> findAllByIgnore(boolean ignored, Pageable pageable);

  @Modifying
  @Query("update MaterialEntity set semantically_analyzed= :semanticallyAnalyzed where client= :client")
  void setAllClientSemanticallyAnalyzed(@Param("semanticallyAnalyzed") boolean semanticallyAnalyzed, @Param("client") String client);

//    @Modifying
//    @Query(nativeQuery = true, value = "update materials_data_staging set semantically_analyzed = :semanticallyAnalyzed where id in :ids")
//    void updateSemanticallyAnalizedByIds(@Param("semanticallyAnalyzed") boolean semanticallyAnalyzed, @Param("ids") List<Long> ids);

  Page<MaterialEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MaterialEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);

  Page<MaterialEntity> findAllByClient(String client, Pageable pageable);

}
