package com.creactives.tam4.dataloader.entrypoints.job.steps.out.storagelocation;


import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationEntity;
import com.creactives.tam4.messaging.materials.commands.StorageLocationDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class ConvertEntityToStorageLocationDefinitionRequestItemProcessor implements ItemProcessor<StorageLocationEntity, StorageLocationDefinitionUpsertRequestMessage> {


  @Override
  public StorageLocationDefinitionUpsertRequestMessage process(final StorageLocationEntity storageLocationEntity) throws Exception {
    return StorageLocationDefinitionUpsertRequestMessage.builder()
        .client(storageLocationEntity.getClient())
        .plantCode(storageLocationEntity.getPlant())
        .storageLocation(storageLocationEntity.getStorageLocation())
        .description(storageLocationEntity.getDescription())
        .build();
  }
}
