package com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForValuationClassDefinition {

  private ValuationClassDefinitionsKeyEntity valuationClassDefinitionsKeyEntity;
  private List<ValuationClassDefinitionsEntity> valuationClassEntities;
}
