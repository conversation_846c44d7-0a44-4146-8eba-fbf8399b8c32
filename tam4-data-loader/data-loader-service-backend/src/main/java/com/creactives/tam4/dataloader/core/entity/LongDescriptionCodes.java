package com.creactives.tam4.dataloader.core.entity;

public enum LongDescriptionCodes {
  BEST("purchase-order"),
  GRUN("long"),
  IVER("internal-note"),
  PRUE("inspection");

  private final String code;

  LongDescriptionCodes(final String code) {
    this.code = code;
  }

  public static LongDescriptionCodes findByCode(final String code) {
    for (final LongDescriptionCodes value : LongDescriptionCodes.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }
    throw new IllegalArgumentException("Enum value not found for code: " + code);
  }

  public String getCode() {
    return code;
  }
}
