package com.creactives.tam4.dataloader.entrypoints.job.steps.in.dimension;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.dimension.DimensionTextEntity;
import com.creactives.tam4.dataloader.dataproviders.database.dimension.DimensionTextStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class DimensionTextEntitiesWriter implements ItemWriter<DimensionTextEntity> {

  private final DimensionTextStagingRepository dimensionTextStagingRepository;

  @Override
  public void write(final List<? extends DimensionTextEntity> list) throws Exception {
    for (final DimensionTextEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    dimensionTextStagingRepository.saveAll(list);
  }
}
