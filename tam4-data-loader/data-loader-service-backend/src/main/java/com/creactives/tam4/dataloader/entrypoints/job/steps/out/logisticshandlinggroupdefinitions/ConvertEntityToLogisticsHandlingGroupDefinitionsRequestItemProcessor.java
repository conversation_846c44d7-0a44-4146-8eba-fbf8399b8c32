package com.creactives.tam4.dataloader.entrypoints.job.steps.out.logisticshandlinggroupdefinitions;


import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupEntity;
import com.creactives.tam4.messaging.materials.commands.LogisticsHandlingGroupUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ConvertEntityToLogisticsHandlingGroupDefinitionsRequestItemProcessor implements ItemProcessor<LogisticsHandlingGroupEntity, LogisticsHandlingGroupUpsertRequestMessage> {

  @Override
  public LogisticsHandlingGroupUpsertRequestMessage process(final LogisticsHandlingGroupEntity logisticsHandlingGroupEntity) throws Exception {
    return LogisticsHandlingGroupUpsertRequestMessage.builder()
        .client(logisticsHandlingGroupEntity.getClient())
        .logisticsHandlingGroup(logisticsHandlingGroupEntity.getLogisticsHandlingGroup())
        .descriptions(Map.of(logisticsHandlingGroupEntity.getLanguage(), logisticsHandlingGroupEntity.getDescription()))
        .enabled(logisticsHandlingGroupEntity.getEnabled())
        .build();
  }
}
