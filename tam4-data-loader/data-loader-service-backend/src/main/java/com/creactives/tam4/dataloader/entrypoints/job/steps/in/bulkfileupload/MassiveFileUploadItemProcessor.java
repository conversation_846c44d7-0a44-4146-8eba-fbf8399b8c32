package com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload;

import com.creactives.tam4.messaging.Change;
import com.creactives.tam4.messaging.Changes;
import com.creactives.tam4.messaging.materials.commands.MasterdataChangesMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@Log4j2
@Component
public class MassiveFileUploadItemProcessor implements ItemProcessor<File, MasterdataChangesMessage> {

  public static final int SYSTEM_USER = 1;
  public static final String NAME = "defaultImage";
  private final AttachmentDataProvider attachmentDataProvider;
  public String client;

  @Override
  public MasterdataChangesMessage process(final File directory) throws Exception {
    final String client = directory.getParent().substring(directory.getParent().lastIndexOf(File.separator) + 1);
    setClient(client);
    final MasterdataChangesMessage masterdataChangesMessage = new MasterdataChangesMessage();
    checkForDefaultImages(masterdataChangesMessage, directory);
    checkForAttachments(masterdataChangesMessage, directory);
    return masterdataChangesMessage;
  }

  private void checkForDefaultImages(final MasterdataChangesMessage masterdataChangesMessage, final File directory) throws IOException {
    final String materialCode = directory.getName();
    final Path imagePath = Files.walk(Paths.get(directory.getPath())).filter(it -> it.getFileName().toString().equals(NAME)).findAny().orElse(null);
    if (imagePath != null) {
      try {
        final Optional<Path> defaultImageOptional = Files.walk(imagePath, 1).findFirst();
        if (defaultImageOptional.isPresent() && Objects.requireNonNull(defaultImageOptional.get().toFile().listFiles()).length > 0) {
          final File file = Objects.requireNonNull(defaultImageOptional.get().toFile().listFiles())[0];
          final UUID uuid = attachmentDataProvider.addAttachment(SYSTEM_USER, file, "image");
          final Changes change = getChanges(uuid);
          masterdataChangesMessage.setMaterialCode(materialCode);
          masterdataChangesMessage.setClient(client);
          masterdataChangesMessage.setChanges(change);
        }
      } catch (final Exception e) {
        log.info("Exception for material {} while uploading file.", materialCode);
        log.trace(e);
        log.catching(e);
      }
    } else {
      log.info("No image with name {} found for material {}.", NAME, materialCode);
    }
  }

  private void checkForAttachments(final MasterdataChangesMessage masterdataChangesMessage, final File directory) throws IOException {
    final String materialCode = directory.getName();
    final List<Change<UUID>> attachments = new ArrayList<>();
    Files.walk(Paths.get(directory.getPath()), 1).filter(Files::isRegularFile).forEach(it -> {
      final File attachment = it.toFile();
      try {
        final UUID uuid = attachmentDataProvider.addAttachment(SYSTEM_USER, attachment, "default");
        final Change<UUID> attachmentChange = new Change<>(null, uuid);
        attachments.add(attachmentChange);
      } catch (final Exception e) {
        log.info("Exception for material {} while uploading file.", materialCode);
        log.trace(e);
        log.catching(e);
      }
    });
    Files.walk(Paths.get(directory.getPath()), 1).filter(Files::isDirectory).filter(it -> !it.toFile().getName().equals(NAME)).forEach(it -> {
      final String categoryName = it.toFile().getName();
      if (!categoryName.equals(materialCode)) {
        try {
          Files.walk(it, 1).filter(Files::isRegularFile).forEach(attachment -> {
            try {
              final UUID uuid = attachmentDataProvider.addAttachment(SYSTEM_USER, attachment.toFile(), categoryName);
              final Change<UUID> attachmentChange = new Change<>(null, uuid);
              attachments.add(attachmentChange);
            } catch (final Exception e) {
              log.info("Exception for material {} while uploading file.", materialCode);
            }
          });
        } catch (final IOException e) {
          log.info("Exception for material {} while uploading file.", materialCode);
        }
      }
    });
    masterdataChangesMessage.setMaterialCode(materialCode);
    masterdataChangesMessage.setClient(client);

    final Changes changes = masterdataChangesMessage.getChanges();
    if (changes == null) {
      masterdataChangesMessage.setChanges(Changes.builder().attachments(attachments).ignoreOldValues(true).build());

    } else {
      changes.setAttachments(attachments);
      changes.setIgnoreOldValues(true);
      masterdataChangesMessage.setChanges(changes);
    }

  }

  private Changes getChanges(final UUID uuid) {
    final Change<UUID> imageChange = new Change<>(null, uuid);
    return Changes.builder().image(imageChange).build();
  }

  public void setClient(final String client) {
    this.client = client;
  }
}

