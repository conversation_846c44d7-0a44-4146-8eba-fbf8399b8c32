package com.creactives.tam4.dataloader.entrypoints.job.steps.out.supplierdefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierEntity;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Optional;
@RequiredArgsConstructor
@Service
public class SetSupplierSynchronizedFields implements ItemProcessor<SupplierEntity, SupplierEntity> {

  private final SupplierStagingRepository repository;

  @Override
  @Transactional
  public SupplierEntity process(final SupplierEntity supplierEntity) throws Exception {

    final Optional<SupplierEntity> registrationNumber = repository.findByClientAndCodeAndSynchronizationState(supplierEntity.getClient(),
        supplierEntity.getCode(),
        SyncStatus.PENDING.getCode());

    if (registrationNumber.isPresent()) {
      registrationNumber.get().setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
      registrationNumber.get().setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
    }

    return supplierEntity;
  }
}
