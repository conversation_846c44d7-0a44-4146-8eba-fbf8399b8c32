package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

/**
 * Created on 6/11/2019 4:41 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface MaterialPlantValuationStagingRepository extends PagingAndSortingRepository<MaterialPlantValuationEntity, Long> {

  List<MaterialPlantValuationEntity> findByClientAndMaterialCode(String client, String materialCode);

  List<MaterialPlantValuationEntity> findByClientAndMaterialCodeAndPlantIdIn(String client, String materialCode, Collection<String> plantId);

  List<MaterialPlantValuationEntity> findByClientAndMaterialCodeAndValuationType(String client, String materialCode, String valuationType);

  @Query(value = "UPDATE MaterialPlantValuationEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE synchronizationState = :stateOld AND id in (:ids)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("stateOld") String stateOld,
                   @Param("ids") List<Long> ids);


}
