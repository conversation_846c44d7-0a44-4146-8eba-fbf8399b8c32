package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.creactives.tam4.common.batch.SimpleJdbcPartitioner;
import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.dataloader.entrypoints.job.utils.WriteTimeLoggerListener;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ChunkListener;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.database.builder.JdbcCursorItemReaderBuilder;
import org.springframework.batch.item.support.CompositeItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class SendMaterialApplyExtensionRequestedV4Step {

  private final StepBuilderFactory stepBuilderFactory;
  private final ChunkWriterListener chunkWriterListener;
  private final TaskExecutor taskExecutor;
  private final DataSource dataSource;
  private final SendMaterialApplyExtensionRequestedV4Writer writer;

  @Bean(name = SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME)
  public Step getSendMaterialApplyExtensionRequestedStep(@Value("${creactives.tam4.batch.grid-size:8}") final int gridSize,
                                                         @Value("${data-loader.send-job.materials-extension-chunk-size:1000}") final int chunkSize,
                                                         @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PARTITIONER) final Partitioner partitioner,
                                                         @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_READER) final JdbcCursorItemReader<MaterialApplyExtensionRow> itemReader,
                                                         @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PROCESSOR) final ItemProcessor<MaterialApplyExtensionRow, MaterialApplyExtensionLoadMessage> itemProcessor,
                                                         @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITER) final CompositeItemWriter<MaterialApplyExtensionLoadMessage> itemWriter,
                                                         @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITELISTENER) final ChunkListener loggerListener) {
    final AbstractTaskletStepBuilder<SimpleStepBuilder<MaterialApplyExtensionRow, MaterialApplyExtensionLoadMessage>> builder = stepBuilderFactory.get(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME)
        .<MaterialApplyExtensionRow, MaterialApplyExtensionLoadMessage>chunk(chunkSize)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(itemWriter)
        .listener(chunkWriterListener)
        .listener(loggerListener);

    return stepBuilderFactory.get(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PARTITIONER)
        .partitioner(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME, partitioner)
        .step(builder.build())
        .gridSize(gridSize)
        .taskExecutor(taskExecutor)
        .build();
  }

  @Bean(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PARTITIONER)
  @JobScope
  public Partitioner partitioner(@Value("${data-loader.send-job.materials-extension-partition-size:10000}") final int partitionSize,
                                 @Value("#{jobExecution}") final JobExecution jobExecution,
                                 @Value("#{jobParameters['clientCode']}") final String clientCode,
                                 @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {

    final Map<String, Object> params = new HashMap<>();
    params.put("jobExecutionId", jobExecution.getJobId());
    params.put("clientCode", clientCode);
    params.put("ignoreStatus", ignoreStatus);

    final String _ignoreStatus = DataLoaderUtils.getIgnoreStatus(ignoreStatus);
    if (StringUtils.isNotBlank(_ignoreStatus)) {
      params.put("synchronizationState", _ignoreStatus);
    }

    return SimpleJdbcPartitioner.builder()
        .jdbcTemplate(new NamedParameterJdbcTemplate(dataSource))
        .qryForKeys(SendMaterialApplyExtensionRequestedV4Constants.getPartitionerQuery(clientCode, ignoreStatus, true))
        .params(params)
        .partitionSize(partitionSize)
        .stringKey(true)
        .build();
  }

  @Bean(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_READER)
  @StepScope
  public JdbcCursorItemReader<MaterialApplyExtensionRow> reader(@Value("${data-loader.send-job.materials-chunk-size:1000}") final int fetchSize,
                                                                @Value("#{stepExecution}") final StepExecution stepExecution,
                                                                @Value("#{jobParameters['clientCode']}") final String clientCode,
                                                                @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    return new JdbcCursorItemReaderBuilder<MaterialApplyExtensionRow>()
        .name(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_READER)
        .dataSource(dataSource)
        .preparedStatementSetter(DataLoaderUtils.initPrepareStatementSetter(clientCode, ignoreStatus, stepExecution, true))
        .rowMapper(new MaterialApplyExtensionRowMapper())
        .fetchSize(fetchSize)
        .sql(SendMaterialApplyExtensionRequestedV4Constants.getChunkQuery(clientCode, ignoreStatus))
        .build();
  }


  @Bean(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITER)
  @StepScope
  public CompositeItemWriter<MaterialApplyExtensionLoadMessage> buildAsyncWriter() {
    return new CompositeItemWriterBuilder<MaterialApplyExtensionLoadMessage>().delegates(List.of(writer,
        updateStagingTable("materials_plant_data_staging", "material_code"),
        updateStagingTable("material_storage_locations_staging", "material_code")
    )).build();
  }


  private JdbcBatchItemWriter<MaterialApplyExtensionLoadMessage> updateStagingTable(final String tableName, final String matCodeColumn) {
    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getMaterialCode());
    });
  }

  @Bean(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITELISTENER)
  @StepScope
  public ChunkListener itemWriteListener(@Value("#{stepExecution}") final StepExecution stepExecution) {
    return new WriteTimeLoggerListener<MaterialApplyExtensionLoadMessage>(stepExecution, "info");
  }
}
