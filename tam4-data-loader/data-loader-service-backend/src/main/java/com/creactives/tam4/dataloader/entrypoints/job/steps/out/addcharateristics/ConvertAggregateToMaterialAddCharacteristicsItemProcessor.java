package com.creactives.tam4.dataloader.entrypoints.job.steps.out.addcharateristics;

import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.AggregateDataForMaterialCreated;
import com.creactives.tam4.messaging.materials.commands.MaterialAddCharacteristicsMessage;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * Created on 6/12/2019 7:19 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class ConvertAggregateToMaterialAddCharacteristicsItemProcessor implements
    ItemProcessor<AggregateDataForMaterialCreated, MaterialAddCharacteristicsMessage> {

  private final MaterialCodeNormalizer materialCodeNormalizer;


  @Override
  @SneakyThrows
  public MaterialAddCharacteristicsMessage process(final AggregateDataForMaterialCreated aggregate) {
    final MaterialEntity materialEntity = aggregate.getMaterialEntity();
    final Map<String, String> externalAttributes = new HashMap<>();
    aggregate.getCharacteristics().asMap()
        .forEach((key, value) -> externalAttributes.put(key, StringUtils.join(value, ", ")));

    return MaterialAddCharacteristicsMessage.builder()
        .client(materialEntity.getClient())
        .materialCode(materialCodeNormalizer.removeZeroes(materialEntity.getMaterialCode()))
        .externalAttributes(externalAttributes)
        .build();
  }


}
