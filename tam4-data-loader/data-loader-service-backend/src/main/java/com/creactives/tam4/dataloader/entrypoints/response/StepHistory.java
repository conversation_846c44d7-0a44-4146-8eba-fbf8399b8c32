package com.creactives.tam4.dataloader.entrypoints.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StepHistory {

  private String stepName;
  private int readCount = 0;
  private int writeCount = 0;
  private Date startTime;
  private Date endTime;
  private String exitStatus;
  private String exceptions;

}
