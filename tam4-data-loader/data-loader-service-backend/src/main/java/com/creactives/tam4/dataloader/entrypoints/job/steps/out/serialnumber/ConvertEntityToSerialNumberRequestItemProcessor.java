package com.creactives.tam4.dataloader.entrypoints.job.steps.out.serialnumber;


import com.creactives.tam4.dataloader.dataproviders.database.serialnumber.SerialNumberManagementEntity;
import com.creactives.tam4.messaging.materials.commands.SerialNumberProfileValueUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToSerialNumberRequestItemProcessor implements ItemProcessor<SerialNumberManagementEntity, SerialNumberProfileValueUpsertRequestMessage> {


  public ConvertEntityToSerialNumberRequestItemProcessor() {

  }

  @Override
  public SerialNumberProfileValueUpsertRequestMessage process(final SerialNumberManagementEntity serialNumberManagementEntity) throws Exception {
    return SerialNumberProfileValueUpsertRequestMessage.builder()
        .client(serialNumberManagementEntity.getClient())
        .serialNumberProfile(serialNumberManagementEntity.getSerialNumberProfile())
        .build();
  }
}
