package com.creactives.tam4.dataloader.entrypoints.job.steps.out.serialnumber;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.SerialNumberProfileValueUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
@RequiredArgsConstructor
@Component
public class SerialNumberWriter implements ItemWriter<SerialNumberProfileValueUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends SerialNumberProfileValueUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
