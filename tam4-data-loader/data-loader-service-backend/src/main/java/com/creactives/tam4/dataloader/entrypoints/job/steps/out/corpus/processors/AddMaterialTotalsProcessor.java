package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.core.exceptions.MissingCurrencyException;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.GetPlantCurrency;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.MaterialPrices;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.valuation.MaterialPriceDetails;
import com.google.common.collect.Maps;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class AddMaterialTotalsProcessor implements ItemProcessor<Material, Material> {

  public static final String DEFAULT_VALUATION = "";
  private final MaterialPlantValuationStagingRepository materialPlantValuationStagingRepository;
  private final ConsumptionDataStagingRepository consumptionDataStagingRepository;
  private final POHistoryStagingRepository poHistoryStagingRepository;
  private final GetPlantCurrency getPlantCurrency;
  private final CurrencyConverter currencyConverter;

  public AddMaterialTotalsProcessor(final MaterialPlantValuationStagingRepository materialPlantValuationStagingRepository,
                                    final ConsumptionDataStagingRepository consumptionDataStagingRepository,
                                    final POHistoryStagingRepository poHistoryStagingRepository,
                                    final GetPlantCurrency getPlantCurrency,
                                    final CurrencyConverter currencyConverter) {
    this.materialPlantValuationStagingRepository = materialPlantValuationStagingRepository;
    this.consumptionDataStagingRepository = consumptionDataStagingRepository;
    this.poHistoryStagingRepository = poHistoryStagingRepository;
    this.getPlantCurrency = getPlantCurrency;
    this.currencyConverter = currencyConverter;
  }

  private static Double getTotalStockQuantity(final Collection<MaterialPlantValuationEntity> valuationEntities) {
    final List<MaterialPlantValuationEntity> valuations = valuationEntities.stream()
        .filter(AddMaterialTotalsProcessor::isDefaultValuationType)
        .collect(Collectors.toList());

    BigDecimal stockSum = BigDecimal.ZERO;
    for (final MaterialPlantValuationEntity materialPlantValuationEntity : valuations) {
      if (materialPlantValuationEntity != null && materialPlantValuationEntity.getTotalValue() != null) {
        stockSum = stockSum.add(materialPlantValuationEntity.getInventoryAmount());
      }
    }
    return stockSum.doubleValue();
  }

  private static boolean isDefaultValuationType(final MaterialPlantValuationEntity valuationEntity) {
    return MaterialPriceDetails.isDefaultValuationType(valuationEntity.getValuationType());
  }

  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material material) throws Exception {
    final List<MaterialPlantValuationEntity> valuationEntities = getDefaultValuationEntities(material);
    final List<ConsumptionDataEntity> consumptionEntities = consumptionDataStagingRepository.findAllByClientAndMaterialCode(material.getClient(), material.getMaterialCode());
    final List<POHistoryEntity> poHistoryEntities = poHistoryStagingRepository.findByClientAndMaterialCode(material.getClient(), material.getMaterialCode());

    final Double totalStockQuantity = getTotalStockQuantity(valuationEntities);
    final Map<String, Double> totalStockByCurrency = getTotalStockByCurrency(valuationEntities);
    final Double totalConsumptionQuantity = getTotalConsumptionQuantity(consumptionEntities);
    final Map<String, Double> totalConsumptionByCurrency = getTotalConsumptionByCurrency(consumptionEntities);
    final Double totalOrderedQuantity = getTotalOrderedQuantity(poHistoryEntities);
    final Map<String, Double> totalOrderedByCurrency = getTotalOrderedByCurrency(poHistoryEntities);
    final List<BigDecimal> pricesInEUR = getPrices(valuationEntities);

    return Material.from(material)
        .totalStockQuantity(totalStockQuantity)
        .totalStockAmount(totalStockByCurrency)
        .totalConsumptionQuantity(totalConsumptionQuantity)
        .totalConsumptionAmount(totalConsumptionByCurrency)
        .totalPOQuantity(totalOrderedQuantity)
        .totalPOAmount(totalOrderedByCurrency)
        .prices(new MaterialPrices(pricesInEUR))
        .build();
  }

  private List<BigDecimal> getPrices(final Collection<MaterialPlantValuationEntity> valuationEntities) {
    return valuationEntities.stream()
        .map(this::getConvertedStandardPrice)
        .collect(Collectors.toList());
  }

  private BigDecimal getConvertedStandardPrice(final MaterialPlantValuationEntity valuation) {
    final PlantKey plantKey = new PlantKey(valuation.getPlantId(), valuation.getClient());
    final String currency = getPlantCurrency.getPlantCurrencies().get(plantKey);
    if (currency == null) {
      throw new MissingCurrencyException(plantKey);
    }
    final BigDecimal originalStandardPrice = valuation.getStandardPrice();
    if (originalStandardPrice != null) {
      return currencyConverter.convertToDefaultCurrency(currency, originalStandardPrice);
    } else {
      return BigDecimal.ZERO;
    }
  }

  private Map<String, Double> getTotalStockByCurrency(final Collection<MaterialPlantValuationEntity> valuationEntities) {
    final Map<String, List<MaterialPlantValuationEntity>> valuationsByCurrency = valuationEntities.stream()
        .filter(AddMaterialTotalsProcessor::isDefaultValuationType)
        .collect(Collectors.groupingBy(plantValuation ->
                getCurrencyForPlant(Objects.requireNonNull(plantValuation.getClient(), "Plant valuation row without client: " + plantValuation),
                    Objects.requireNonNull(plantValuation.getPlantId(), "Plant valuation row without plant: " + plantValuation)
                )
            )
        );

    final Map<String, Double> stockTotalsByCurrency = Maps.newHashMap();
    for (final Map.Entry<String, List<MaterialPlantValuationEntity>> entry : valuationsByCurrency.entrySet()) {
      BigDecimal stockSum = BigDecimal.ZERO;
      for (final MaterialPlantValuationEntity materialPlantValuationEntity : entry.getValue()) {
        if (materialPlantValuationEntity != null && materialPlantValuationEntity.getTotalValue() != null) {
          stockSum = stockSum.add(materialPlantValuationEntity.getTotalValue());
        }
      }
      stockTotalsByCurrency.put(entry.getKey(), stockSum.doubleValue());
    }
    return stockTotalsByCurrency;
  }

  private List<MaterialPlantValuationEntity> getDefaultValuationEntities(final Material item) {
    return materialPlantValuationStagingRepository
        .findByClientAndMaterialCodeAndValuationType(item.getClient(),
            item.getMaterialCode(),
            DEFAULT_VALUATION
        );
  }

  private Map<String, Double> getTotalConsumptionByCurrency(final List<ConsumptionDataEntity> consumptionEntities) {
    final Map<String, Double> totalConsumptionsByCurrency = Maps.newHashMap();
    final Map<String, List<ConsumptionDataEntity>> consumptionsByCurrency = consumptionEntities.stream()
        .collect(Collectors.groupingBy(this::getConsumptionCurrencyCode));
    for (final Map.Entry<String, List<ConsumptionDataEntity>> entry : consumptionsByCurrency.entrySet()) {
      BigDecimal consumptionSum = BigDecimal.ZERO;
      for (final ConsumptionDataEntity consumptionDataEntity : entry.getValue()) {
        consumptionSum = consumptionSum.add(consumptionDataEntity.getConsumptionAmount());
      }
      totalConsumptionsByCurrency.put(entry.getKey(), consumptionSum.doubleValue());
    }
    return totalConsumptionsByCurrency;
  }

  private Double getTotalConsumptionQuantity(final List<ConsumptionDataEntity> consumptionEntities) {
    if (consumptionEntities == null) {
      return null;
    }
    BigDecimal consumptionSum = BigDecimal.ZERO;
    for (final ConsumptionDataEntity consumptionDataEntity : consumptionEntities) {
      consumptionSum = consumptionSum.add(consumptionDataEntity.getConsumptionQuantity());
    }
    return consumptionSum.doubleValue();
  }

  private Map<String, Double> getTotalOrderedByCurrency(final List<POHistoryEntity> poHistoryEntities) {
    final Map<String, Double> totalOrderedByCurrency = Maps.newHashMap();
    final Map<String, List<POHistoryEntity>> orderedByCurrency = poHistoryEntities.stream()
        .collect(Collectors.groupingBy(this::getPoCurrencyCode));
    for (final Map.Entry<String, List<POHistoryEntity>> entry : orderedByCurrency.entrySet()) {
      BigDecimal orderedSum = BigDecimal.ZERO;
      for (final POHistoryEntity consumptionDataEntity : entry.getValue()) {
        orderedSum = orderedSum.add(consumptionDataEntity.getOrderedAmount());
      }
      totalOrderedByCurrency.put(entry.getKey(), orderedSum.doubleValue());
    }
    return totalOrderedByCurrency;
  }

  private Double getTotalOrderedQuantity(final List<POHistoryEntity> poHistoryEntities) {
    if (poHistoryEntities == null) {
      return null;
    }
    BigDecimal orderedSum = BigDecimal.ZERO;
    for (final POHistoryEntity poHistoryEntity : poHistoryEntities) {
      orderedSum = orderedSum.add(poHistoryEntity.getOrderedQuantity());
    }
    return orderedSum.doubleValue();
  }

  private String getPoCurrencyCode(final POHistoryEntity po) {
    final String client = Objects.requireNonNull(po.getClient(), "PO history row without client: " + po);
    final String plantCode = Objects.requireNonNull(po.getPlantCode(), "PO history row without plant: " + po);
    return Optional.ofNullable(po.getCurrency())
        .orElse(getCurrencyForPlant(client, plantCode));
  }

  private String getConsumptionCurrencyCode(final ConsumptionDataEntity consumption) {
    final String client = Objects.requireNonNull(consumption.getPlantCode(), "Consumption row without client: " + consumption);
    final String plantCode = Objects.requireNonNull(consumption.getPlantCode(), "Consumption row without plant: " + consumption);
    return Optional.ofNullable(consumption.getCurrency())
        .orElse(getCurrencyForPlant(client, plantCode));
  }

  private String getCurrencyForPlant(final String client, final String plantCode) {
    return Objects.requireNonNullElseGet(getPlantCurrency.getPlantCurrencies().get(new PlantKey(plantCode, client)), String::new); //why empty string?
  }

}
