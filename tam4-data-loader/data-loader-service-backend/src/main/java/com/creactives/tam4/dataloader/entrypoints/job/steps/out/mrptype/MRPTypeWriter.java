package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrptype;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MRPTypeUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MRPTypeWriter implements ItemWriter<MRPTypeUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  public MRPTypeWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;

  }

  @Override
  public void write(final List<? extends MRPTypeUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
