package com.creactives.tam4.dataloader.entrypoints.job.steps.out.externalmaterialgroup;


import com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup.DefinitionExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup.DefinitionExternalMaterialGroupStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.messaging.materials.commands.ExternalMaterialGroupUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class ConvertEntityToExternalMaterialGroupRequestItemProcessor implements ItemProcessor<ExternalMaterialGroupEntity, ExternalMaterialGroupUpsertRequestMessage> {


  private final DefinitionExternalMaterialGroupStagingRepository definitionExternalMaterialGroupStagingRepository;

  @Override
  public ExternalMaterialGroupUpsertRequestMessage process(final ExternalMaterialGroupEntity externalMaterialGroupEntity) throws Exception {
    final List<DefinitionExternalMaterialGroupEntity> definitionExternalMaterialGroupEntities = definitionExternalMaterialGroupStagingRepository
        .findByClientAndExternalMaterialGroup(externalMaterialGroupEntity.getClient(), externalMaterialGroupEntity.getExternalMaterialGroup());

    return ExternalMaterialGroupUpsertRequestMessage.builder()
        .client(externalMaterialGroupEntity.getClient())
        .externalMaterialGroup(externalMaterialGroupEntity.getExternalMaterialGroup())
        .descriptions(definitionExternalMaterialGroupEntities.stream()
            .filter(definitionExternalMaterialGroupEntity -> definitionExternalMaterialGroupEntity.getLanguage() != null)
            .collect(Collectors.toMap(DefinitionExternalMaterialGroupEntity::getLanguage,
                    DefinitionExternalMaterialGroupEntity::getDescription
                )
            )
        )
        .enabled(externalMaterialGroupEntity.getEnabled())
        .build();
  }
}
