package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForMaterialPlantValuation {

  private String client;

  private String materialCode;

  private List<MaterialPlantValuationRow> materialPlantValuation;
  private List<POHistoryRow> poHistory;
  private List<ConsumptionDataRow> consumptionData;
  private List<PlantRow> plants;
  private List<PlantCurrencyRow> plantCurrencies;

}
