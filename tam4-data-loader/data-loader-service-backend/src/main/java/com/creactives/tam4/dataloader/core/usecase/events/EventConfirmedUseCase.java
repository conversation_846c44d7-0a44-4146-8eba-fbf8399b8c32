package com.creactives.tam4.dataloader.core.usecase.events;

import com.creactives.tam4.dataloader.dataproviders.database.events.EventsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.events.EventsStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
@RequiredArgsConstructor
@Service
public class EventConfirmedUseCase {

  private final EventsStagingRepository eventsStagingRepository;

  public void updateEvent(final String externalCorrelationId, final long confirmedDate) {
    final EventsEntity entity = eventsStagingRepository.findByCorrelationId(externalCorrelationId);
    entity.setSynchronizationConfirmedOn(new Timestamp(confirmedDate));
    eventsStagingRepository.save(entity);
  }
}
