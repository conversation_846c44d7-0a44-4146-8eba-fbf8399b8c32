package com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MasterdataChangesMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Log4j2
@Service
public class MaterialSnapshotProvider implements ItemWriter<MasterdataChangesMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends MasterdataChangesMessage> list) {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
