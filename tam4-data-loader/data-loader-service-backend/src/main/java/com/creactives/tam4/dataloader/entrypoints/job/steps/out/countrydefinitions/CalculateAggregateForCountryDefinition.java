package com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountriesDefinitionStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Log4j2
@Component
@StepScope
public class CalculateAggregateForCountryDefinition implements ItemProcessor<CountryDefinitionsKeyEntity, AggregateDataForCountryDefinition> {

  private final CountriesDefinitionStagingRepository countriesDefinitionStagingRepository;

  private final boolean ignoreStatus;


  public CalculateAggregateForCountryDefinition(final CountriesDefinitionStagingRepository countriesDefinitionStagingRepository,
                                                @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.countriesDefinitionStagingRepository = countriesDefinitionStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  public AggregateDataForCountryDefinition process(final CountryDefinitionsKeyEntity countryDefinitionsKeyEntity) throws Exception {
    final List<CountryDefinitionsEntity> countriesStagingEntities = ignoreStatus ?
        countriesDefinitionStagingRepository.findByClientAndCountry(countryDefinitionsKeyEntity.getClient(),
            countryDefinitionsKeyEntity.getCountry())
        : countriesDefinitionStagingRepository.findByClientAndCountryAndSynchronizationState(countryDefinitionsKeyEntity.getClient(),
        countryDefinitionsKeyEntity.getCountry(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(countriesStagingEntities)) {
      return null;
    }
    return AggregateDataForCountryDefinition.builder()
        .countryDefinitionsKeyEntity(countryDefinitionsKeyEntity)
        .countryEntities(countriesStagingEntities).build();
  }

  private boolean haveAlreadyBeenSent(final List<CountryDefinitionsEntity> entities) {
    return !CollectionUtils.isEmpty(entities) &&
        entities.stream().noneMatch(e -> SyncStatus.PENDING.getCode().equals(e.getSynchronizationState()));
  }
}
