package com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QMControlDefinitionsStagingRepository extends PagingAndSortingRepository<QMControlDefinitionsEntity, Long> {

  List<QMControlDefinitionsEntity> findByClientAndQmControlKey(String client, String qmControlKey);


  List<QMControlDefinitionsEntity> findByClientAndLanguageAndQmControlKeyIn(String client, String language, List<String> qmControlKeys);

  List<QMControlDefinitionsEntity> findByClientAndQmControlKeyIn(String client, List<String> keys);
}
