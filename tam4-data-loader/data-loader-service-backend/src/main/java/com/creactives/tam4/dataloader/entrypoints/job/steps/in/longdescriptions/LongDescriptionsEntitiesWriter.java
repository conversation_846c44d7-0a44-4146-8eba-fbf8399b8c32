package com.creactives.tam4.dataloader.entrypoints.job.steps.in.longdescriptions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Repository;

import java.util.List;

@RequiredArgsConstructor
@Repository
@StepScope
public class LongDescriptionsEntitiesWriter implements ItemWriter<LongDescriptionEntity> {

  private final LongDescriptionStagingRepository longDescriptionStagingRepository;

  @Override
  public void write(final List<? extends LongDescriptionEntity> list) throws Exception {
    for (final LongDescriptionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    longDescriptionStagingRepository.saveAll(list);
  }
}
