package com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn;

import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "characteristics_cabn_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CharacteristicsCABNEntity implements ClientEnrichableEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "characteristics_cabn_staging_generator")
  @SequenceGenerator(name = "characteristics_cabn_staging_generator", sequenceName = "characteristics_cabn_staging_seq", allocationSize = 1000)
  private long id;

  @Column
  @FieldDoc(sapField = "ATINN", description = "Internal characteristic", required = true)
  private String code;

  @Column
  @FieldDoc(sapField = "MANDT", referenceTable = "T000", description = "Client", required = true)
  private String client;

  @Column
  @FieldDoc(sapField = "ATNAM", description = "Characteristic Name", required = true)
  private String name;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
