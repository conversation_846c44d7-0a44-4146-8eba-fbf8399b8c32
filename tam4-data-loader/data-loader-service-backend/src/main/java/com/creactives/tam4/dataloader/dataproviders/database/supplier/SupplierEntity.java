package com.creactives.tam4.dataloader.dataproviders.database.supplier;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 4:10 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */

@Table(name = "suppliers_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SupplierEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "suppliers_definitions_staging_generator")
  @SequenceGenerator(name = "suppliers_definitions_staging_generator", sequenceName = "suppliers_definitions_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "code")
  private String code;

  @Column(name = "vat_registration_number")
  private String vatRegistrationNumber;

  @Column(name = "name")
  private String name;

  @Column(name = "name2")
  private String name2;

  @Column(name = "name3")
  private String name3;

  @Column(name = "name4")
  private String name4;

  @Column(name = "address")
  private String address;

  @Column(name = "supplier_name_normalized")
  private String supplierNameNormalized;

  @Column(name = "supplier_group_normalized")
  private String supplierGroupNormalized;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "created_on", insertable = false, updatable = false)
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
