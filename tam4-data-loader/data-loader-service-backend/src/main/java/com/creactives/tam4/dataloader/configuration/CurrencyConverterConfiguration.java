package com.creactives.tam4.dataloader.configuration;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
@ConfigurationProperties(prefix = "data-loader.currency-rates")
@Data
public class CurrencyConverterConfiguration {
  private String defaultCurrency = "EUR";
  private boolean loggedMissingCurrency = false;
  private boolean assumeDefaultCurrencyWhenMissing = true;

  public String getDefaultCurrency() {
    if (defaultCurrency == null) {
      if (!loggedMissingCurrency) {
        loggedMissingCurrency = true;
        log.warn("Default currency is null, replacing it with EUR.");
      }
      defaultCurrency = "EUR";
    }
    return defaultCurrency;
  }
}
