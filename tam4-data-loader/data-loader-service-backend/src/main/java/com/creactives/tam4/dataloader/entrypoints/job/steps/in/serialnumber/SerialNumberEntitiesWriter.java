package com.creactives.tam4.dataloader.entrypoints.job.steps.in.serialnumber;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.serialnumber.SerialNumberManagementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.serialnumber.SerialNumberManagementStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class SerialNumberEntitiesWriter implements ItemWriter<SerialNumberManagementEntity> {

  private final SerialNumberManagementStagingRepository serialNumberManagementStagingRepository;

  @Override
  public void write(final List<? extends SerialNumberManagementEntity> list) throws Exception {
    for (final SerialNumberManagementEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    serialNumberManagementStagingRepository.saveAll(list);
  }
}
