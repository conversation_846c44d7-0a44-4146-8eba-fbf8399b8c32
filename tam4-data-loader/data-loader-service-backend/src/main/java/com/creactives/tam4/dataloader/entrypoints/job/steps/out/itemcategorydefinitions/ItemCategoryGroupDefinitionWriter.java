package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.ItemCategoryGroupDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
@RequiredArgsConstructor

@Log4j2
@Component
public class ItemCategoryGroupDefinitionWriter implements ItemWriter<ItemCategoryGroupDefinitionUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends ItemCategoryGroupDefinitionUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }

}
