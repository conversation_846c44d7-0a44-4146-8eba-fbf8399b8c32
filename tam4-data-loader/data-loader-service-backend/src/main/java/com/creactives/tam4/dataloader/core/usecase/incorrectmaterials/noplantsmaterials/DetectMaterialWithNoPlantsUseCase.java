package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noplantsmaterials;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialWithNoPlantsUseCase {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialWithNoPlantsUseCase(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoPlants(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-plants")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoPlants(client))
        .build();
  }
}
