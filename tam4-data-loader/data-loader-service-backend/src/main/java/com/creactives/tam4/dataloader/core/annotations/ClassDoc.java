package com.creactives.tam4.dataloader.core.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Date: 1/18/2021 Time: 9:15 AM
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ClassDoc {

  String sapTable();

  String description() default "";

  String additionalDescription() default "";
}
