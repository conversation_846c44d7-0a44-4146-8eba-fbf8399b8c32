package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials;

import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GetTotalMaterialsUseCase {

  private final GetTotalMaterialsInDatabase getTotalMaterialsInDatabase;

  public DataAnalyseResponse totalMaterials(final String client) {
    return DataAnalyseResponse.builder()
        .code("total-materials")
        .rowCount(getTotalMaterialsInDatabase.totalMaterials(client))
        .build();
  }

}
