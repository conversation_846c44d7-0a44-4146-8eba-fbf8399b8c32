package com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers;


import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "mrp_controller_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T024D", description = "MRP controllers")
public class MRPControllersEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "mrp_controller_staging_generator")
  @SequenceGenerator(name = "mrp_controller_staging_generator", sequenceName = "mrp_controller_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "plant")
  @FieldDoc(required = true, sapField = "WERKS", referenceTable = "T001W", description = "Plant")
  private String plant;

  @Column(name = "mrp_controller")
  @FieldDoc(required = true, sapField = "DISPO", description = "MRP Controller (Materials Planner)")
  private String mrpController;

  @Column(name = "mrp_controller_name")
  @FieldDoc(required = true, sapField = "DSNAM", description = "Name of MRP controller")
  private String mrpControllerName;

  @Column(name = "purchasing_group")
  @FieldDoc(sapField = "EKGRP", referenceTable = "T024", description = "Purchasing Group")
  private String purchasingGroup;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
