package com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LotSizeDefinitionStagingRepository extends PagingAndSortingRepository<LotSizeDefinitionEntity, Long> {

  List<LotSizeDefinitionEntity> findByClientAndLotSize(String client, String lotSize);

  List<LotSizeDefinitionEntity> findByClientAndLanguageAndLotSizeIn(String client, String language, List<String> lotSize);

  Page<LotSizeDefinitionEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<LotSizeDefinitionEntity> findAllByClient(String client, Pageable pageable);

  Page<LotSizeDefinitionEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
