package com.creactives.tam4.dataloader.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Objects;

@ConfigurationProperties("export.include")
public class IncludeConfiguration {
  private boolean materialGroupDescription;
  private boolean shortDescriptions;
  private boolean longDescriptionsPO;
  private boolean longDescriptions;
  private boolean internalNoteDescriptions;
  private boolean inspectionDescriptions;
  private boolean supplierName;
  private boolean materialPlants;
  private boolean materialWarehouses;
  private boolean materialTotals;
  private boolean characteristics;


  public boolean isLongDescriptions() {
    return longDescriptions;
  }

  public void setLongDescriptions(final boolean longDescriptions) {
    this.longDescriptions = longDescriptions;
  }

  public boolean isInternalNoteDescriptions() {
    return internalNoteDescriptions;
  }

  public void setInternalNoteDescriptions(final boolean internalNoteDescriptions) {
    this.internalNoteDescriptions = internalNoteDescriptions;
  }

  public boolean isInspectionDescriptions() {
    return inspectionDescriptions;
  }

  public void setInspectionDescriptions(final boolean inspectionDescriptions) {
    this.inspectionDescriptions = inspectionDescriptions;
  }


  public boolean isMaterialGroupDescription() {
    return materialGroupDescription;
  }

  public void setMaterialGroupDescription(final boolean materialGroupDescription) {
    this.materialGroupDescription = materialGroupDescription;
  }

  public boolean isShortDescriptions() {
    return shortDescriptions;
  }

  public void setShortDescriptions(final boolean shortDescriptions) {
    this.shortDescriptions = shortDescriptions;
  }

  public boolean isLongDescriptionsPO() {
    return longDescriptionsPO;
  }

  public void setLongDescriptionsPO(final boolean longDescriptionsPO) {
    this.longDescriptionsPO = longDescriptionsPO;
  }

  public boolean isSupplierName() {
    return supplierName;
  }

  public void setSupplierName(final boolean supplierName) {
    this.supplierName = supplierName;
  }

  public boolean isMaterialPlants() {
    return materialPlants;
  }

  public void setMaterialPlants(final boolean materialPlants) {
    this.materialPlants = materialPlants;
  }

  public boolean isMaterialTotals() {
    return materialTotals;
  }

  public void setMaterialTotals(final boolean materialTotals) {
    this.materialTotals = materialTotals;
  }

  public boolean isCharacteristics() {
    return characteristics;
  }

  public void setCharacteristics(final boolean characteristics) {
    this.characteristics = characteristics;
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    final IncludeConfiguration that = (IncludeConfiguration) o;
    return materialGroupDescription == that.materialGroupDescription &&
        shortDescriptions == that.shortDescriptions &&
        longDescriptionsPO == that.longDescriptionsPO &&
        supplierName == that.supplierName &&
        materialPlants == that.materialPlants &&
        materialTotals == that.materialTotals &&
        characteristics == that.characteristics;
  }

  @Override
  public int hashCode() {
    return Objects.hash(materialGroupDescription, shortDescriptions, longDescriptionsPO, supplierName, materialPlants, materialTotals, characteristics);
  }

  @Override
  public String toString() {
    return "IncludeConfiguration{" +
        "materialGroupDescription=" + materialGroupDescription +
        ", shortDescriptions=" + shortDescriptions +
        ", longDescriptionsPO=" + longDescriptionsPO +
        ", supplierName=" + supplierName +
        ", materialPlants=" + materialPlants +
        ", materialTotals=" + materialTotals +
        ", characteristics=" + characteristics +
        '}';
  }

  public boolean isMaterialWarehouses() {
    return materialWarehouses;
  }

  public void setMaterialWarehouses(final boolean materialWarehouses) {
    this.materialWarehouses = materialWarehouses;
  }
}
