package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsEntity;
import com.creactives.tam4.messaging.materials.commands.ItemCategoryGroupDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Log4j2
@Component
public class ConvertAggregateToItemCategoryGroupDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForItemCategoryGroupDefinition, ItemCategoryGroupDefinitionUpsertRequestMessage> {

  @Override
  public ItemCategoryGroupDefinitionUpsertRequestMessage process(final AggregateDataForItemCategoryGroupDefinition aggregateDataForItemCategoryGroupDefinition) throws Exception {
    return ItemCategoryGroupDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForItemCategoryGroupDefinition.getItemCategoryGroupDefinitionsKeyEntity().getClient())
        .itemCategoryGroup(aggregateDataForItemCategoryGroupDefinition.getItemCategoryGroupDefinitionsKeyEntity().getItemCategoryGroup())
        .descriptions(aggregateDataForItemCategoryGroupDefinition.getItemCategoryGroupEntities().stream()
            .filter(itemCategoryGroupEntity -> itemCategoryGroupEntity.getLanguage() != null)
            .collect(Collectors.toMap(ItemCategoryGroupDefinitionsEntity::getLanguage,
                ItemCategoryGroupDefinitionsEntity::getDescription
            ))
        )
        .enabled(aggregateDataForItemCategoryGroupDefinition.getItemCategoryGroupDefinitionsKeyEntity().getEnabled())
        .build();
  }
}
