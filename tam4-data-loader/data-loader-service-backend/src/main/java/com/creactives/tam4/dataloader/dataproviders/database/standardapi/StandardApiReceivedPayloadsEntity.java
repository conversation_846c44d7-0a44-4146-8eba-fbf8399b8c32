package com.creactives.tam4.dataloader.dataproviders.database.standardapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Table(name = "standard_api_received_payloads")
@Entity
// lombok annotations
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StandardApiReceivedPayloadsEntity implements Serializable {

  private static final long serialVersionUID = -7178224468108854475L;

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "standard_api_received_payloads_id_seq")
  @SequenceGenerator(name = "standard_api_received_payloads_id_seq", sequenceName = "standard_api_received_payloads_id_seq", allocationSize = 1000)
  private Long id;

  @Embedded
  private PayloadEmbeddedId payloadEmbeddedId;

//  @Column(name = "message_type", insertable = false, updatable = false)
//  private String messageType;
//
//  @Column(name = "record_key", insertable = false, updatable = false)
//  private String key;
//
//  @Column(name = "payload_hash", insertable = false, updatable = false)
//  private String hash;

  @Column(name = "payload_content")
  @ToString.Exclude
  @EqualsAndHashCode.Exclude
  private String payload;

  @Column(name = "first_received_tms")
  private Date firstReceivedTms;

  @Column(name = "received_counter")
  private int receivedCounter;

  @Column(name = "last_received_tms")
  private Date lastReceivedTms;


  @Transient
  public void updateCounter() {
    this.receivedCounter++;
    this.lastReceivedTms = new Date();
  }
}
