package com.creactives.tam4.dataloader.entrypoints.job;

import lombok.Data;

/**
 * Date: 2/25/2021 Time: 1:35 PM
 */
@Data
public class JobMetadata {
  private final String name;
  private String description;
  private String table;
  private String category;

  public JobMetadata(final String name) {
    this.name = name;
    this.category = guessCategory(name);
  }

  public static String guessCategory(final String name) {
    if ("avato-warnings".equals(name) || name.startsWith("sim-")) {
      return "warnings";
    }
    if (name.startsWith("avato-")) {
      return "avato";
    }
    if (name.startsWith("excel-")) {
      return "excel";
    }
    if (name.startsWith("send-")) {
      return "outbound";
    }
    return "others";
  }

  /*
    classifier(name: string): string {
    if ( ['avato-warnings', 'sim-incorrect-materials', 'sim-missing-mg'].indexOf(name) >= 0 ) return 'warnings';
    if ( name.startsWith('avato-') ) return 'avato';
    if ( name.startsWith('excel-') ) return 'excel';
    if ( name.startsWith('send-') ) return 'outbound';
    return 'others';
  }

   */
}
