package com.creactives.tam4.dataloader.core.usecase.events;

import com.creactives.tam4.dataloader.dataproviders.database.events.EventsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.events.EventsStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

@RequiredArgsConstructor
@Service
public class EventRejectedUseCase {

  private final EventsStagingRepository eventsStagingRepository;

  public void updateEvent(final String externalCorrelationId, final String reason, final long rejectedDate) {
    final EventsEntity entity = eventsStagingRepository.findByCorrelationId(externalCorrelationId);
    entity.setSynchronizationConfirmedOn(new Timestamp(rejectedDate));
    entity.setSynchronizationState(reason);
    eventsStagingRepository.save(entity);
  }
}
