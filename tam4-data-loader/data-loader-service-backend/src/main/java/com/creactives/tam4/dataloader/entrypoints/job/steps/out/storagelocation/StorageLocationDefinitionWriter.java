package com.creactives.tam4.dataloader.entrypoints.job.steps.out.storagelocation;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.StorageLocationDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Log4j2
public class StorageLocationDefinitionWriter implements ItemWriter<StorageLocationDefinitionUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  private final StorageLocationStagingRepository storageLocationStagingRepository;

  public StorageLocationDefinitionWriter(final WriteMessageService sendMessages, final StorageLocationStagingRepository storageLocationStagingRepository) {
    this.sendMessages = sendMessages;

    this.storageLocationStagingRepository = storageLocationStagingRepository;
  }

  @Override
  public void write(final List<? extends StorageLocationDefinitionUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
