package com.creactives.tam4.dataloader.dataproviders.messaging;

import com.creactives.tam4.dataloader.configuration.DataLoaderConfiguration;
import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.outbound.DLOutboundWriterProvider;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Log4j2
public class WriteMessageService {
  private final RabbitTemplate rabbitTemplate;
  private final boolean sendMessagesEnabled;
  private final boolean printOutMessagesEnabled;
  private final boolean bufferToDb;
  private final DLOutboundWriterProvider outboundWriterProvider;

  public WriteMessageService(final RabbitTemplate rabbitTemplate,
                             @Value("${data-loader.debug.send-messages-to-exchange: true}") final boolean sendMessagesEnabled,
                             @Value("${data-loader.debug.print-out-messages-enabled: false}") final boolean printOutMessagesEnabled,
                             final DataLoaderConfiguration configuration,
                             final DLOutboundWriterProvider outboundWriterProvider) {
    this.rabbitTemplate = rabbitTemplate;
    this.sendMessagesEnabled = sendMessagesEnabled;
    this.printOutMessagesEnabled = printOutMessagesEnabled;
    this.bufferToDb = configuration.isWriteToDatabase();
    this.outboundWriterProvider = outboundWriterProvider;
  }

  public void forcefullySend(final String exchange, final Object message) {
    if (sendMessagesEnabled) {
      log.debug("sending message/s {} to {} STARTED", message, exchange);
      if (printOutMessagesEnabled) {
        log.debug("Message content: {}", message);
      }
      rabbitTemplate.convertAndSend(exchange, MessagingConfiguration.ROUTING_KEY, message);
      log.debug("sending message/s {} to {} COMPLETED", message, exchange);
    } else {
      log.warn("RABBITMQ: send message to queue not enabled");
    }
  }

  public <T> void send(final String exchange, final List<T> message) {
    if (bufferToDb) {
      this.outboundWriterProvider.writeBatchMessages(exchange, message);
    } else {
      for (final Object o : message) {
        forcefullySend(exchange, o);
      }
    }
  }
}
