package com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountriesDefinitionKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@StepScope
public class CountryKeysDefinitionsItemReader extends RepositoryItemReader<CountryDefinitionsKeyEntity> {

  public CountryKeysDefinitionsItemReader(final CountriesDefinitionKeysStagingRepository countriesDefinitionKeysStagingRepository,
                                          @Value("#{jobParameters['clientCode']}") final String clientCode) {
    this.setRepository(countriesDefinitionKeysStagingRepository);
    this.setPageSize(100);
    if (clientCode != null) {
      this.setMethodName("findAllByClient");
      this.setArguments(List.of(clientCode));
    } else {
      this.setMethodName("findAll");
    }

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC
    );
    this.setSort(sorts);
  }

}
