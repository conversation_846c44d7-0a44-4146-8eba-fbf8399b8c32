package com.creactives.tam4.dataloader.entrypoints.job.steps.in.valuationclassdefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassesDefinitionsKeysStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class ValuationClassDefinitionsEnabledEntitiesWriter implements ItemWriter<ValuationClassDefinitionsKeyEntity> {

  private final ValuationClassesDefinitionsKeysStagingRepository valuationClassesDefinitionsKeysStagingRepository;

  @Override
  public void write(final List<? extends ValuationClassDefinitionsKeyEntity> list) throws Exception {
    for (final ValuationClassDefinitionsKeyEntity entity : list) {
      if (entity.getEnabled()) {
        valuationClassesDefinitionsKeysStagingRepository.setEnabled(true, entity.getValuationClass(), entity.getClient());
      }
    }
  }
}
