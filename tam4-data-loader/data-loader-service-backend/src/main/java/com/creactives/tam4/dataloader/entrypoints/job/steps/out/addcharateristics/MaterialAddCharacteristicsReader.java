package com.creactives.tam4.dataloader.entrypoints.job.steps.out.addcharateristics;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.AggregateDataForMaterialCreated;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.SimpleJdbcBatchItemReader;
import com.google.common.collect.ArrayListMultimap;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static java.util.Collections.emptyList;

@RequiredArgsConstructor
@Component
public class MaterialAddCharacteristicsReader extends SimpleJdbcBatchItemReader<Long, AggregateDataForMaterialCreated> {

  private final String BASE_QUERY = "SELECT" +
      "    mds.*," +
      "    array_agg(distinct cs.description || ':' || cs.value) AS characteristics" +
      "  FROM materials_data_staging mds" +
      "    LEFT JOIN characteristics_staging cs on mds.client = cs.client AND mds.material_code = cs.material_code";

  private final String GROUP_AN_ORDER_BY = "  GROUP BY mds.id" +
      "  ORDER BY mds.id";

  private final String CONDITIONS = " mds.ignore is false ";

  private final JdbcTemplate jdbcTemplate;
  private final int fetchSize = 10000;


  @Override
  protected Long getKey(final AggregateDataForMaterialCreated entity) {
    return entity.getMaterialEntity().getId();
  }

  @Override
  protected List<AggregateDataForMaterialCreated> queryForFirstPage() {
    return jdbcTemplate.query(BASE_QUERY + " WHERE " + CONDITIONS + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        fetchSize
    );
  }

  @Override
  protected List<AggregateDataForMaterialCreated> queryAfterId(final Long id) {
    return jdbcTemplate.query(BASE_QUERY + " where " + CONDITIONS + " and mds.id > ? " + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        id,
        fetchSize
    );
  }

  private AggregateDataForMaterialCreated map(final ResultSet rs) throws SQLException {
    return AggregateDataForMaterialCreated.builder()
        .materialEntity(readMaterial(rs))
        .alternativeUnitsOfMeasureEntities(emptyList())
        .shortDescriptionEntities(emptyList())
        .poDescriptionEntities(emptyList())
        .longDescriptionEntities(emptyList())
        .inspectionDescriptionEntities(emptyList())
        .internalNoteDescriptionEntities(emptyList())
        .characteristics(readCharacteristics(rs))
        .build();
  }

  private ArrayListMultimap<String, String> readCharacteristics(final ResultSet rs) throws SQLException {
    final Array descriptions = rs.getArray("characteristics");
    final String[] array = (String[]) descriptions.getArray();
    final ArrayListMultimap<String, String> out = ArrayListMultimap.create();
    if (array != null) {
      for (final String descriptionEncoded : array) {
        if (descriptionEncoded != null) {
          final int i = descriptionEncoded.indexOf(':');
          final String key = descriptionEncoded.substring(0, i);
          final String value = descriptionEncoded.substring(i + 1);
          out.put(key, value);
        }
      }
    }
    return out;
  }

  private MaterialEntity readMaterial(final ResultSet rs) throws SQLException {
    return MaterialEntity.builder()
        .id(rs.getLong("id"))
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .deletionFlag(rs.getBoolean("deletion_flag"))
        .materialType(rs.getString("material_type"))
        .industrySector(rs.getString("industry_sector"))
        .materialGroup(rs.getString("material_group"))
        .oldMaterialNumber(rs.getString("old_material_number"))
        .baseUnitOfMeasurement(rs.getString("base_unit_of_measurement"))
        .productDivision(rs.getString("product_division"))
        .authorizationGroup(rs.getString("authorization_group"))
        .crossPlantMaterialStatus(rs.getString("cross_plant_material_status"))
        .materialStatusValidFromDate(rs.getLong("material_status_valid_from_date"))
        .manufacturerPartNumber(rs.getString("manufacturer_part_number"))
        .manufacturerCode(rs.getString("manufacturer_code"))
        .genericItemGroup(rs.getString("generic_item_group"))
        .revisionNumber(rs.getString("revision_number"))
        .purchasingMeasurementUnits(rs.getString("purchasing_measurement_units"))
        .materialCreatedOn(rs.getString("material_created_on"))
        .externalMaterialGroup(rs.getString("external_material_group"))
        .weightUnit(rs.getString("weight_unit"))
        .netWeight(rs.getBigDecimal("net_weight"))
        .grossWeight(rs.getBigDecimal("gross_weight"))
        .sizeDimension(rs.getString("size_dimension"))
        .hazardousMaterialNumber(rs.getString("hazardous_material_number"))
        .ignore(rs.getBoolean("ignore"))
        .createdOn(rs.getTimestamp("created_on"))
        .lastModifiedOn(rs.getTimestamp("last_modified_on"))
        .synchronizedOn(rs.getTimestamp("synchronized_on"))
        .synchronizationConfirmedOn(rs.getTimestamp("synchronization_confirmed_on"))
        .synchronizationState(rs.getString("synchronization_state"))
        .semanticallyAnalyzed(rs.getBoolean("semantically_analyzed"))
        .mdDomain(rs.getString("md_domain"))

        ////FIXME: Custom fields required by A2A T4-1756
        .famiglia(rs.getString("famiglia"))
        .sottoFamiglia(rs.getString("sotto_famiglia"))
        .specificaTecnica(rs.getString("specifica_tecnica"))
        .edizione(rs.getString("edizione"))
        .revisione(rs.getString("revisione"))
        .dataCustom(rs.getLong("data_custom"))

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(rs.getString("product_hierarchy"))

        .volume(BigDecimal.valueOf(rs.getLong("volume")))
        .volumeUnit(rs.getString("volume_unit"))
        .internationalArticleNumberEanUpc(rs.getString("internation_article_number"))
        .serviceValuationClass(rs.getString("service_valuation_class"))
        .build();
  }


}
