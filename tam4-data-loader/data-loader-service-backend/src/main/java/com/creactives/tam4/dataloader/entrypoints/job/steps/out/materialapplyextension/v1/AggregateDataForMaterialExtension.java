package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AggregateDataForMaterialExtension {

  private MaterialEntity materialEntity;
  private List<MaterialPlantEntity> materialPlantEntities;
  private List<MaterialStorageLocationEntity> materialStorageLocationEntities;
  private Map<String, PlantEntity> plants;

}
