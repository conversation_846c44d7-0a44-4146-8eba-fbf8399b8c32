package com.creactives.tam4.dataloader.core.entity;

import com.creactives.tam4.dataloader.core.exceptions.UnsupportedLanguageException;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * Created on 6/5/2019 3:23 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
@SuppressWarnings("MethodMayBeStatic")
@Log4j2
public class SAPLanguageConverter {

  private static final BiMap<String, String> biMap = HashBiMap.create();

  static {
    biMap.put("a", "af");
    biMap.put("A", "ar");
    biMap.put("W", "bg");
    biMap.put("c", "ca");
    biMap.put("1", "zh");
    biMap.put("M", "zf");
    biMap.put("6", "hr");
    biMap.put("C", "cs");
    biMap.put("K", "da");
    biMap.put("N", "nl");
    biMap.put("E", "en");
    biMap.put("9", "et");
    biMap.put("U", "fi");
    biMap.put("F", "fr");
    biMap.put("D", "de");
    biMap.put("G", "el");
    biMap.put("B", "he");
    biMap.put("H", "hu");
    biMap.put("b", "is");
    biMap.put("i", "id");
    biMap.put("I", "it");
    biMap.put("J", "ja");
    biMap.put("3", "ko");
    biMap.put("Y", "lv");
    biMap.put("X", "lt");
    biMap.put("7", "ms");
    biMap.put("O", "no");
    biMap.put("L", "pl");
    biMap.put("P", "pt");
    biMap.put("Z", "z1");
    biMap.put("4", "ro");
    biMap.put("R", "ru");
    biMap.put("0", "sr");
    biMap.put("d", "sh");
    biMap.put("Q", "sk");
    biMap.put("5", "sl");
    biMap.put("S", "es");
    biMap.put("V", "sv");
    biMap.put("2", "th");
    biMap.put("T", "tr");
    biMap.put("8", "uk");
    biMap.put("쁩", "vi");
    biMap.put("뱋", "kk");
    biMap.put("묩", "hi");
  }

  public String convertToLanguage(final String sapOneLetterCode) {
    if (StringUtils.isBlank(sapOneLetterCode)) {
      return "en";
    }
    if (sapOneLetterCode.length() == 2) {
      final String sapTwoLetterCode = sapOneLetterCode.toLowerCase(Locale.ENGLISH);
      if (biMap.containsValue(sapTwoLetterCode)) {
        return sapTwoLetterCode;
      } else {
        throw new UnsupportedLanguageException(sapTwoLetterCode);
      }
    }
    final String language = biMap.get(sapOneLetterCode);
    if (language == null) {
      throw new UnsupportedLanguageException(sapOneLetterCode);
    }
    return language;
  }

  public String convertToSapOneLetterCode(final String language) {
    return biMap.inverse().get(language);
  }
}
