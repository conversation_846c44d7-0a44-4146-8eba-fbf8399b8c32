package com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsEntity;
import com.creactives.tam4.messaging.materials.commands.GlobalValuationCategoryDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Log4j2
public class ConvertAggregateToGlobalValuationCategoryDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForGlobalValuationCategoryDefinition, GlobalValuationCategoryDefinitionUpsertRequestMessage> {

  @Override
  public GlobalValuationCategoryDefinitionUpsertRequestMessage process(final AggregateDataForGlobalValuationCategoryDefinition aggregateDataForGlobalValuationCategoryDefinition) throws Exception {
    return GlobalValuationCategoryDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForGlobalValuationCategoryDefinition.getGlobalValuationCategoryDefinitionsKeyEntity().getClient())
        .globalValuationCategory(aggregateDataForGlobalValuationCategoryDefinition.getGlobalValuationCategoryDefinitionsKeyEntity().getGlobalValuationCategory())
        .descriptions(aggregateDataForGlobalValuationCategoryDefinition.getGlobalValuationCategoryEnities().stream()
            .filter(globalValuationCategoryEntity -> globalValuationCategoryEntity.getLanguage() != null)
            .collect(Collectors.toMap(GlobalValuationCategoryDefinitionsEntity::getLanguage,
                GlobalValuationCategoryDefinitionsEntity::getDescription
            ))
        )
        .enabled(aggregateDataForGlobalValuationCategoryDefinition.getGlobalValuationCategoryDefinitionsKeyEntity().getEnabled())
        .build();
  }
}
