package com.creactives.tam4.dataloader.entrypoints.job;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.basicmaterial.SendBasicMaterialAddOrUpateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.clientdescription.SendClientsDescriptionsAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions.SendCountryDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.externalmaterialgroup.SendExternalMaterialGroupAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions.SendGlobalValuationCategoryDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.hazardousmaterial.SendHazardousMaterialAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions.SendIndustrySectorDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions.SendItemCategoryGroupDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.laboratory.SendLaboratoryAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.logisticshandlinggroupdefinitions.SendLogisticsHandlingGroupDefinitionsAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.lotsizedefinition.SendLotSizeDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey.SendSchedulingMarginKeyAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4.SendMaterialApplyExtensionRequestedV4Constants;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.MaterialCreatedConstants;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialgroup.SendMaterialGroupAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2.SendMaterialPlantValuationV2Constants;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions.SendMaterialStatusDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialtypes.SendMaterialTypesAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpcontroller.SendMRPControllerAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel.SendMRPMaterialLevelAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrptype.SendMRPTypeAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant.SendPlantAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition.SendProductDivisionDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.purchasinggroup.SendPurchasingGroupAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcertificatecategory.SendQMCertificateCategoryAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcontrol.SendQMControlAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.serialnumber.SendSerialNumberAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.storagelocation.SendStorageLocationAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.supplierdefinition.SendSupplierDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.synchronizationstate.UpdateSynchronizationStateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure.SendUnitOfMeasureDefinitionAddOrUpdateStep;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions.SendValuationClassDefinitionAddOrUpdateStep;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class SendAllMessagesJob {

  public static final String SEND_ALL_MESSAGES = "send-all-messages";
  private final Step sendMaterialCreatedMessagesStep;
  private final Step sendMaterialApplyExtensionRequestedMessagesStep;
  //  private final SendMaterialApplyExtensionRequestedMessagesStepV3 sendMaterialApplyExtensionRequestedMessagesStep;
  private final Step sendMaterialPlantValuationRequestedMessagesStep;
  private final SendItemCategoryGroupDefinitionAddOrUpdateStep sendItemCategoryGroupDefinitionAddOrUpdateStep;
  private final SendMaterialStatusDefinitionAddOrUpdateStep sendMaterialStatusDefinitionAddOrUpdateStep;
  private final SendIndustrySectorDefinitionAddOrUpdateStep sendIndustrySectorDefinitionAddOrUpdateStep;
  private final SendUnitOfMeasureDefinitionAddOrUpdateStep sendUnitOfMeasureDefinitionAddOrUpdateStep;
  private final SendStorageLocationAddOrUpdateStep sendStorageLocationAddOrUpdateStep;
  private final SendCountryDefinitionAddOrUpdateStep sendCountryDefinitionAddOrUpdateStep;
  private final SendPlantAddOrUpdateStep sendPlantAddOrUpdateStep;
  private final SendValuationClassDefinitionAddOrUpdateStep sendValuationClassDefinitionAddOrUpdateStep;
  private final SendGlobalValuationCategoryDefinitionAddOrUpdateStep sendGlobalValuationCategoryDefinitionAddOrUpdateStep;
  private final SendPurchasingGroupAddOrUpdateStep sendPurchasingGroupAddOrUpdateStep;
  private final SendSupplierDefinitionAddOrUpdateStep sendSupplierDefinitionAddOrUpdateStep;
  private final SendProductDivisionDefinitionAddOrUpdateStep sendProductDivisionDefinitionAddOrUpdateStep;
  private final SendSerialNumberAddOrUpdateStep sendSerialNumberAddOrUpdateStep;
  private final SendMaterialGroupAddOrUpdateStep sendMaterialGroupAddOrUpdateStep;
  private final SendMaterialTypesAddOrUpdateStep sendMaterialTypesAddOrUpdateStep;
  //  private final SendUnitsOfMeasurementAddOrUpdateStep sendUnitsOfMeasurementAddOrUpdateStep;
  private final SendBasicMaterialAddOrUpateStep sendBasicMaterialAddOrUpateStep;
  private final SendExternalMaterialGroupAddOrUpdateStep sendExternalMaterialGroupAddOrUpdateStep;
  private final SendHazardousMaterialAddOrUpdateStep sendHazardousMaterialAddOrUpdateStep;
  private final SendLaboratoryAddOrUpdateStep sendLaboratoryAddOrUpdateStep;
  private final SendLogisticsHandlingGroupDefinitionsAddOrUpdateStep sendLogisticsHandlingGroupDefinitionsAddOrUpdateStep;
  private final SendMRPControllerAddOrUpdateStep sendMRPControllerAddOrUpdateStep;
  private final SendMRPMaterialLevelAddOrUpdateStep sendMRPMaterialLevelAddOrUpdateStep;
  private final SendMRPTypeAddOrUpdateStep sendMRPTypeAddOrUpdateStep;
  private final SendQMCertificateCategoryAddOrUpdateStep sendQMCertificateCategoryAddOrUpdateStep;
  private final SendQMControlAddOrUpdateStep sendQMControlAddOrUpdateStep;
  private final SendLotSizeDefinitionAddOrUpdateStep sendLotSizeDefinitionAddOrUpdateStep;
  private final SendSchedulingMarginKeyAddOrUpdateStep sendSchedulingMarginKeyAddOrUpdateStep;
  private final SendClientsDescriptionsAddOrUpdateStep sendClientsDescriptionsAddOrUpdateStep;
  private final UpdateSynchronizationStateStep updateSynchronizationStateStep;


  public SendAllMessagesJob(@Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES) final Step sendMaterialCreatedMessagesStep,
                            @Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME) final Step sendMaterialApplyExtensionRequestedMessagesStep,
                            @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION) final Step sendMaterialPlantValuationRequestedMessagesStep,
                            final SendItemCategoryGroupDefinitionAddOrUpdateStep sendItemCategoryGroupDefinitionAddOrUpdateStep,
                            final SendUnitOfMeasureDefinitionAddOrUpdateStep sendUnitOfMeasureDefinitionAddOrUpdateStep,
                            final SendIndustrySectorDefinitionAddOrUpdateStep sendIndustrySectorDefinitionAddOrUpdateStep,
                            final SendMaterialStatusDefinitionAddOrUpdateStep sendMaterialStatusDefinitionAddOrUpdateStep,
                            final SendStorageLocationAddOrUpdateStep sendStorageLocationAddOrUpdateStep,
                            final SendCountryDefinitionAddOrUpdateStep sendCountryDefinitionAddOrUpdateStep,
                            final SendPlantAddOrUpdateStep sendPlantAddOrUpdateStep,
                            final SendValuationClassDefinitionAddOrUpdateStep sendValuationClassDefinitionAddOrUpdateStep,
                            final SendGlobalValuationCategoryDefinitionAddOrUpdateStep sendGlobalValuationCategoryDefinitionAddOrUpdateStep,
                            final SendPurchasingGroupAddOrUpdateStep sendPurchasingGroupAddOrUpdateStep,
                            final SendSupplierDefinitionAddOrUpdateStep sendSupplierDefinitionAddOrUpdateStep,
                            final SendProductDivisionDefinitionAddOrUpdateStep sendProductDivisionDefinitionAddOrUpdateStep,
                            final SendSerialNumberAddOrUpdateStep sendSerialNumberAddOrUpdateStep,
                            final SendMaterialGroupAddOrUpdateStep sendMaterialGroupAddOrUpdateStep,
                            final SendMaterialTypesAddOrUpdateStep sendMaterialTypesAddOrUpdateStep,
                            // final SendUnitsOfMeasurementAddOrUpdateStep sendUnitsOfMeasurementAddOrUpdateStep,
                            final SendBasicMaterialAddOrUpateStep sendBasicMaterialAddOrUpateStep,
                            final SendExternalMaterialGroupAddOrUpdateStep sendExternalMaterialGroupAddOrUpdateStep,
                            final SendHazardousMaterialAddOrUpdateStep sendHazardousMaterialAddOrUpdateStep,
                            final SendLaboratoryAddOrUpdateStep sendLaboratoryAddOrUpdateStep,
                            final SendLogisticsHandlingGroupDefinitionsAddOrUpdateStep sendLogisticsHandlingGroupDefinitionsAddOrUpdateStep,
                            final SendMRPControllerAddOrUpdateStep sendMRPControllerAddOrUpdateStep,
                            final SendMRPMaterialLevelAddOrUpdateStep sendMRPMaterialLevelAddOrUpdateStep,
                            final SendMRPTypeAddOrUpdateStep sendMRPTypeAddOrUpdateStep,
                            final SendQMCertificateCategoryAddOrUpdateStep sendQMCertificateCategoryAddOrUpdateStep,
                            final SendQMControlAddOrUpdateStep sendQMControlAddOrUpdateStep,
                            final SendLotSizeDefinitionAddOrUpdateStep sendLotSizeDefinitionAddOrUpdateStep,
                            final SendSchedulingMarginKeyAddOrUpdateStep sendSchedulingMarginKeyAddOrUpdateStep,
                            final SendClientsDescriptionsAddOrUpdateStep sendClientsDescriptionsAddOrUpdateStep,
                            final UpdateSynchronizationStateStep updateSynchronizationStateStep) {
    this.sendMaterialCreatedMessagesStep = sendMaterialCreatedMessagesStep;
    this.sendMaterialPlantValuationRequestedMessagesStep = sendMaterialPlantValuationRequestedMessagesStep;
    this.sendMaterialApplyExtensionRequestedMessagesStep = sendMaterialApplyExtensionRequestedMessagesStep;
    this.sendItemCategoryGroupDefinitionAddOrUpdateStep = sendItemCategoryGroupDefinitionAddOrUpdateStep;
    this.sendMaterialStatusDefinitionAddOrUpdateStep = sendMaterialStatusDefinitionAddOrUpdateStep;
    this.sendIndustrySectorDefinitionAddOrUpdateStep = sendIndustrySectorDefinitionAddOrUpdateStep;
    this.sendUnitOfMeasureDefinitionAddOrUpdateStep = sendUnitOfMeasureDefinitionAddOrUpdateStep;
    this.sendStorageLocationAddOrUpdateStep = sendStorageLocationAddOrUpdateStep;
    this.sendCountryDefinitionAddOrUpdateStep = sendCountryDefinitionAddOrUpdateStep;
    this.sendPlantAddOrUpdateStep = sendPlantAddOrUpdateStep;
    this.sendValuationClassDefinitionAddOrUpdateStep = sendValuationClassDefinitionAddOrUpdateStep;
    this.sendGlobalValuationCategoryDefinitionAddOrUpdateStep = sendGlobalValuationCategoryDefinitionAddOrUpdateStep;
    this.sendPurchasingGroupAddOrUpdateStep = sendPurchasingGroupAddOrUpdateStep;
    this.sendSupplierDefinitionAddOrUpdateStep = sendSupplierDefinitionAddOrUpdateStep;
    this.sendProductDivisionDefinitionAddOrUpdateStep = sendProductDivisionDefinitionAddOrUpdateStep;
    this.sendSerialNumberAddOrUpdateStep = sendSerialNumberAddOrUpdateStep;
    this.sendMaterialGroupAddOrUpdateStep = sendMaterialGroupAddOrUpdateStep;
    this.sendMaterialTypesAddOrUpdateStep = sendMaterialTypesAddOrUpdateStep;
    this.sendBasicMaterialAddOrUpateStep = sendBasicMaterialAddOrUpateStep;
    this.sendExternalMaterialGroupAddOrUpdateStep = sendExternalMaterialGroupAddOrUpdateStep;
    this.sendHazardousMaterialAddOrUpdateStep = sendHazardousMaterialAddOrUpdateStep;
    this.sendLaboratoryAddOrUpdateStep = sendLaboratoryAddOrUpdateStep;
    this.sendLogisticsHandlingGroupDefinitionsAddOrUpdateStep = sendLogisticsHandlingGroupDefinitionsAddOrUpdateStep;
    this.sendMRPControllerAddOrUpdateStep = sendMRPControllerAddOrUpdateStep;
    this.sendMRPMaterialLevelAddOrUpdateStep = sendMRPMaterialLevelAddOrUpdateStep;
    this.sendMRPTypeAddOrUpdateStep = sendMRPTypeAddOrUpdateStep;
    this.sendQMCertificateCategoryAddOrUpdateStep = sendQMCertificateCategoryAddOrUpdateStep;
    this.sendQMControlAddOrUpdateStep = sendQMControlAddOrUpdateStep;
    this.sendLotSizeDefinitionAddOrUpdateStep = sendLotSizeDefinitionAddOrUpdateStep;
    this.sendSchedulingMarginKeyAddOrUpdateStep = sendSchedulingMarginKeyAddOrUpdateStep;
    this.sendClientsDescriptionsAddOrUpdateStep = sendClientsDescriptionsAddOrUpdateStep;
    this.updateSynchronizationStateStep = updateSynchronizationStateStep;
  }


  @Bean(name = SEND_ALL_MESSAGES)
  public Job job(final JobBuilderFactory factory, final TaskExecutor taskExecutor) {
    log.info("Configuring job");
    final FlowBuilder<Flow> flowBuilder = new FlowBuilder<>(SEND_ALL_MESSAGES + "-flowbuilder");

    final Flow flowMaterials = flowBuilder.start(sendMaterialCreatedMessagesStep)
        .next(sendMaterialApplyExtensionRequestedMessagesStep)
        .next(sendMaterialPlantValuationRequestedMessagesStep)
        .next(sendHazardousMaterialAddOrUpdateStep.configureStep())
        .build();

    return factory.get(SEND_ALL_MESSAGES)
        .incrementer(new RunIdIncrementer())
        .start(getDefinitionSubFlow(taskExecutor))
        .next(getPlantDefinitionsSubFlow())
        .next(flowMaterials)
        .next(updateSynchronizationStateStep.configureStep())
        .end().build();
  }

  private Flow getDefinitionSubFlow(final TaskExecutor taskExecutor) {
    final FlowBuilder<Flow> flowBuilder = new FlowBuilder<>(SEND_ALL_MESSAGES + "-flowbuilder-definition");

    return flowBuilder.start(sendClientsDescriptionsAddOrUpdateStep.configureStep())
        .split(taskExecutor)
        .add(subFlow(sendCountryDefinitionAddOrUpdateStep),
            subFlow(sendItemCategoryGroupDefinitionAddOrUpdateStep),
            subFlow(sendIndustrySectorDefinitionAddOrUpdateStep),
            subFlow(sendMaterialStatusDefinitionAddOrUpdateStep),
            subFlow(sendUnitOfMeasureDefinitionAddOrUpdateStep),
            subFlow(sendValuationClassDefinitionAddOrUpdateStep),
            subFlow(sendExternalMaterialGroupAddOrUpdateStep),
            subFlow(sendBasicMaterialAddOrUpateStep),
            subFlow(sendGlobalValuationCategoryDefinitionAddOrUpdateStep),
            subFlow(sendPurchasingGroupAddOrUpdateStep),
            subFlow(sendSupplierDefinitionAddOrUpdateStep),
            subFlow(sendSerialNumberAddOrUpdateStep),
            subFlow(sendProductDivisionDefinitionAddOrUpdateStep),
            subFlow(sendMaterialGroupAddOrUpdateStep),
            subFlow(sendMaterialTypesAddOrUpdateStep),
            //                          subFlow(sendHazardousMaterialAddOrUpdateStep),
            subFlow(sendLaboratoryAddOrUpdateStep),
            subFlow(sendLogisticsHandlingGroupDefinitionsAddOrUpdateStep),
            subFlow(sendLotSizeDefinitionAddOrUpdateStep)
        ).build();
  }

  private Flow subFlow(final StepConfigurer stepConfigurer) {
    final FlowBuilder<Flow> flowBuilder = new FlowBuilder<>(SEND_ALL_MESSAGES + "-flowbuilder-" + stepConfigurer.getClass().getCanonicalName());

    return flowBuilder.start(stepConfigurer.configureStep()).build();
  }

  private Flow getPlantDefinitionsSubFlow() {
    final FlowBuilder<Flow> flowBuilder = new FlowBuilder<>(SEND_ALL_MESSAGES + "-flowbuilder-plant");

    return flowBuilder.start(sendPlantAddOrUpdateStep.configureStep())
        .next(sendSchedulingMarginKeyAddOrUpdateStep.configureStep())
        .next(sendStorageLocationAddOrUpdateStep.configureStep())
        .next(sendMRPControllerAddOrUpdateStep.configureStep())
        .next(sendMRPMaterialLevelAddOrUpdateStep.configureStep())
        .next(sendMRPTypeAddOrUpdateStep.configureStep())
        .next(sendQMCertificateCategoryAddOrUpdateStep.configureStep())
        .next(sendQMControlAddOrUpdateStep.configureStep())
        .build();
  }

}
