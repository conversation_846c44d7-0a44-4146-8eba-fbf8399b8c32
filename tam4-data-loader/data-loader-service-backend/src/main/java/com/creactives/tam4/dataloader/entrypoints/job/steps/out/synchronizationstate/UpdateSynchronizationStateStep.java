package com.creactives.tam4.dataloader.entrypoints.job.steps.out.synchronizationstate;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Component
public class UpdateSynchronizationStateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;

  private final JdbcTemplate jdbcTemplate;

  private final List<String> syncListTable = List.of("consumption_order_staging",
      "country_definitions_staging",
      "global_valuation_category_definitions_staging",
//            "history_order_staging",
//            "long_descriptions_staging",
      "material_group_definitions_staging",
      "material_group_staging",
      "material_status_definitions_staging",
//            "material_storage_locations_staging",
      "material_type_definitions_staging",
//            "materials_plant_data_staging",
      "material_type_staging",
      "plant_currency",
      "plant_definitions_staging",
//            "plants_valuation_data_staging",
      "purchasing_group_staging",
//            "short_descriptions_staging",
//            "storage_location_definitions_staging",
      "suppliers_definitions_staging",
      "unit_of_measure_definitions_staging",
      "valuation_area_staging"
  );

  public UpdateSynchronizationStateStep(final StepBuilderFactory stepBuilderFactory,
                                        final JdbcTemplate jdbcTemplate) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.jdbcTemplate = jdbcTemplate;
  }

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("update-synchronization-state").tasklet((contribution, chunkContext) -> {
      final String clientCode = chunkContext.getStepContext().getStepExecution()
          .getJobParameters().getString("clientCode");
      final Map<String, String> valuesMap = new HashMap<>();
      final StringBuilder templateString = new StringBuilder("UPDATE ${table} SET synchronization_state = '${set_state}' WHERE synchronization_state = '${condition_state}'");
      if (clientCode != null) {
        valuesMap.put("client_code", clientCode);
        templateString.append(" and client = '${client_code}'");
      }
      updateMaterialsStaging(valuesMap, templateString);
      syncTables(valuesMap, templateString);
      return RepeatStatus.FINISHED;
    }).build();
  }

  private void updateMaterialsStaging(final Map<String, String> valuesMap, final StringBuilder templateString) {
    final Map<String, String> params = new HashMap<>(valuesMap);
    params.put("table", "materials_data_staging");
    params.put("set_state", SyncStatus.MESSAGE_SENT.getCode());
    params.put("condition_state", SyncStatus.PENDING.getCode());
    updateTable(params, templateString.toString());
  }

  private void syncTables(final Map<String, String> valuesMap, final StringBuilder templateString) {
    syncListTable.parallelStream().forEach(table -> {
      final Map<String, String> params = new HashMap<>(valuesMap);
      params.put("table", table);
      params.put("set_state", SyncStatus.UNMATCHED.getCode());
      params.put("condition_state", SyncStatus.PENDING.getCode());
      updateTable(params, templateString.toString());
    });
  }

  private void updateTable(final Map<String, String> params, final String query) {
    final long start = System.currentTimeMillis();
    log.info("Update table {} - Synchronization status {}", params.get("table"), params.get("set_state"));
    final StringSubstitutor sub = new StringSubstitutor(params);
    jdbcTemplate.batchUpdate(sub.replace(query));
    log.info("End update table {} - time: {} ms", params.get("table"), (System.currentTimeMillis() - start));
  }
}
