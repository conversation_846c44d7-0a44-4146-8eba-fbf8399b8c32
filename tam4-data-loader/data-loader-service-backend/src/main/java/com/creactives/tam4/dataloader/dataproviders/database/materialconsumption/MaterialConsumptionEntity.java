package com.creactives.tam4.dataloader.dataproviders.database.materialconsumption;

import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "material_consumption_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MaterialConsumptionEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_group_staging_generator")
  @SequenceGenerator(name = "material_group_staging_generator", sequenceName = "material_group_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_number")
  private String materialNumber;

  @Column(name = "plant")
  private String plant;

  @Column(name = "fiscal_year")
  private String fiscalYear;

  @Column(name = "period_indicator")
  private String periodIndicator;

  @Column(name = "follow_on_record_nr")
  private String followOnRecordNr;

  @Column(name = "total_consumption_01")
  private double totalConsumption01;

  @Column(name = "total_consumption_02")
  private double totalConsumption02;

  @Column(name = "total_consumption_03")
  private double totalConsumption03;

  @Column(name = "total_consumption_04")
  private double totalConsumption04;

  @Column(name = "total_consumption_05")
  private double totalConsumption05;

  @Column(name = "total_consumption_06")
  private double totalConsumption06;

  @Column(name = "total_consumption_07")
  private double totalConsumption07;

  @Column(name = "total_consumption_08")
  private double totalConsumption08;

  @Column(name = "total_consumption_09")
  private double totalConsumption09;

  @Column(name = "total_consumption_10")
  private double totalConsumption10;

  @Column(name = "total_consumption_11")
  private double totalConsumption11;

  @Column(name = "total_consumption_12")
  private double totalConsumption12;

  @Column(name = "total_consumption_13")
  private double totalConsumption13;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;

}
