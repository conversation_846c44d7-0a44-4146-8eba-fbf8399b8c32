package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialtypes;


import com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition.MaterialTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition.MaterialTypeDefinitionStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.messaging.materials.commands.MaterialTypeDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
@Log4j2
public class ConvertEntityToMaterialTypeRequestItemProcessor implements ItemProcessor<MaterialTypesEntity, MaterialTypeDefinitionUpsertRequestMessage> {

  private final MaterialTypeDefinitionStagingRepository materialTypeDefinitionStagingRepository;

  @Override
  public MaterialTypeDefinitionUpsertRequestMessage process(final MaterialTypesEntity materialTypesEntity) throws Exception {
    final List<MaterialTypeDefinitionEntity> materialTypeDefinitionEntityList = materialTypeDefinitionStagingRepository.findByClientAndMaterialType(materialTypesEntity.getClient(), materialTypesEntity.getMaterialType());
    return MaterialTypeDefinitionUpsertRequestMessage.builder()
        .client(materialTypesEntity.getClient())
        .authorizationGroup(materialTypesEntity.getAuthorizationGroup())
        .categoryGroup(materialTypesEntity.getCategoryGroup())
        .maintenanceStatus(materialTypesEntity.getMaintenanceStatus())
        .materialType(materialTypesEntity.getMaterialType())
        .priceControlIndicator(materialTypesEntity.getPriceControlIndicator())
        .priceControlMandatory(materialTypesEntity.getPriceControlMandatory())
        .descriptions(materialTypeDefinitionEntityList.stream()
            .filter(materialTypeDefinitionEntity -> materialTypeDefinitionEntity.getLanguage() != null)
            .collect(Collectors.toMap(MaterialTypeDefinitionEntity::getLanguage,
                MaterialTypeDefinitionEntity::getDescription
            ))
        )
        .enabled(materialTypesEntity.getEnabled())
        .build();
  }
}
