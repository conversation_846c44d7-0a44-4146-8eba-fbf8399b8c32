package com.creactives.tam4.dataloader.dataproviders.database.businessadress;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "business_address_service_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BusinessAddressEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "business_address_service_staging_generator")
  @SequenceGenerator(name = "business_address_service_staging_generator", sequenceName = "business_address_service_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "address_number")
  private String addressNumber;

  @Column(name = "valid_from_date")
  private String validFromDate;

  @Column(name = "city")
  private String city;

  @Column(name = "address_version_id")
  private String addressVersionId;

  @Column(name = "valid_to_date")
  private String validToDate;

  @Column(name = "language_key")
  private String languageKey;

  @Column(name = "name_1")
  private String name1;

  @Column(name = "city_postal")
  private String cityPostal;

  @Column(name = "po_box")
  private String poBox;

  @Column(name = "street")
  private String street;

  @Column(name = "house_number")
  private String houseNumber;

  @Column(name = "county")
  private String county;

  @Column(name = "region")
  private String region;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
