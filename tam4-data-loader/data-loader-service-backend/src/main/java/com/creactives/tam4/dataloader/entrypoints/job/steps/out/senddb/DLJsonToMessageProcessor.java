package com.creactives.tam4.dataloader.entrypoints.job.steps.out.senddb;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DLJsonToMessageProcessor implements ItemProcessor<DbMessage, ObjectMessage> {

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public ObjectMessage process(final DbMessage entity) throws Exception {
    final String className = entity.getClassName();
    final Class<?> aClass = Class.forName(className);
    final Object message = objectMapper.readValue(entity.getSerializedMessage(), aClass);
    return new ObjectMessage(entity.getId(), entity.getExchange(), message);
  }
}
