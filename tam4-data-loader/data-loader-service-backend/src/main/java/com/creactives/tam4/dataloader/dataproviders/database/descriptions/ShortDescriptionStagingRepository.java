package com.creactives.tam4.dataloader.dataproviders.database.descriptions;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created on 6/5/2019 4:44 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface ShortDescriptionStagingRepository extends CrudRepository<ShortDescriptionEntity, Long> {

  List<ShortDescriptionEntity> findByClientAndMaterial(final String client, final String materialCode);

  List<ShortDescriptionEntity> findByClientAndMaterialIn(final String client, final List<String> materialCode);

  @Query(value = "UPDATE ShortDescriptionEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE client = :client AND synchronizationState = :stateOld AND material in (:codes)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("client") String client,
                   @Param("stateOld") String stateOld,
                   @Param("codes") List<String> codes);
}
