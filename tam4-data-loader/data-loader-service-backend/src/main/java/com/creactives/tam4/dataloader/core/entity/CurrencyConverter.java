package com.creactives.tam4.dataloader.core.entity;

import com.creactives.tam4.common.utils.currency.CurrencyUtils;
import com.creactives.tam4.dataloader.configuration.CurrencyConverterConfiguration;
import com.creactives.tam4.dataloader.core.exceptions.MissingExchangeRateException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Log4j2
@Data
public class CurrencyConverter {

  @Autowired
  private final CurrencyConverterConfiguration currencyConverterConfiguration;
  @Autowired
  private final CurrencyUtils currencyUtils;

  //Non + letti da application.yml, ma caricati da mothership
  private Map<String, Double> rates;

  @PostConstruct
  public void init() {
    final String defaultCurrency = getDefaultCurrency();
    log.info("Reading currency from mothership... START");
    rates = currencyUtils.getRates(defaultCurrency);

    if (rates == null) {
      throw new RuntimeException("Cannot load currency from mothership!!");
    }

    if (log.isDebugEnabled()) {
      for (final Map.Entry<String, Double> entry : rates.entrySet()) {
        log.debug("Conversion rate for {} to {} is {}", entry.getKey(), defaultCurrency, entry.getValue());
      }
    }

    log.info("Reading currency from mothership... END");
  }

  public String getDefaultCurrency() {
    return currencyConverterConfiguration.getDefaultCurrency();
  }

  public BigDecimal convertToDefaultCurrency(final String currency, final BigDecimal originalValue) {
    return convertToCurrency(currency, originalValue, getDefaultCurrency());
  }

  public BigDecimal convertToCurrency(final String originalCurrency, final BigDecimal originalValue,
                                      final String targetCurrency) {
    if (originalValue == null) {
      return null;
    }
    if (originalCurrency == null || originalCurrency.isEmpty()) {
      throw new IllegalStateException("Original Currency is null.");
    }
    if (targetCurrency == null || targetCurrency.isEmpty()) {
      throw new IllegalStateException("Target Currency is null.");
    }

    if (targetCurrency.equals(originalCurrency)) {
      return originalValue;
    }
    final String defaultCurrency = getDefaultCurrency();
    if (!rates.containsKey(originalCurrency) && !originalCurrency.equals(defaultCurrency)) {
      log.info("Cannot find rate for original Currency: {}", originalCurrency);
      throw new MissingExchangeRateException(originalCurrency);
    }
    if (!rates.containsKey(targetCurrency) && !targetCurrency.equals(defaultCurrency)) {
      log.info("Cannot find rate for target Currency: {}", targetCurrency);
      throw new MissingExchangeRateException(targetCurrency);
    }

    final Double rateOriginalCurrencyDefaultOne = getRateForCurrency(originalCurrency);
    final BigDecimal originalValueInDefaultCurrency;
    if (rateOriginalCurrencyDefaultOne == 1.0d) {
      // avoid multiplication when rate is 1
      originalValueInDefaultCurrency = originalValue;
    } else {
      originalValueInDefaultCurrency = originalValue.multiply(BigDecimal.valueOf(rateOriginalCurrencyDefaultOne));
    }

    final double rateDefaultCurrencyTargetOne = getInvertedRateCurrency(targetCurrency);
    if (rateDefaultCurrencyTargetOne == 1.0d) {
      // avoid multiplication when rate is 1
      return originalValueInDefaultCurrency;
    } else {
      return originalValueInDefaultCurrency.multiply(BigDecimal.valueOf(rateDefaultCurrencyTargetOne));
    }
  }

  private double getInvertedRateCurrency(final String targetCurrency) {
    final Double rateForCurrency = getRateForCurrency(targetCurrency);
    if (rateForCurrency != null) {
      return 1 / rateForCurrency;
    } else {
      throw new MissingExchangeRateException(targetCurrency);
    }
  }

  @Nullable
  private Double getRateForCurrency(final String currency) {
    Double rateOriginalCurrencyDefaultOne = rates.get(currency);
    if (rateOriginalCurrencyDefaultOne == null && currency.equals(getDefaultCurrency())) {
      rateOriginalCurrencyDefaultOne = 1.0;
    }
    return rateOriginalCurrencyDefaultOne;
  }

  public boolean isAssumeDefaultCurrencyWhenMissing() {
    return currencyConverterConfiguration.isAssumeDefaultCurrencyWhenMissing();
  }
}
