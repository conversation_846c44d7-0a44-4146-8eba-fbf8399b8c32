package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materials.includeinsemantic;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 8/27/2019 3:18 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */

@Log4j2
@Component
@RequiredArgsConstructor
@StepScope
public class MaterialsToIncludeInSemanticItemWriter implements ItemWriter<MaterialToIncludeInSemantics> {
//    private final MaterialStagingRepository repository;

  private final NamedParameterJdbcTemplate jdbcTemplate;

  @Override
  public void write(final List<? extends MaterialToIncludeInSemantics> items) throws Exception {
    final Map[] batchParams = items.stream()
        .map(i -> Map.of("client", i.getClient(), "materialCode", i.getMaterialCode()))
        .collect(Collectors.toList())
        .toArray(new Map[]{});

    jdbcTemplate.batchUpdate("update materials_data_staging "
        + " set semantically_analyzed = true "
        + " where client = :client "
        + " and material_code = :materialCode ", batchParams);


  }
}
