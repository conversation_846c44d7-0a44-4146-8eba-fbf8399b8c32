package com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsEntity;
import com.creactives.tam4.messaging.materials.commands.CountryDefinitionUpsertRequestMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class ConvertAggregateToCountryDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForCountryDefinition, CountryDefinitionUpsertRequestMessage> {

  private static String getDescription(final CountryDefinitionsEntity countryDefinitionsEntity) {
    if (StringUtils.isNotBlank(countryDefinitionsEntity.getLongDescription())) {
      return countryDefinitionsEntity.getLongDescription();
    } else {
      if (StringUtils.isBlank(countryDefinitionsEntity.getDescription())) {
        //o mi spacco
        throw new UnsupportedOperationException("Unable to get description for country: " + countryDefinitionsEntity.getCountry());
//        o metto desc = codice
//        return countryDefinitionsEntity.getCountry();
      } else {
        return countryDefinitionsEntity.getDescription();
      }
    }
  }

  @Override
  public CountryDefinitionUpsertRequestMessage process(final AggregateDataForCountryDefinition aggregateDataForCountryDefinition) throws Exception {
    return CountryDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForCountryDefinition.getCountryDefinitionsKeyEntity().getClient())
        .country(aggregateDataForCountryDefinition.getCountryDefinitionsKeyEntity().getCountry())
        .descriptions(aggregateDataForCountryDefinition.getCountryEntities().stream()
            .filter(countryEntity -> countryEntity.getLanguage() != null)
            .collect(Collectors.toMap(o -> o.getLanguage(),
                ConvertAggregateToCountryDefinitionRequestItemProcessor::getDescription
            ))
        )
        .enabled(aggregateDataForCountryDefinition.getCountryDefinitionsKeyEntity().getEnabled())
        .build();
  }
}
