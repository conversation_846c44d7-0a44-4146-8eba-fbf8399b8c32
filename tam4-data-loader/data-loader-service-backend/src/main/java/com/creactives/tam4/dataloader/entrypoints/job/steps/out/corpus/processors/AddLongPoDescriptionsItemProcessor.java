package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.core.entity.LongDescriptionCodes;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
@RequiredArgsConstructor
@Component
public class AddLongPoDescriptionsItemProcessor implements ItemProcessor<Material, Material> {

  private final LongDescriptionStagingRepository longDescriptionStagingRepository;


  private static String getText(final LongDescriptionEntity description) {
    if (StringUtils.isNotBlank(description.getLongDescription())) {
      return description.getLongDescription();
    }
    return description.getLongDescription();
  }

  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final List<LongDescriptionEntity> descriptions = longDescriptionStagingRepository.findByClientAndMaterialAndType(item.getClient(), item.getMaterialCode(), LongDescriptionCodes.BEST.name());
    final Map<String, String> longDescriptionsPO = new HashMap<>();

    for (final LongDescriptionEntity description : descriptions) {
      final String lang = description.getLanguage();
      final String text = getText(description);
      longDescriptionsPO.put(lang, text);
    }
    return Material.from(item)
        .longDescriptionPO(longDescriptionsPO)
        .build();
  }
}
