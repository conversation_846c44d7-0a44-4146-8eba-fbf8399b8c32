package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials;

import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GetTotalAnalysedMaterialsUseCase {

  private final GetTotalMaterialsInDatabase getTotalMaterialsInDatabase;

  public DataAnalyseResponse totalAnalysedMaterials(final String client) {
    return DataAnalyseResponse.builder()
        .code("total-analysed-materials")
        .rowCount(getTotalMaterialsInDatabase.totalAnalysedMaterials(client, true))
        .build();
  }
}
