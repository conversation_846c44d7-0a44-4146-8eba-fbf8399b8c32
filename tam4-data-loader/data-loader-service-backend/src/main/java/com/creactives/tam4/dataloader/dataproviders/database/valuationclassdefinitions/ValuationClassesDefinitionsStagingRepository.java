package com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ValuationClassesDefinitionsStagingRepository extends PagingAndSortingRepository<ValuationClassDefinitionsEntity, Long> {

  List<ValuationClassDefinitionsEntity> findByClientAndValuationClass(String client, String valuationClass);

  List<ValuationClassDefinitionsEntity> findByClientAndValuationClassIn(String client, List<String> valuationClass);

  List<ValuationClassDefinitionsEntity> findByClientAndValuationClassAndSynchronizationState(String client, String valuationClass, String synchronizationState);
}
