package com.creactives.tam4.dataloader.entrypoints.job.steps.out.basicmaterial;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.basicmaterial.BasicMaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.BasicMaterialUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.CompositeItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SendBasicMaterialAddOrUpateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToBasicMaterialRequestItemProcessor convertEntityToBasicMaterialRequestItemProcessor;
  private final BasicMaterialItemReader itemReader;
  private final BasicMaterialWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-basic-material-add-or-update")
        .<BasicMaterialEntity, BasicMaterialUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(convertEntityToBasicMaterialRequestItemProcessor)
        .writer(buildCompositeItemWriter())
        .listener(new StatisticsListener())
        .build();
  }

  private CompositeItemWriter<BasicMaterialUpsertRequestMessage> buildCompositeItemWriter() {
    return new CompositeItemWriterBuilder<BasicMaterialUpsertRequestMessage>()
        .delegates(List.of(writer, updateStagingTable("basic_material_staging", "basic_material")))
        .build();
  }

  private JdbcBatchItemWriter<BasicMaterialUpsertRequestMessage> updateStagingTable(final String tableName, final String keyColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, keyColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getBasicMaterial());
    });
  }


}
