package com.creactives.tam4.dataloader.entrypoints.job.steps.out.senddb;

import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class SendMessagesWriter implements ItemWriter<ObjectMessage> {
  private final WriteMessageService sendMessages;
  private final JdbcTemplate jdbcTemplate;


  @Override
  public void write(final List<? extends ObjectMessage> list) throws Exception {
    final Map<String, Long> map = list.stream().map(it -> it.getMessage().getClass().getSimpleName()).collect(Collectors.groupingBy(it -> it, Collectors.counting()));
    map.forEach((messageType, counter) -> log.info("Sending {} messages of type {}", counter, messageType));
    for (final ObjectMessage objectMessage : list) {
      sendMessages.forcefullySend(objectMessage.getExchange(), objectMessage.getMessage());
    }
    setSentFlag(list);
  }

  private void setSentFlag(final List<? extends ObjectMessage> list) {
    final Optional<Long> maxId = list.stream().map(ObjectMessage::getId).max(Comparator.naturalOrder());
    maxId.ifPresent(it -> jdbcTemplate.update("update outbound_queue set sent=true where sent=false and id <= ?", it));

  }
}
