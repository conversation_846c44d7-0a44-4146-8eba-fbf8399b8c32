package com.creactives.tam4.dataloader.entrypoints.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionHistory {

  private long executionId;

  private String jobName;

  private Date startTime;

  private Date endTime;

  private String exitStatus;


}
