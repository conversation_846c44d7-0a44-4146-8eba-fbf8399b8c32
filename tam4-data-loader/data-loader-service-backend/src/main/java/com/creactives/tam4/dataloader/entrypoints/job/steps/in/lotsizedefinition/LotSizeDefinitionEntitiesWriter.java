package com.creactives.tam4.dataloader.entrypoints.job.steps.in.lotsizedefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class LotSizeDefinitionEntitiesWriter implements ItemWriter<LotSizeDefinitionEntity> {

  private final LotSizeDefinitionStagingRepository lotSizeStagingRepository;

  @Override
  public void write(final List<? extends LotSizeDefinitionEntity> list) throws Exception {
    for (final LotSizeDefinitionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    lotSizeStagingRepository.saveAll(list);
  }
}
