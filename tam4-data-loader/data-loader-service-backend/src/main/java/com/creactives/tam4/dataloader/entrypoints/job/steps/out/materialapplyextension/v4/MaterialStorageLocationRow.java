package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MaterialStorageLocationRow implements Serializable {
  @JsonAlias("id")
  private long id;
  @JsonAlias("client")
  private String client;
  @JsonAlias("material_code")
  private String materialCode;
  @JsonAlias("plant_code")
  private String plantCode;
  @JsonAlias("storage_location")
  private String storageLocation;
  @JsonAlias("valuated_unrestricted_use_stock")
  private BigDecimal valuatedUnrestrictedUseStock;
  @JsonAlias("stock_in_transfer")
  private BigDecimal stockInTransfer;
  @JsonAlias("stock_in_quality_inspection")
  private BigDecimal stockInQualityInspection;
  @JsonAlias("blocked_stock")
  private BigDecimal blockedStock;
  @JsonAlias("storage_bin")
  private String storageBin;
  @JsonAlias("maintenance_status")
  private String maintenanceStatus;
  @JsonAlias("flag_material_for_deletion_at_storage_location_level")
  private String flagMaterialForDeletionAtStorageLocationLevel;
  @JsonAlias("avato_log_system")
  private String avatoLogSystem;
  @JsonAlias("avato_version")
  private String avatoVersion;
  @JsonAlias("avato_last_sync")
  private String avatoLastSync;
  @JsonAlias("avato_sequence")
  private String avatoSequence;
  @JsonAlias("created_on")
  private Timestamp createdOn;
  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;
  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;
  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;
  @JsonAlias("synchronization_state")
  private String synchronizationState;

  public String getPlantKey() {
    return this.client + "/" + this.plantCode;
  }
}
