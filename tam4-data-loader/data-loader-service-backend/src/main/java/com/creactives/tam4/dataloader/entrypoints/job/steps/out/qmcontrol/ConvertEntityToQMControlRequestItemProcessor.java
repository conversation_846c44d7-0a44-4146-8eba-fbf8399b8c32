package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcontrol;


import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions.QMControlDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions.QMControlDefinitionsStagingRepository;
import com.creactives.tam4.messaging.materials.commands.QMControlUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class ConvertEntityToQMControlRequestItemProcessor implements ItemProcessor<QMControlEntity, QMControlUpsertRequestMessage> {


  private final QMControlDefinitionsStagingRepository qmControlDefinitionsStagingRepository;
  @Override
  public QMControlUpsertRequestMessage process(final QMControlEntity qmControlEntity) throws Exception {
    final List<QMControlDefinitionsEntity> qmTextEntities = qmControlDefinitionsStagingRepository.findByClientAndQmControlKey(qmControlEntity.getClient(), qmControlEntity.getQmControlKey());

    return QMControlUpsertRequestMessage.builder()
        .client(qmControlEntity.getClient())
        .blockInactive(qmControlEntity.getBlockInactive())
        .blockInvoice(qmControlEntity.getBlockInvoice())
        .certificationRequired(qmControlEntity.getCertificationRequired())
        .controlGR(qmControlEntity.getControlGR())
        .documentation(qmControlEntity.getDocumentation())
        .notificationType(qmControlEntity.getNotificationType())
        .qmControlKey(qmControlEntity.getQmControlKey())
        .qualityAssuranceAgreement(qmControlEntity.getQualityAssuranceAgreement())
        .rejectionGR(qmControlEntity.getRejectionGR())
        .technicalDeliveryTerms(qmControlEntity.getTechnicalDeliveryTerms())
        .vendorRelease(qmControlEntity.getVendorRelease())
        .shortText(qmTextEntities.stream()
            .filter(qmTextEntity -> qmTextEntity.getLanguage() != null)
            .collect(Collectors.toMap(o -> o.getLanguage(),
                QMControlDefinitionsEntity::getShortText
            )))
        .enabled(qmControlEntity.getEnabled())
        .build();
  }
}
