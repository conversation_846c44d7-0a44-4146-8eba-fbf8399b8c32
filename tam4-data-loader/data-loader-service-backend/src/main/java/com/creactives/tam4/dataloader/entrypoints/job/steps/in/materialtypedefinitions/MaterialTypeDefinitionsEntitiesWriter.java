package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialtypedefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition.MaterialTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition.MaterialTypeDefinitionStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialTypeDefinitionsEntitiesWriter implements ItemWriter<MaterialTypeDefinitionEntity> {

  private final MaterialTypeDefinitionStagingRepository materialTypeDefinitionStagingRepository;
  private final MaterialTypesStagingRepository materialTypeStagingRepository;


  @Override
  public void write(final List<? extends MaterialTypeDefinitionEntity> materialTypeEntities) throws Exception {
    for (final MaterialTypeDefinitionEntity entity :
        materialTypeEntities) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    materialTypeDefinitionStagingRepository.saveAll(materialTypeEntities);
    for (final MaterialTypeDefinitionEntity materialTypeEntity : materialTypeEntities) {
      if (materialTypeStagingRepository.findByClientAndMaterialType(materialTypeEntity.getClient(), materialTypeEntity.getMaterialType()).isEmpty()) {
        materialTypeStagingRepository.save(MaterialTypesEntity.builder()
            .client(materialTypeEntity.getClient())
            .materialType(materialTypeEntity.getMaterialType())
            .createdOn(new Timestamp(System.currentTimeMillis()))
            .enabled(true)
            .synchronizationState(SyncStatus.PENDING.getCode())
            .build());
      }
    }
  }
}
