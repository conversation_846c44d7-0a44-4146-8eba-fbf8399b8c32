package com.creactives.tam4.dataloader.dataproviders.database.approvedmanufacturerparts;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "approved_manufactured_parts_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "AMPL", description = "Table of Approved Manufacturer Parts")
public class ApprovedManufacturedPartsEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "approved_manufactured_parts_staging_generator")
  @SequenceGenerator(name = "approved_manufactured_parts_staging_generator", sequenceName = "approved_manufactured_parts_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(sapField = "MANDT",
      referenceTable = "T000",
      description = "Client",
      required = true
  )
  private String client;

  @Column(name = "record_number")
  @FieldDoc(sapField = "AMPNR",
      referenceTable = "MARA",
      description = "Number of AMPL record",
      required = true
  )
  private String recordNumber;

  @Column(name = "material_number")
  @FieldDoc(sapField = "BMATN",
      referenceTable = "MARA",
      description = "Number of firm's own (internal) inventory-managed material",
      required = true
  )
  private String materialNumber;

  @Column(name = "plant")
  @FieldDoc(sapField = "WERKS",
      referenceTable = "T001W",
      description = "Plant"
  )
  private String plant;

  @Column(name = "manufacturer_number")
  @FieldDoc(sapField = "MFRNR",
      referenceTable = "LFA1",
      description = "Number of a Manufacturer"
  )
  private String manufacturerNumber;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
