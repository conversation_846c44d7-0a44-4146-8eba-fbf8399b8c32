package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;


import com.creactives.tam4.common.utils.TamStringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
public class MaterialApplyExtensionRowMapper implements RowMapper<MaterialApplyExtensionRow> {


  @Override
  public MaterialApplyExtensionRow mapRow(final ResultSet rs, final int rowNum) throws SQLException {

    return MaterialApplyExtensionRow
        .builder()
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .materialPlantData(mapMaterialPlantData(rs.getString("mpds_data")))
        .materialPlantStorageLocationData(mapMaterialPlantStorageLocationData(rs.getString("msls_data")))
        .build();
  }


  private List<MaterialPlantRow> mapMaterialPlantData(final String json) {
    return TamStringUtils.jsonToListOfObject(json, new TypeReference<List<MaterialPlantRow>>() {
    });
  }

  private List<MaterialStorageLocationRow> mapMaterialPlantStorageLocationData(final String json) {

    return TamStringUtils.jsonToListOfObject(json, new TypeReference<List<MaterialStorageLocationRow>>() {
    });

  }
}
