package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialgroup;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MaterialGroupDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class MaterialGroupWriter implements ItemWriter<MaterialAndMessageCouple> {

  private final WriteMessageService sendMessages;

  public MaterialGroupWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }

  @Override
  public void write(final List<? extends MaterialAndMessageCouple> list) throws Exception {
    final List<MaterialGroupDefinitionUpsertRequestMessage> messages = list.stream().map(MaterialAndMessageCouple::getMaterialGroupDefinitionUpsertRequestMessage).collect(Collectors.toList());

    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, messages);
  }
}
