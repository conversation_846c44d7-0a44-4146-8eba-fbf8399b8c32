package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionStagingRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@StepScope
public class CalculateAggregateForProductDivisionDefinition implements ItemProcessor<ProductDivisionKeyEntity, AggregateDataForProductDivisionDefinition> {

  private final ProductDivisionStagingRepository productDivisionStagingRepository;
  private final boolean ignoreStatus;

  public CalculateAggregateForProductDivisionDefinition(final ProductDivisionStagingRepository productDivisionStagingRepository,
                                                        @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.productDivisionStagingRepository = productDivisionStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  public AggregateDataForProductDivisionDefinition process(final ProductDivisionKeyEntity productDivisionKeyEntity) throws Exception {
    final List<ProductDivisionEntity> entities = ignoreStatus ?
        productDivisionStagingRepository.findAllByClientAndDivision(productDivisionKeyEntity.getClient(),
            productDivisionKeyEntity.getDivision())
        : productDivisionStagingRepository.findAllByClientAndDivisionAndSynchronizationState(productDivisionKeyEntity.getClient(),
        productDivisionKeyEntity.getDivision(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForProductDivisionDefinition.builder()
        .productDivisionKeyEntity(productDivisionKeyEntity)
        .productDivisionEntities(entities)
        .build();
  }
}
