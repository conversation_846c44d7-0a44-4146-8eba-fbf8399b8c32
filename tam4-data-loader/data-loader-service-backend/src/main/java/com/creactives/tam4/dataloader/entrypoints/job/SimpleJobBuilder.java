package com.creactives.tam4.dataloader.entrypoints.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.flow.FlowExecutionStatus;
import org.springframework.batch.core.job.flow.JobExecutionDecider;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.util.StringUtils;

import java.util.Objects;
@RequiredArgsConstructor
@Log4j2
public class SimpleJobBuilder {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private Runnable onDelete;
  private Runnable onInit;
  private Step step;
  private String table;
  private String category;
  private String description;

  private static JobExecutionDecider decider() {
    return (jobExecution, stepExecution) -> {
      if (Boolean.parseBoolean(getReloadParameter(jobExecution))) {
        return FlowExecutionStatus.COMPLETED;
      } else {
        return FlowExecutionStatus.FAILED;
      }
    };


  }

  private static String getReloadParameter(final JobExecution jobExecution) {
    return jobExecution.getJobParameters().getString("reload", "false");
  }

  public SimpleJobBuilder setTable(final String table) {
    this.table = table;
    return this;
  }

  public SimpleJobBuilder setCategory(final String category) {
    this.category = category;
    return this;
  }

  public SimpleJobBuilder setDescription(final String description) {
    this.description = description;
    return this;
  }

  public SimpleJobBuilder onInit(final Runnable onInit) {
    this.onInit = Objects.requireNonNull(onInit);
    return this;
  }

  public SimpleJobBuilder onDelete(final Runnable onDelete) {
    this.onDelete = Objects.requireNonNull(onDelete);
    return this;
  }

  public SimpleJobBuilder loadStep(final StepConfigurer step) {
    this.step = Objects.requireNonNull(step.configureStep());
    return this;
  }

  public SimpleJobBuilder loadStep(final Step step) {
    this.step = step;
    return this;
  }

  public JobFactory build(final String name) {
    final JobMetadata jobMetadata = new JobMetadata(name);
    if (StringUtils.hasText(category)) {
      jobMetadata.setCategory(category);
    }
    jobMetadata.setDescription(description);
    jobMetadata.setTable(table);
    return new SupplierJobFactory(name, jobMetadata, () -> {
      final Step initPipeline = stepBuilderFactory
          .get(name + "/start")
          .tasklet((contribution, chunkContext) -> {
            log.info("Starting {}", name);
            if (onInit != null) {
              onInit.run();
            }
            return RepeatStatus.FINISHED;
          }).build();

      final Step clearTables = stepBuilderFactory
          .get(name + "/delete")
          .tasklet((contribution, chunkContext) -> {
            if (onDelete != null) {
              onDelete.run();
            }
            return RepeatStatus.FINISHED;
          }).build();

      final JobExecutionDecider shouldDeleteAll = decider();
      return jobBuilderFactory.get(name)
          .start(initPipeline)
          .next(shouldDeleteAll)
          .on("COMPLETED")
          .to(clearTables)
          .next(step)
          .from(shouldDeleteAll)
          .on("FAILED")
          .to(step)
          .end()
          .build();
    });
  }
}
