package com.creactives.tam4.dataloader.dataproviders.database.productdivision;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductDivisionStagingRepository extends PagingAndSortingRepository<ProductDivisionEntity, Long> {

  List<ProductDivisionEntity> findAllByClientAndDivision(String client, String division);

  List<ProductDivisionEntity> findAllByClientInAndDivisionIn(List<String> client, List<String> division);

  //TODO:SAPKEYS Organizational Unit: Sales Divisions: Texts 44
  List<ProductDivisionEntity> findAllByClientAndLanguageAndDivisionIn(String client, String language, List<String> divisions);

  List<ProductDivisionEntity> findAllByClientAndDivisionAndSynchronizationState(String client, String division, String status);
}
