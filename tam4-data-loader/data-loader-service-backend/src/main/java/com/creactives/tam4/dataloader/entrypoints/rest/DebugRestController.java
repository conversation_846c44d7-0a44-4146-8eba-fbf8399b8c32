package com.creactives.tam4.dataloader.entrypoints.rest;

import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/data-loader/api")
@Log4j2
public class DebugRestController {

//
//  private final PollInboundTableUseCase pollInboundTableUseCase;
//
//  public DebugRestController(PollInboundTableUseCase pollInboundTableUseCase) {
//
//    this.pollInboundTableUseCase = pollInboundTableUseCase;
//  }
//
//  @PostMapping("/poll-inbound-queue-now")
//  @ResponseBody
//  public void pollInboundQueueNow() {
//    pollInboundTableUseCase.poll();
//  }

}
