package com.creactives.tam4.dataloader.core.entity;

import com.creactives.tam4.dataloader.configuration.DataLoaderConfiguration;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Created on 8/27/2019 3:33 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MaterialCodeNormalizer {

  private final DataLoaderConfiguration dataLoaderConfiguration;

  public String addZeroes(final String materialCode) {
/*    if (dataLoaderConfiguration.isDoPadding()) {
      return StringUtils.leftPad(materialCode, 18, "0");
    }*/
    return materialCode;
  }

  public String removeZeroes(final String materialCode) {
/*    if (dataLoaderConfiguration.isDoPadding()) {
      return StringUtils.stripStart(materialCode, "0");
    }*/
    return materialCode;
  }

}
