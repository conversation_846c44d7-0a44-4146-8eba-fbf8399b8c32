package com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface CharacteristicsCABNStagingRepository extends PagingAndSortingRepository<CharacteristicsCABNEntity, Long> {

  List<CharacteristicsCABNEntity> findAllByClientAndCode(String client, String code);

  List<CharacteristicsCABNEntity> findAllByClientAndCodeIn(String client, Set<String> codes);
}
