package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialgroupdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions.MaterialGroupDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions.MaterialGroupDefinitionsStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created on 6/11/2019 5:02 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialGroupDefinitionsEntitiesWriter implements ItemWriter<MaterialGroupDefinitionEntity> {

  private final MaterialGroupStagingRepository materialGroupStagingRepository;
  private final MaterialGroupDefinitionsStagingRepository repository;

  @Override
  public void write(final List<? extends MaterialGroupDefinitionEntity> items) throws Exception {
    for (final MaterialGroupDefinitionEntity entity :
        items) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    repository.saveAll(items);
    for (final MaterialGroupDefinitionEntity item : items) {
      if (materialGroupStagingRepository.findByClientAndMaterialGroup(item.getClient(), item.getMaterialGroup()).isEmpty()) {
        materialGroupStagingRepository.save(MaterialGroupEntity.builder()
            .client(item.getClient())
            .materialGroup(item.getMaterialGroup())
            .createdOn(new Timestamp(System.currentTimeMillis()))
            .synchronizationState(SyncStatus.PENDING.getCode())
            .enabled(true)
            .build());
      }
    }
  }
}
