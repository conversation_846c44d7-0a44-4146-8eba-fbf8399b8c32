package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class CreateMaterialProcessor implements ItemProcessor<MaterialEntity, Material> {

  @Override
  public Material process(final MaterialEntity item) throws Exception {
    return Material
        .builder()
        .client(item.getClient())
        .materialCode(item.getMaterialCode())
        .materialGroupId(item.getMaterialGroup())
        .deletionFlag(item.isDeletionFlag())
        .manufacturer(item.getManufacturerCode())
        .manufacturerPartNumber(item.getManufacturerPartNumber())
        .oldMaterialNumber(item.getOldMaterialNumber())
        .semanticallyAnalyzed(item.isSemanticallyAnalyzed())
        .mdDomain(item.getMdDomain())

        // added to comply with A2A requirements
        .materialType(item.getMaterialType())
        .industrySector(item.getIndustrySector())
        .materialGroup(item.getMaterialGroup())
        .baseUnitOfMeasurement(item.getBaseUnitOfMeasurement())
        .productDivision(item.getProductDivision())
        .authorizationGroup(item.getAuthorizationGroup())
        .crossPlantMaterialStatus(item.getCrossPlantMaterialStatus())
        .materialStatusValidFromDate(item.getMaterialStatusValidFromDate())
        .manufacturerCode(item.getManufacturerCode())
        .genericItemGroup(item.getGenericItemGroup())
        .revisionNumber(item.getRevisionNumber())
        .purchasingMeasurementUnits(item.getPurchasingMeasurementUnits())
        .materialCreatedOn(item.getMaterialCreatedOn())
        .externalMaterialGroup(item.getExternalMaterialGroup())
        .weightUnit(item.getWeightUnit())
        .netWeight(item.getNetWeight())
        .grossWeight(item.getGrossWeight())
        .sizeDimension(item.getSizeDimension())
        .hazardousMaterialNumber(item.getHazardousMaterialNumber())
        .basicMaterial(item.getBasicMaterial())
        .documentNumber(item.getDocumentNumber())
        .batchManagementRequirementIndicator(item.isBatchManagementRequirementIndicator())
        .laboratory(item.getLaboratory())

        //FIXME: Custom fields required by A2A T4-1756
        .famiglia(item.getFamiglia())
        .sottoFamiglia(item.getSottoFamiglia())
        .specificaTecnica(item.getSpecificaTecnica())
        .edizione(item.getEdizione())
        .revisione(item.getRevisione())
        .dataCustom(item.getDataCustom())

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(item.getProductHierarchy())

        .build();
  }
}
