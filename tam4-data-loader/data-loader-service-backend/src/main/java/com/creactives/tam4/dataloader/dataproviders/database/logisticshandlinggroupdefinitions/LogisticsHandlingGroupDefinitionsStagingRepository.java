package com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LogisticsHandlingGroupDefinitionsStagingRepository extends PagingAndSortingRepository<LogisticsHandlingGroupEntity, Long> {

  List<LogisticsHandlingGroupEntity> findByClientInAndLogisticsHandlingGroupIn(List<String> client, List<String> logisticsHandlingGroup);

  List<LogisticsHandlingGroupEntity> findByClientAndLanguageAndLogisticsHandlingGroupIn(String client, String language, List<String> logisticsHandlingGroups);

  @Modifying
  @Query("update LogisticsHandlingGroupEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update LogisticsHandlingGroupEntity set enabled= :enabled WHERE logisticsHandlingGroup=:logisticsHandlingGroup AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("logisticsHandlingGroup") String logisticsHandlingGroup, @Param("client") String client);

  Page<LogisticsHandlingGroupEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<LogisticsHandlingGroupEntity> findAllByClient(String client, Pageable pageable);

  Page<LogisticsHandlingGroupEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
