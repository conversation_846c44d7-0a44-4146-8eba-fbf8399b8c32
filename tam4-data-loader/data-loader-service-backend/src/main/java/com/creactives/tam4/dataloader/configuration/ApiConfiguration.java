package com.creactives.tam4.dataloader.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "data-loader.api")
@Data
public class ApiConfiguration {

  private boolean restrictValidation;

  private boolean restrictClientCheck;

  private Map<String, Boolean> messageTypesToSkip = new HashMap<>();

}
