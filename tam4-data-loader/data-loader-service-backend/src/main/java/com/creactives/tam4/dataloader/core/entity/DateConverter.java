package com.creactives.tam4.dataloader.core.entity;

import com.creactives.tam4.dataloader.configuration.DataLoaderConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * Created on 7/31/2019 11:57 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Log4j2
public class DateConverter {
  //questo è lo stesso di POISheet
  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
  private final DataLoaderConfiguration dataLoaderConfiguration;

  private static Long convertDate(final String sapDate, final String format) {
    if (sapDate == null ||
        StringUtils.isBlank(sapDate) ||
        "00000000".equals(sapDate) ||
        "99999999".equals(sapDate) ||
        "00.00.0000".equals(sapDate)
    ) {
      return null;
    }
    final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

    try {
      final LocalDate localDate = LocalDate.parse(sapDate, formatter);
      return localDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
    } catch (final Exception e) {
      log.error("Exception while converting {} using formatter {}", sapDate, format);
      throw e;
    }
  }

  public static Long convertToMillis(final String millis) {
    if (millis == null || StringUtils.isBlank(millis) || "00000000".equals(millis) || "00/00/00".equals(millis)) {
      return null;
    }
    try {
      final LocalDate localDate = LocalDate.parse(millis, DATE_FORMATTER);
      return localDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
    } catch (final Exception e) {
      throw new UnsupportedOperationException("Impossible to parse date " + millis + " -> " + e.getMessage());
    }
  }

  public Long convertValidFromDate(final String sapDate) {
    return convertDate(sapDate, dataLoaderConfiguration.getValidFromDateExpectedFormat());
  }

  public Long convertEffectiveOutDate(final String sapDate) {
    return convertDate(sapDate, dataLoaderConfiguration.getEffectiveOutDateExpectedFormat());
  }

  public Long convertRevisionNumber(final String sapDate) {
    return convertDate(sapDate, dataLoaderConfiguration.getRevisionNumberExpectedFormat());
  }

  public Long convertCreatedOn(final String sapDate) {
    return convertDate(sapDate, dataLoaderConfiguration.getCreatedOnExpectedFormat());
  }


}
