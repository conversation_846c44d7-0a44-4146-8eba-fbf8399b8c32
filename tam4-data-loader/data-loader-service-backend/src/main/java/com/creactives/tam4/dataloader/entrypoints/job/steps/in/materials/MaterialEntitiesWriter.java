package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materials;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created on 6/4/2019 3:36 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialEntitiesWriter implements ItemWriter<MaterialEntity> {

  private final MaterialStagingRepository materialStagingRepository;

  @Override
  public void write(final List<? extends MaterialEntity> materialEntities) {
    for (final MaterialEntity entity : materialEntities) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    materialStagingRepository.saveAll(materialEntities);
  }
}
