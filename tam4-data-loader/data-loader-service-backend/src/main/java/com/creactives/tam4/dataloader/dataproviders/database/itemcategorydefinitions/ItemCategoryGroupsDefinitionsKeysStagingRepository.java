package com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ItemCategoryGroupsDefinitionsKeysStagingRepository extends PagingAndSortingRepository<ItemCategoryGroupDefinitionsKeyEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<ItemCategoryGroupDefinitionsKeyEntity> findByClientAndItemCategoryGroup(String client, String ItemCategoryGroup);

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  List<ItemCategoryGroupDefinitionsKeyEntity> findByClientAndItemCategoryGroupIn(String client, List<String> ItemCategoryGroups);

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  <S extends ItemCategoryGroupDefinitionsKeyEntity> @NotNull S save(@NotNull S entity);

  @Modifying
  @Query("update ItemCategoryGroupDefinitionsKeyEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update ItemCategoryGroupDefinitionsKeyEntity set enabled= :enabled WHERE itemCategoryGroup=:itemCategoryGroup AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("itemCategoryGroup") String itemCategoryGroup, @Param("client") String client);

  Page<ItemCategoryGroupDefinitionsKeyEntity> findAllByClient(String client, Pageable pageable);
}
