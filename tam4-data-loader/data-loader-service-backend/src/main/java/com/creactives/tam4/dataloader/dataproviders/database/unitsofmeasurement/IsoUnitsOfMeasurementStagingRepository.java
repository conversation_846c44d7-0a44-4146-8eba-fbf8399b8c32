package com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IsoUnitsOfMeasurementStagingRepository extends PagingAndSortingRepository<ISOUnitsOfMeasurementEntity, Long> {

  List<ISOUnitsOfMeasurementEntity> findByClientAndIsoUnitsOfMeasurementIn(String client, List<String> unitsOfMeasurement);
}
