package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SetMaterialPlantSynchronizedFields implements ItemProcessor<AggregateDataForMaterialExtension, AggregateDataForMaterialExtension> {
  private final MaterialPlantStagingRepository materialPlantStagingRepository;

  @Override
  @Transactional
  public AggregateDataForMaterialExtension process(final AggregateDataForMaterialExtension aggregateDataForMaterialExtension) throws Exception {
    final List<Long> materialPlantEntityIds = aggregateDataForMaterialExtension.getMaterialPlantEntities()
        .parallelStream()
        .map(MaterialPlantEntity::getId)
        .collect(Collectors.toList());

    final List<MaterialPlantEntity> materialPlantEntities =
        materialPlantStagingRepository.findAllByIdInAndSynchronizationState(materialPlantEntityIds,
            SyncStatus.PENDING.getCode());

    materialPlantEntities.parallelStream()
        .forEach(materialPlantEntity -> {
          materialPlantEntity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
          materialPlantEntity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
        });

    return aggregateDataForMaterialExtension;
  }
}
