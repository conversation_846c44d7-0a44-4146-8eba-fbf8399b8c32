package com.creactives.tam4.dataloader.dataproviders.database.laboratory;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LaboratoryStagingRepository extends PagingAndSortingRepository<LaboratoryEntity, Long> {

  List<LaboratoryEntity> findByClientAndLaboratoryIn(String client, List<String> laboratories);

  Page<LaboratoryEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<LaboratoryEntity> findAllByClient(String client, Pageable pageable);

  Page<LaboratoryEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
