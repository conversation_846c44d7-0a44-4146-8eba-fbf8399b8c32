package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Log4j2
@StepScope
public class CalculateAggregateForMaterialStatusDefinition implements ItemProcessor<MaterialStatusEntity, AggregateDataForMaterialStatusDefinition> {

  private final MaterialStatusDefinitionsStagingRepository materialStatusDefinitionsStagingRepository;

  private final boolean ignoreStatus;

  public CalculateAggregateForMaterialStatusDefinition(final MaterialStatusDefinitionsStagingRepository materialStatusDefinitionsStagingRepository,
                                                       @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.materialStatusDefinitionsStagingRepository = materialStatusDefinitionsStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForMaterialStatusDefinition process(final MaterialStatusEntity materialStatusEntity) throws Exception {
    final List<MaterialStatusDefinitionsEntity> entities =
        ignoreStatus ?
            materialStatusDefinitionsStagingRepository.findByClientAndMaterialStatus(materialStatusEntity.getClient(),
                materialStatusEntity.getMaterialStatus())
            :
            materialStatusDefinitionsStagingRepository.findByClientAndMaterialStatusAndSynchronizationState(materialStatusEntity.getClient(),
                materialStatusEntity.getMaterialStatus(),
                SyncStatus.PENDING.getCode()
            );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForMaterialStatusDefinition.builder()
        .materialStatusEntity(materialStatusEntity)
        .materialStatusEntities(entities)
        .build();
  }
}
