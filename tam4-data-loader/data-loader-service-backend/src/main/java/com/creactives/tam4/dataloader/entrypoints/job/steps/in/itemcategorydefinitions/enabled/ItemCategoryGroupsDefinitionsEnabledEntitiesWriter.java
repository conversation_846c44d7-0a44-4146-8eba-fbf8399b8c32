package com.creactives.tam4.dataloader.entrypoints.job.steps.in.itemcategorydefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupsDefinitionsKeysStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class ItemCategoryGroupsDefinitionsEnabledEntitiesWriter implements ItemWriter<ItemCategoryGroupDefinitionsKeyEntity> {

  private final ItemCategoryGroupsDefinitionsKeysStagingRepository itemCategoryGroupsDefinitionsKeysStagingRepository;

  @Override
  public void write(final List<? extends ItemCategoryGroupDefinitionsKeyEntity> list) throws Exception {
    for (final ItemCategoryGroupDefinitionsKeyEntity entity : list) {
      if (entity.getEnabled()) {
        itemCategoryGroupsDefinitionsKeysStagingRepository.setEnabled(true, entity.getItemCategoryGroup(), entity.getClient());
      }
    }
  }
}
