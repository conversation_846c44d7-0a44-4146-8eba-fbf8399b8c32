package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialplantvaluations.pohistory;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Repository;

import java.util.List;

@RequiredArgsConstructor
@Repository
@StepScope
public class POHistoryEntitiesWriter implements ItemWriter<POHistoryEntity> {

  private final POHistoryStagingRepository poHistoryStagingRepository;

  @Override
  public void write(final List<? extends POHistoryEntity> list) throws Exception {
    for (final POHistoryEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    poHistoryStagingRepository.saveAll(list);
  }
}
