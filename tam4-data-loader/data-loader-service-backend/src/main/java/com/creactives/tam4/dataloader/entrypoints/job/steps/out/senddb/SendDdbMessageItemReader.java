package com.creactives.tam4.dataloader.entrypoints.job.steps.out.senddb;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.SimpleJdbcBatchItemReader;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class SendDdbMessageItemReader extends SimpleJdbcBatchItemReader<Long, DbMessage> {
  private final JdbcTemplate jdbcTemplate;
  private final int fetchSize = 10000;

  @Override
  protected Long getKey(final DbMessage entity) {
    return entity.getId();
  }

  @Override
  protected List<DbMessage> queryForFirstPage() {
    return jdbcTemplate.query("select id, exchange, message_type, content from outbound_queue where sent = false order by id limit ?",
        (resultSet, i) -> new DbMessage(resultSet.getLong("id"),
            resultSet.getString("exchange"),
            resultSet.getString("message_type"),
            resultSet.getString("content")

        ), fetchSize);
  }

  @Override
  protected List<DbMessage> queryAfterId(final Long id) {
    return jdbcTemplate.query("select id, exchange, message_type, content from outbound_queue where sent = false and id > ? order by id limit ?",
        (resultSet, i) -> new DbMessage(resultSet.getLong("id"),
            resultSet.getString("exchange"),
            resultSet.getString("message_type"),
            resultSet.getString("content")
        ), id, fetchSize);
  }
}
