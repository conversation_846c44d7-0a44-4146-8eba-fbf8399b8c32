package com.creactives.tam4.dataloader.core.usecase.loadclient;

import com.creactives.tam4.dataloader.configuration.Client;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;

/**
 * Created on 24/01/2020 17:24
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Service
public class ClientPathResolverUseCase {

  private final String basePath;
  private final String basePathAttachments;

  public ClientPathResolverUseCase(@Value("${data-loader.input-data-path}") final String basePath) {
    this.basePath = basePath;
    this.basePathAttachments = basePath + "/attachments";
  }

  public File getClientFolder(final Client client) {
    final String folder = client.getFolderName();
    if (StringUtils.isBlank(folder)) {
      throw new UnsupportedOperationException("Invalid client configuration: " + client);
    }
    return Path.of(basePath, folder).toFile();
  }

  public File getValidatedClientFolder(final Client client) {
    final File clientFolder = getClientFolder(client);
    if (!clientFolder.isDirectory()) {
      throw new IllegalStateException("Missing client folder for client: " + client + ". Expected path: " + clientFolder.getAbsolutePath());
    }
    return clientFolder;
  }

  public File getAttachmentsFolderForClient(final Client client) {
    final String folder = client.getFolderName();
    if (StringUtils.isBlank(folder)) {
      throw new UnsupportedOperationException("Invalid client configuration: " + client);
    }
    return Path.of(basePathAttachments, folder).toFile();
  }

  public File getBaseAttachmentsFolder() {
    return Path.of(basePathAttachments).toFile();
  }

}

