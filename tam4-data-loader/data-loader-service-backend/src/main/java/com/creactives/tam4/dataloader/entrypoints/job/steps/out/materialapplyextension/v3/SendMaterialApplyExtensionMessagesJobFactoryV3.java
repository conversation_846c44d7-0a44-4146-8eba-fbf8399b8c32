package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 7:03 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
public class SendMaterialApplyExtensionMessagesJobFactoryV3 {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMaterialApplyExtensionRequestedMessagesStepV3 step;

  public SendMaterialApplyExtensionMessagesJobFactoryV3(final JobBuilderFactory jobBuilderFactory,
                                                        final StepBuilderFactory stepBuilderFactory,
                                                        final SendMaterialApplyExtensionRequestedMessagesStepV3 step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }


  @Bean
  public JobFactory sendMaterialExtensionsFactoryV3() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Deprecated")
        .build("send-plants-v3");
  }

}
