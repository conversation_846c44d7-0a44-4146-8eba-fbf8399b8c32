package com.creactives.tam4.dataloader.entrypoints.job.steps.out.serialnumber;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.serialnumber.SerialNumberManagementEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.SerialNumberProfileValueUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
public class SendSerialNumberAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final SerialNumberItemReader serialNumberItemReader;
  private final SerialNumberWriter serialNumberWriter;
  private final ConvertEntityToSerialNumberRequestItemProcessor convertEntityToSerialNumberRequestItemProcessor;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-serial-number-management-add-or-update")
        .<SerialNumberManagementEntity, SerialNumberProfileValueUpsertRequestMessage>chunk(1000)
        .reader(serialNumberItemReader)
        .processor(convertEntityToSerialNumberRequestItemProcessor)
        .writer(new CompositeItemWriterBuilder<SerialNumberProfileValueUpsertRequestMessage>().delegates(List.of(serialNumberWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<SerialNumberProfileValueUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "serial_number_management_values_staging", "serial_number_profile",
        (serialNumberProfileValueUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, serialNumberProfileValueUpsertRequestMessage.getClient());
          ps.setString(4, serialNumberProfileValueUpsertRequestMessage.getSerialNumberProfile());
        });
  }
}
