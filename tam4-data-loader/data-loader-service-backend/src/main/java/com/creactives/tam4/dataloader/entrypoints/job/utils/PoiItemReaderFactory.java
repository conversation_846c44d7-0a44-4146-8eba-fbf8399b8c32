package com.creactives.tam4.dataloader.entrypoints.job.utils;

import com.creactives.tam4.dataloader.core.entity.ValueTrimmer;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.excel.mapping.BeanWrapperRowMapper;
import org.springframework.batch.item.excel.poi.PoiItemReader;
import org.springframework.batch.item.excel.support.rowset.DefaultRowSetFactory;

@Log4j2
public class PoiItemReaderFactory<T> {

  private String file;
  private Class<T> tClasss;
  private Integer headerRowNumber = 1;
  private boolean strict = true;
  private int distance = 0;

  public PoiItemReaderFactory<T> forFile(final String file) {
    this.file = file;
    return this;
  }

  public PoiItemReaderFactory<T> forClass(final Class<T> tClasss) {
    this.tClasss = tClasss;
    return this;
  }

  public PoiItemReaderFactory<T> withDistance(final int distance) {
    this.distance = distance;
    return this;
  }

  public PoiItemReaderFactory<T> optional() {
    this.strict = false;
    return this;
  }

  public PoiItemReaderFactory<T> useHeaderRow(final int headerRowNumber) {
    this.headerRowNumber = headerRowNumber;
    return this;
  }

  public PoiItemReader<T> build() {
    final PoiItemReader<T> reader = new PoiItemReader<>();

    final DefaultRowSetFactory rowSetFactory = new DefaultRowSetFactory();
    rowSetFactory.setColumnNameExtractor(new NthRowColumnNameExtractor(headerRowNumber));
    reader.setRowSetFactory(rowSetFactory);
    reader.setFileName(file);
    reader.setRowMapper(rowMapper());
    reader.setStrict(strict);
    return reader;
  }

  public BeanWrapperRowMapper<T> rowMapper() {
    final BeanWrapperRowMapper<T> mapper = new BeanWrapperRowMapper<>(new ValueTrimmer());
    //ignoro colonne
    mapper.setStrict(false);
    mapper.setTargetType(tClasss);
    mapper.setDistanceLimit(distance);
    return mapper;
  }

}
