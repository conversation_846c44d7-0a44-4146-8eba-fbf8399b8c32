package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendUnitOfMeasureDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendUnitOfMeasureDefinitionAddOrUpdateStep step;

  public SendUnitOfMeasureDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                               final StepBuilderFactory stepBuilderFactory,
                                               final SendUnitOfMeasureDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendUnitOfMeasureDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T006A")
        .build("send-unit-of-measure-definitions");
  }
}
