package com.creactives.tam4.dataloader.dataproviders.database.currency;

import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface CurrencyStagingRepository extends PagingAndSortingRepository<CurrencyEntity, Long> {

  CurrencyEntity findByClientAndCurrencyKey(String client, String currencyKey);

  List<CurrencyEntity> findByClientAndCurrencyKeyIn(String client, List<String> currencyKey);
}
