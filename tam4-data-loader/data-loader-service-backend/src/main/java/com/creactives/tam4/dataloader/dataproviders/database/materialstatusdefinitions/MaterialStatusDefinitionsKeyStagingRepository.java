package com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface MaterialStatusDefinitionsKeyStagingRepository extends PagingAndSortingRepository<MaterialStatusEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<MaterialStatusEntity> findByClientAndMaterialStatus(String client, String materialStatus);

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  List<MaterialStatusEntity> findByClientAndMaterialStatusIn(String client, List<String> materialStatus);

  @Override
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  <S extends MaterialStatusEntity> @NotNull S save(@NotNull S entity);

  @Modifying
  @Query("update MaterialStatusEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update MaterialStatusEntity set enabled= :enabled WHERE materialStatus=:materialStatus AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("materialStatus") String materialStatus, @Param("client") String client);

  Page<MaterialStatusEntity> findAllByClient(String client, Pageable pageable);
}
