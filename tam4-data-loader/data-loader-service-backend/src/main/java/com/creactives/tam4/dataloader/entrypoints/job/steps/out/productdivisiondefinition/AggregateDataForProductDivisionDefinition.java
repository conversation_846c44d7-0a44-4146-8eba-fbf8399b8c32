package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForProductDivisionDefinition {

  private ProductDivisionKeyEntity productDivisionKeyEntity;
  private List<ProductDivisionEntity> productDivisionEntities;

}
