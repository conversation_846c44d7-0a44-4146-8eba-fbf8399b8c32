package com.creactives.tam4.dataloader.entrypoints.job.steps.out.purchasinggroup;


import com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup.PurchasingGroupEntity;
import com.creactives.tam4.messaging.materials.commands.PurchasingGroupDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class ConvertEntityToPurchasingGroupResuestItemProcessor implements ItemProcessor<PurchasingGroupEntity, PurchasingGroupDefinitionUpsertRequestMessage> {


  public ConvertEntityToPurchasingGroupResuestItemProcessor() {

  }

  @Override
  public PurchasingGroupDefinitionUpsertRequestMessage process(final PurchasingGroupEntity purchasingGroup) throws Exception {
    return PurchasingGroupDefinitionUpsertRequestMessage.builder()
        .client(purchasingGroup.getClient())
        .industrySector(purchasingGroup.getPurchasingGroup())
        .description(purchasingGroup.getDescription())
        .build();
  }
}
