package com.creactives.tam4.dataloader.entrypoints.job.steps.in.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoriesDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoriesKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsKeyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class GlobalValuationCategoryDefinitionsEntitiesWriter implements ItemWriter<GlobalValuationCategoryDefinitionsEntity> {

  private final GlobalValuationCategoriesKeysStagingRepository globalValuationCategoriesKeysStagingRepository;
  private final GlobalValuationCategoriesDefinitionsStagingRepository globalValuationCategoriesDefinitionsStagingRepository;

  @Override
  public void write(final List<? extends GlobalValuationCategoryDefinitionsEntity> list) throws Exception {
    for (final GlobalValuationCategoryDefinitionsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    globalValuationCategoriesDefinitionsStagingRepository.saveAll(list);
    for (final GlobalValuationCategoryDefinitionsEntity globalValuationCategoryDefinitionsEntity : list) {
      if (globalValuationCategoriesKeysStagingRepository.findByClientAndGlobalValuationCategory(globalValuationCategoryDefinitionsEntity.getClient(),
          globalValuationCategoryDefinitionsEntity.getGlobalValuationCategory()).isEmpty()) {
        globalValuationCategoriesKeysStagingRepository.save(GlobalValuationCategoryDefinitionsKeyEntity.builder()
            .client(globalValuationCategoryDefinitionsEntity.getClient())
            .globalValuationCategory(globalValuationCategoryDefinitionsEntity.getGlobalValuationCategory())
            .enabled(true)
            .build());
      }
    }
  }
}
