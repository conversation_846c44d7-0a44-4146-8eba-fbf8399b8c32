package com.creactives.tam4.dataloader.entrypoints.job.steps.in.characteristics;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.characteristics.CharacteristicsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristics.CharacteristicsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn.CharacteristicsCABNEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn.CharacteristicsCABNStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@StepScope
public class CharacteristicsEntitiesWriter implements ItemWriter<CharacteristicsEntity> {

  private final CharacteristicsStagingRepository characteristicsStagingRepository;
  private final CharacteristicsCABNStagingRepository characteristicsCABNStagingRepository;

  @Override
  public void write(final List<? extends CharacteristicsEntity> list) throws Exception {
    for (final CharacteristicsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }

    final String client = list.get(0).getClient();
    final Set<String> characteristicCodes = list.stream().map(CharacteristicsEntity::getCode).collect(Collectors.toSet());
    final List<CharacteristicsCABNEntity> cabnCharacteristics = characteristicsCABNStagingRepository.findAllByClientAndCodeIn(client, characteristicCodes);

    final Map<String, String> idToDescription = cabnCharacteristics.stream().collect(Collectors.toMap(CharacteristicsCABNEntity::getCode, CharacteristicsCABNEntity::getName));

    for (final CharacteristicsEntity characteristicsEntity : list) {
      characteristicsEntity.setDescription(idToDescription.get(characteristicsEntity.getCode()));
    }

    characteristicsStagingRepository.saveAll(list);
  }
}
