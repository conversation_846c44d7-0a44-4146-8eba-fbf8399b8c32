package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MaterialApplyExtensionRow implements Serializable {
  private String client;
  private String materialCode;
  private List<MaterialPlantRow> materialPlantData = new ArrayList<>();
  private List<MaterialStorageLocationRow> materialPlantStorageLocationData = new ArrayList<>();
}
