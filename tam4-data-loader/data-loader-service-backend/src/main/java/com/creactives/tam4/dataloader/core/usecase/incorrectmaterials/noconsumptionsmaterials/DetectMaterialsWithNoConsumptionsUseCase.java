package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noconsumptionsmaterials;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialsWithNoConsumptionsUseCase {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialsWithNoConsumptionsUseCase(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoConsumptions(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-consumptions")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoConsumptions(client))
        .build();
  }
}
