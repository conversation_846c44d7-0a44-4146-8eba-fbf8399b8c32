package com.creactives.tam4.dataloader.dataproviders.database.storagelocation;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StorageLocationStagingRepository extends PagingAndSortingRepository<StorageLocationEntity, Long> {

  List<StorageLocationEntity> findAllByClientAndStorageLocationIn(String client, List<String> storageLocation);

  List<StorageLocationEntity> findAllByClientAndStorageLocationAndSynchronizationState(String client, String storageLocation, String status);

  Page<StorageLocationEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<StorageLocationEntity> findAllByClient(String client, Pageable pageable);

  Page<StorageLocationEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
