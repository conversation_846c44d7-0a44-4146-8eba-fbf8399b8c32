package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Log4j2
public class CalculateAggregateForMaterialExtensionV3 implements ItemProcessor<MaterialEntity, AggregateDataForMaterialExtension> {

  private final MaterialPlantStagingRepository materialPlantStagingRepository;
  private final MaterialStorageLocationsStagingRepository materialStorageLocationsStagingRepository;
  private final PlantStagingRepository plantStagingRepository;

  @SneakyThrows
  @Override
//  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForMaterialExtension process(final MaterialEntity materialEntity) {
    final List<MaterialPlantEntity> materialPlants = materialPlantStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    final List<MaterialPlantEntity> aggregatedMaterialPlants = aggregateSamePlants(materialPlants);
    final Map<String, PlantEntity> map = new HashMap<>();
    intoMapIgnoringDuplicates(aggregatedMaterialPlants, map);
    final List<MaterialStorageLocationEntity> allStorageLocations = materialStorageLocationsStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    return AggregateDataForMaterialExtension.builder()
        .materialEntity(materialEntity)
        .materialPlantEntities(aggregatedMaterialPlants)
        .materialStorageLocationEntities(allStorageLocations)
        .plants(map)
        .build();
  }

  private List<MaterialPlantEntity> aggregateSamePlants(final List<MaterialPlantEntity> materialPlants) {
    final List<MaterialPlantEntity> result = new ArrayList<>(materialPlants.size());
    final Set<String> plants = materialPlants.stream().map(MaterialPlantEntity::getPlantId).collect(Collectors.toSet());
    plants.forEach(plant -> {
      materialPlants.stream()
          .sorted((o1, o2) -> o2.getCreatedOn().compareTo(o1.getCreatedOn()))
          .filter(materialPlantEntity -> materialPlantEntity.getPlantId().equals(plant))
          .collect(Collectors.toList()).stream()
          .findFirst()
          .ifPresent(result::add);
    });
    return result;
  }

  private void intoMapIgnoringDuplicates(final List<MaterialPlantEntity> materialPlants, final Map<String, PlantEntity> map) {
    for (final MaterialPlantEntity materialPlantEntity : materialPlants) {
      final PlantEntity firstByClientAndPlantOrderByErpSequenceNumDesc = plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(materialPlantEntity.getClient(),
          materialPlantEntity.getPlantId());
      if (firstByClientAndPlantOrderByErpSequenceNumDesc != null) {
        map.put(firstByClientAndPlantOrderByErpSequenceNumDesc.getPlant(), firstByClientAndPlantOrderByErpSequenceNumDesc);
      }
    }
  }
}
