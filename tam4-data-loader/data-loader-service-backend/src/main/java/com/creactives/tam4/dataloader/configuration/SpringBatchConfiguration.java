package com.creactives.tam4.dataloader.configuration;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;

@EnableBatchProcessing
@EnableAsync
@EnableScheduling
@Configuration
@EnableTransactionManagement
// lombok things
@Log4j2
@RequiredArgsConstructor
public class SpringBatchConfiguration implements InitializingBean {

  public static final String TAM_JOB_LAUNCHER = "asyncJobLauncher";

  private final JobRegistry jobRegistry;
  private final List<JobFactory> factories;

  @Override
  public void afterPropertiesSet() throws Exception {
    for (final JobFactory factory : factories) {
      log.info("Registering '{}' job", factory.getJobName());
      jobRegistry.register(factory);
    }
  }


  @Bean("asyncJobLauncher")
  @SneakyThrows
  public JobLauncher jobLauncher(final JobRepository jobRepository, final TaskExecutor taskExecutor) {
    final SimpleJobLauncher _jobLauncher = new SimpleJobLauncher();
    _jobLauncher.setJobRepository(jobRepository);
    _jobLauncher.setTaskExecutor(taskExecutor);
    _jobLauncher.afterPropertiesSet();

    return _jobLauncher;
  }


}

