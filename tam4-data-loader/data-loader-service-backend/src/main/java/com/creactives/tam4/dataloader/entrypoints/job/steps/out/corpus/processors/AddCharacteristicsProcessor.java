package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.dataproviders.database.characteristics.CharacteristicsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristics.CharacteristicsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import com.google.common.collect.ArrayListMultimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created on 7/30/2019 7:02 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class AddCharacteristicsProcessor implements ItemProcessor<Material, Material> {

  private final CharacteristicsStagingRepository characteristicsStagingRepository;

  private static String getDescriptionOrCode(final CharacteristicsEntity characteristicEntity) {
    final String characteristicDescription = characteristicEntity.getDescription();
    if (StringUtils.isNotBlank(characteristicDescription)) {
      return characteristicDescription;
    }
    log.warn("Missing description for characteristic: {}. Using code as fallback.", characteristicEntity);
    return characteristicEntity.getCode();
  }

  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final List<CharacteristicsEntity> characteristics = characteristicsStagingRepository.findAllByClientAndMaterialCode(item.getClient(), item.getMaterialCode());
    final ArrayListMultimap<String, String> characteristicValues = ArrayListMultimap.create();
    characteristics.stream()
        .filter(c -> StringUtils.isNotBlank(c.getValue()))
        .forEach(characteristicEntity -> characteristicValues.put(getDescriptionOrCode(characteristicEntity), characteristicEntity.getValue()));
    return Material.from(item).characteristics(characteristicValues).build();
  }
}
