package com.creactives.tam4.dataloader.entrypoints.job;

import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.annotation.AfterWrite;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created on 6/13/2019 11:22 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused") //called by Spring
@Log4j2
@Component
public class ChunkWriterListener {

  private final EntityManager entityManager;
  private final AtomicLong startTime = new AtomicLong();
  private final AtomicLong totalItemWritten = new AtomicLong();

  public ChunkWriterListener(final EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @BeforeStep
  public void beforeStep() {
    startTime.set(System.currentTimeMillis());
    totalItemWritten.set(0L);
  }

  @AfterWrite
  public void afterWrite(final List<?> items) {
    clearEntityManager();
    printStatistics(items);
  }

  private void clearEntityManager() {
    entityManager.flush();
    entityManager.clear();
  }

  public void printStatistics(final List<?> stepExecution) {
    final long itemWritten = totalItemWritten.addAndGet(stepExecution.size());
    final long now = System.currentTimeMillis();
    final long elapsedInSeconds = (now - startTime.get()) / 1000L;
    if (itemWritten % 5000L == 0L) {
      if (elapsedInSeconds > 0L) {
        log.debug("Written {} items. {} items/s", itemWritten, itemWritten / elapsedInSeconds);
      } else {
        log.debug("Written {} items.", itemWritten);
      }
    }

  }

}
