package com.creactives.tam4.dataloader.entrypoints.job.steps.out.hazardousmaterial;


import com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial.HazardousMaterialEntity;
import com.creactives.tam4.messaging.materials.commands.HazardousMaterialUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToHazardousMaterialRequestItemProcessor implements ItemProcessor<HazardousMaterialEntity, HazardousMaterialUpsertRequestMessage> {


  public ConvertEntityToHazardousMaterialRequestItemProcessor() {

  }

  @Override
  public HazardousMaterialUpsertRequestMessage process(final HazardousMaterialEntity hazardousMaterialEntity) throws Exception {
    return HazardousMaterialUpsertRequestMessage.builder()
        .client(hazardousMaterialEntity.getClient())
        .hazardousMaterialNumber(hazardousMaterialEntity.getHazardousMaterialNumber())
        .regionCode(hazardousMaterialEntity.getRegionCode())
        .storageClass(hazardousMaterialEntity.getStorageClass())
        .build();
  }
}
