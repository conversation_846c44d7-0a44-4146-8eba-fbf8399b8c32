package com.creactives.tam4.dataloader.entrypoints.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.configuration.JobFactory;

import java.util.Objects;
import java.util.function.Supplier;

public class SupplierJobFactory implements JobFactory {
  private final String name;
  private final Supplier<Job> jobSupplier;
  private final JobMetadata jobMetadata;

  public SupplierJobFactory(final String name, final JobMetadata jobMetadata, final Supplier<Job> jobSupplier) {
    this.name = name;
    this.jobSupplier = Objects.requireNonNull(jobSupplier);
    this.jobMetadata = jobMetadata;
  }


  @Override
  public Job createJob() {
    return new JobWrapper(jobSupplier.get(), jobMetadata);
  }

  @Override
  public String getJobName() {
    return name;
  }
}
