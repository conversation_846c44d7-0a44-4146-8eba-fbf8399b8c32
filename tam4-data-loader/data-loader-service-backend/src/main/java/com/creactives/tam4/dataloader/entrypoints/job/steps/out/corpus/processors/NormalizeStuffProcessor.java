package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import com.google.common.collect.ArrayListMultimap;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;
@RequiredArgsConstructor
@Component
public class NormalizeStuffProcessor implements ItemProcessor<Material, Material> {

  public static final String CHARACTERISTIC_PREFIX = "2_";
  public static final Pattern PATTERN = Pattern.compile(" ");
  private final MaterialCodeNormalizer materialCodeNormalizer;

  private static ArrayListMultimap<String, String> convertCharacteristicKeys(final ArrayListMultimap<String, String> characteristics) {
    final ArrayListMultimap<String, String> characteristicValues = ArrayListMultimap.create();
    if (characteristics != null) {
      characteristics.entries().forEach(entry -> characteristicValues.put(convertKey(entry.getKey()), entry.getValue()));
    }
    return characteristicValues;
  }

  private static String convertKey(final String key) {
    return CHARACTERISTIC_PREFIX + PATTERN.matcher(key).replaceAll("_");
  }

  @Override
  public Material process(final Material item) throws Exception {
    return Material.from(item)
        .materialCode(materialCodeNormalizer.removeZeroes(item.getMaterialCode()))
        .client(item.getClient())
        .characteristics(convertCharacteristicKeys(item.getCharacteristics()))
        .build();
  }
}
