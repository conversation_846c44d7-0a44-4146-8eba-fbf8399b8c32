package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialWarehousesProcessor;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created on 21/03/2024 Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@Component(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PROCESSOR)
@StepScope
@Log4j2
@RequiredArgsConstructor
public class SendMaterialApplyExtensionRequestedV4Processor implements
    ItemProcessor<MaterialApplyExtensionRow, MaterialApplyExtensionLoadMessage> {

  private final MaterialCodeNormalizer materialCodeNormalizer;

  private static List<MaterialStorageLocationDetails> createStorageLocations(final Collection<MaterialStorageLocationRow> materialStorageLocationEntities) {
    if (materialStorageLocationEntities == null) {
      return Collections.emptyList();
    }
    return materialStorageLocationEntities.stream()
        .map(materialStorageLocationEntity -> MaterialStorageLocationDetails
            .builder()
            .id(materialStorageLocationEntity.getId())
            .storageLocation(materialStorageLocationEntity.getStorageLocation())
            .valuatedUnrestrictedUseStock(materialStorageLocationEntity.getValuatedUnrestrictedUseStock())
            .stockInTransfer(materialStorageLocationEntity.getStockInTransfer())
            .stockInQualityInspection(materialStorageLocationEntity.getStockInQualityInspection())
            .blockedStock(materialStorageLocationEntity.getBlockedStock())
            .storageBin(materialStorageLocationEntity.getStorageBin())
            .deletionFlag(AddMaterialWarehousesProcessor.SAP_CHECKED.equals(materialStorageLocationEntity.getFlagMaterialForDeletionAtStorageLocationLevel()))
            .build()
        )
        .collect(Collectors.toList());
  }

  @Override
  public MaterialApplyExtensionLoadMessage process(@NotNull final MaterialApplyExtensionRow maed)
      throws Exception {

    final List<MaterialPlantDetails> plantDetails = mapList(maed);

    final MaterialApplyExtensionLoadMessage e = MaterialApplyExtensionLoadMessage.builder()
        .client(maed.getClient())
        .materialCode(materialCodeNormalizer.removeZeroes(maed.getMaterialCode()))
        .materialPlantDetails(plantDetails)
        .build();
    return e;
  }

  private List<MaterialPlantDetails> mapList(final MaterialApplyExtensionRow maed) {

    return maed.getMaterialPlantData()
        .stream()
        .map(mpd -> mapToMaterialPlantDetails(mpd,
                ListUtils.defaultIfNull(maed.getMaterialPlantStorageLocationData(),
                        Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(x -> StringUtils.equals(x.getPlantKey(), mpd.getPlantKey()))
                    .collect(Collectors.toList())
            )
        ).collect(Collectors.toList());

  }

  public MaterialPlantDetails mapToMaterialPlantDetails(final MaterialPlantRow materialPlantEntity,
                                                        final List<MaterialStorageLocationRow> materialStorageLocationEntities) {
    return MaterialPlantDetails.builder()
        .id(materialPlantEntity.getId())
        .plantKey(new PlantKey(materialPlantEntity.getPlantId(), materialPlantEntity.getClient()))
        .countryCode(materialPlantEntity.getCountryKey())
        .status(materialPlantEntity.getStatus())
        .leadTimeInDays(materialPlantEntity.getLeadTimeInDays())
        .validFromDate(materialPlantEntity.getValidFromDate())
        .deletionFlag(materialPlantEntity.isDeletionFlag())
        .lotSize(materialPlantEntity.getLotSize())
        .minLotSize(materialPlantEntity.getMinLotSize())
        .seriable(materialPlantEntity.getSeriable())
        .reorderPoint(materialPlantEntity.getReorderPoint())
        .safetyStock(materialPlantEntity.getSafetyStock())
        .minimumSafetyStock(materialPlantEntity.getMinimumSafetyStock())
        .maximumStockLevel(materialPlantEntity.getMaximumStockLevel())
        .mrpType(materialPlantEntity.getMrpType())
        .mrpGroup(materialPlantEntity.getMrpGroup())
        .plantOldMaterialNumber(materialPlantEntity.getPlantOldMaterialNumber())
        .profitCenter(materialPlantEntity.getProfitCenter())
        .purchasingGroup(materialPlantEntity.getPurchasingGroup())
        .followUpMaterialCode(materialPlantEntity.getFollowUpMaterial())
        .logisticsHandlingGroup(materialPlantEntity.getLogisticsHandlingGroup())
        .mrpController(materialPlantEntity.getMrpController())
        .inHouseProductionTime(materialPlantEntity.getInHouseProductionTime())
        .individualColl(materialPlantEntity.getIndividualColl())
        .goodReceiptProcessingTimeInDays(materialPlantEntity.getGoodsReceiptProcessingTimeInDays())
        .controlKeyForQM(materialPlantEntity.getControlKeyForQualityManagement())
        .certificateType(materialPlantEntity.getCertificateType())
        .batchManagementFlag(materialPlantEntity.isBatchManagementRequirement())
        .schedulingMarginKeyForFloats(materialPlantEntity.getSchedulingMarginKeyForFloats())
        .storageLocations(createStorageLocations(materialStorageLocationEntities))
        .intrastatCode(materialPlantEntity.getIntrastatCode())
        .controlCodeConsumptionTaxesForeignTrade(materialPlantEntity.getControlCodeConsumptionTaxesForeignTrade())
        .materialCFOPCategory(materialPlantEntity.getMaterialCFOPCategory())
        .periodIndicator(materialPlantEntity.getPeriodIndicator())
        .specialProcurementType(materialPlantEntity.getSpecialProcurementType())
        .checkingGroupAvailabilityCheck(materialPlantEntity.getCheckingGroupAvailabilityCheck())
        .fixedLotSize(materialPlantEntity.getFixedLotSize())
        .maximumLotSize(materialPlantEntity.getMaximumLotSize())
        .orderingCosts(materialPlantEntity.getOrderingCosts())
        .storageCostsIndicator(materialPlantEntity.getStorageCostsIndicator())
        .roundingValueForPurchaseOrderQuantity(materialPlantEntity.getRoundingValueForPurchaseOrderQuantity())
        .unitOfIssue(materialPlantEntity.getUnitOfIssue())
        .procurementType(materialPlantEntity.getProcurementType())
        .strategyGroup(materialPlantEntity.getStrategyGroup())
        .criticalPart(materialPlantEntity.getCriticalPart())
        .effectiveOutDate(materialPlantEntity.getEffectiveOutDate())
        .countryOfOrigin(materialPlantEntity.getCountryOfOrigin())
        .loadingGroup(materialPlantEntity.getLoadingGroup())
        .planningTimeFence(materialPlantEntity.getPlanningTimeFence())
        .consumptionMode(materialPlantEntity.getConsumptionMode())
        .consumptionPeriodBackward(materialPlantEntity.getConsumptionPeriodBackward())
        .consumptionPeriodForward(materialPlantEntity.getConsumptionPeriodForward())
        .goodReceiptProcessingTimeInDays(materialPlantEntity.getGoodsReceiptProcessingTimeInDays())
        .build();
  }
}
