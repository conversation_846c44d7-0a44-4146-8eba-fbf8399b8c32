package com.creactives.tam4.dataloader.dataproviders.database.materialtypedefinition;


import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialTypeDefinitionStagingRepository extends PagingAndSortingRepository<MaterialTypeDefinitionEntity, Long> {

  List<MaterialTypeDefinitionEntity> findByClientAndMaterialType(String client, String materialType);

  List<MaterialTypeDefinitionEntity> findByClientAndLanguageAndMaterialTypeIn(String client, String language, List<String> materialTypes);

}
