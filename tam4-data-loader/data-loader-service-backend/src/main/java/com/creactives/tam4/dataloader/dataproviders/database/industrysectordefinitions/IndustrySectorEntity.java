package com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "industry_sector_keys_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T137", description = "Industries for materials")
public class IndustrySectorEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "industry_sector_keys_staging_generator")
  @SequenceGenerator(name = "industry_sector_keys_staging_generator", sequenceName = "industry_sector_keys_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "industry_sector")
  @FieldDoc(required = true, sapField = "MBRSH", description = "Industry sector")
  private String industrySector;

  @Column(name = "enabled")
  private Boolean enabled;
}
