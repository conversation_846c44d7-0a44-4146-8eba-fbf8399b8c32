package com.creactives.tam4.dataloader.entrypoints.job.steps.out.supplierdefinition;


import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierEntity;
import com.creactives.tam4.messaging.materials.commands.VendorUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class ConvertEntityToSupplierRequestItemProcessor implements ItemProcessor<SupplierEntity, VendorUpsertRequestMessage> {


  public ConvertEntityToSupplierRequestItemProcessor() {

  }

  @Override
  public VendorUpsertRequestMessage process(final SupplierEntity supplierEntity) throws Exception {
    return VendorUpsertRequestMessage.builder()
        .client(supplierEntity.getClient())
        .name(supplierEntity.getName())
        .vendorCode(supplierEntity.getCode())
        .vatRegistrationNumber(supplierEntity.getVatRegistrationNumber())
        .address(supplierEntity.getAddress())
        .name2(supplierEntity.getName2())
        .name3(supplierEntity.getName3())
        .name4(supplierEntity.getName4())
        .supplierGroupNormalized(supplierEntity.getSupplierGroupNormalized())
        .supplierNameNormalized(supplierEntity.getSupplierNameNormalized())
        .enabled(supplierEntity.getEnabled())
        .build();
  }
}
