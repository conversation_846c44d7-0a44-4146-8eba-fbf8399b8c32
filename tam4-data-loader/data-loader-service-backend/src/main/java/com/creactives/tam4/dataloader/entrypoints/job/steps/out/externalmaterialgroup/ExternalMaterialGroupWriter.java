package com.creactives.tam4.dataloader.entrypoints.job.steps.out.externalmaterialgroup;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.ExternalMaterialGroupUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Service
public class ExternalMaterialGroupWriter implements ItemWriter<ExternalMaterialGroupUpsertRequestMessage> {

  private final WriteMessageService sendMessages;


  @Override
  public void write(final List<? extends ExternalMaterialGroupUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
