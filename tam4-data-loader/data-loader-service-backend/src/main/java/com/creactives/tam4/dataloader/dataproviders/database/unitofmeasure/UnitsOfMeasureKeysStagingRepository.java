package com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface UnitsOfMeasureKeysStagingRepository extends PagingAndSortingRepository<UnitOfMeasureKeyEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<UnitOfMeasureKeyEntity> findByClientAndUnitOfMeasure(String client, String unitOfMeasure);

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  List<UnitOfMeasureKeyEntity> findByClientAndUnitOfMeasureIn(String client, List<String> unitOfMeasure);

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  <S extends UnitOfMeasureKeyEntity> @NotNull S save(@NotNull S entity);

  Page<UnitOfMeasureKeyEntity> findAllByClient(String client, Pageable pageable);
}
