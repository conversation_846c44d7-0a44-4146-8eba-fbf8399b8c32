package com.creactives.tam4.dataloader.entrypoints.job.steps.out.storagelocation;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.StorageLocationDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Log4j2
@Component
@RequiredArgsConstructor
public class SendStorageLocationAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToStorageLocationDefinitionRequestItemProcessor processor;
  private final StorageLocationDefinitionWriter writer;
  private final StorageLocationItemReader reader;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-storage-location-definition-add-or-update")
        .<StorageLocationEntity, StorageLocationDefinitionUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(reader)
        .processor(processor)
        .writer(new CompositeItemWriterBuilder<StorageLocationDefinitionUpsertRequestMessage>()
            .delegates(List.of(writer,
                updateStagingTable("storage_location_definitions_staging", List.of("plant", "storage_location"))
            )).build())
        .listener(new StatisticsListener())
        .build();
  }


  private JdbcBatchItemWriter<StorageLocationDefinitionUpsertRequestMessage> updateStagingTable(final String tableName, final List<String> matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getPlantCode());
      ps.setString(5, mcrm.getStorageLocation());
    });
  }
}
