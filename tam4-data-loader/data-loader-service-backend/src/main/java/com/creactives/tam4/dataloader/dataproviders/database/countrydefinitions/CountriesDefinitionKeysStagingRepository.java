package com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CountriesDefinitionKeysStagingRepository extends PagingAndSortingRepository<CountryDefinitionsKeyEntity, Long> {

  //  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<CountryDefinitionsKeyEntity> findByClientAndCountry(String client, String country);

//  @Transactional(propagation = Propagation.REQUIRES_NEW)
//  @Override
//  <S extends CountryDefinitionsKeyEntity> S save(S entity);

  @Modifying
  @Query("update CountryDefinitionsKeyEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update CountryDefinitionsKeyEntity set enabled= :enabled WHERE country=:country AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("country") String country, @Param("client") String client);

  Page<CountryDefinitionsKeyEntity> findAllByClient(String client, Pageable pageable);

}
