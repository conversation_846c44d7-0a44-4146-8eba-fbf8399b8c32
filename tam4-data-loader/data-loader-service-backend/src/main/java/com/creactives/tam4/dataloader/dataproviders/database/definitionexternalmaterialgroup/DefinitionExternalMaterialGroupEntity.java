package com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "description_external_material_group_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TWEWT", description = "Descriptions for External Material Groups")
public class DefinitionExternalMaterialGroupEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "description_external_material_group_staging_generator")
  @SequenceGenerator(name = "description_external_material_group_staging_generator", sequenceName = "description_external_material_group_staging_seq", allocationSize = 1000)
  private long id;

  @Column
  @FieldDoc(required = true, sapField = "MANDT", description = "Client", referenceTable = "T000")
  private String client;

  @Column
  @FieldDoc(required = true, sapField = "EXTWG", description = "External Material Group")
  private String externalMaterialGroup;

  @Column
  @FieldDoc(required = true, sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column
  @FieldDoc(required = true, sapField = "EWBEZ", description = "Description for external material group")
  private String description;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
