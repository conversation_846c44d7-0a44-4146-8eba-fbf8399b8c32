package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created on 12/4/2019 6:49 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class MaterialPrices {

  private List<BigDecimal> prices;

  public BigDecimal getMin() {
    return prices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
  }


  public BigDecimal getMax() {
    return prices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
  }

}
