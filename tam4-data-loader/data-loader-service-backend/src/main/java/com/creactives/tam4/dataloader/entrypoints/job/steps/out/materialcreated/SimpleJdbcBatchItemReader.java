package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import java.util.Iterator;
import java.util.List;

public abstract class SimpleJdbcBatchItemReader<K, T> extends SimpleBatchItemReader<T> {

  private K lastReadId;


  protected abstract K getKey(T entity);

  protected abstract List<T> queryForFirstPage();

  protected abstract List<T> queryAfterId(K id);

  @Override
  protected Iterator<T> readPage() {
    if (lastReadId == null) {
      return queryForFirstPage().iterator();
    } else {
      return queryAfterId(lastReadId).iterator();
    }
  }

  @Override
  public T read() throws Exception {
    final T entity = super.read();
    if (entity != null) {
      lastReadId = getKey(entity);
    }
    return entity;
  }
}
