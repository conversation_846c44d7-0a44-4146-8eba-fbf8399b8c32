package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialconsumption;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialconsumption.MaterialConsumptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialconsumption.MaterialConsumptionStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class MaterialConsumptionEntitiesWriter implements ItemWriter<MaterialConsumptionEntity> {

  private final MaterialConsumptionStagingRepository materialConsumptionStagingRepository;

  @Override
  public void write(final List<? extends MaterialConsumptionEntity> list) throws Exception {
    for (final MaterialConsumptionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    materialConsumptionStagingRepository.saveAll(list);

  }
}
