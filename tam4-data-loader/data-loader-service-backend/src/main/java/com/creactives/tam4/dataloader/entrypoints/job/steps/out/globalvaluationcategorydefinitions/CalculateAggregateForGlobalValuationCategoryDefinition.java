package com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoriesDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsKeyEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Log4j2
@StepScope
public class CalculateAggregateForGlobalValuationCategoryDefinition implements ItemProcessor<GlobalValuationCategoryDefinitionsKeyEntity, AggregateDataForGlobalValuationCategoryDefinition> {

  private final GlobalValuationCategoriesDefinitionsStagingRepository globalValuationCategoriesDefinitionsStagingRepository;

  private final boolean ignoreStatus;


  public CalculateAggregateForGlobalValuationCategoryDefinition(final GlobalValuationCategoriesDefinitionsStagingRepository globalValuationCategoriesDefinitionsStagingRepository,
                                                                @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.globalValuationCategoriesDefinitionsStagingRepository = globalValuationCategoriesDefinitionsStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public AggregateDataForGlobalValuationCategoryDefinition process(final GlobalValuationCategoryDefinitionsKeyEntity globalValuationCategoryDefinitionsKeyEntity) throws Exception {
    final List<GlobalValuationCategoryDefinitionsEntity> entities = ignoreStatus ?
        globalValuationCategoriesDefinitionsStagingRepository.findByClientAndGlobalValuationCategory(globalValuationCategoryDefinitionsKeyEntity.getClient(),
            globalValuationCategoryDefinitionsKeyEntity.getGlobalValuationCategory())
        : globalValuationCategoriesDefinitionsStagingRepository.findByClientAndGlobalValuationCategoryAndSynchronizationState(globalValuationCategoryDefinitionsKeyEntity.getClient(),
        globalValuationCategoryDefinitionsKeyEntity.getGlobalValuationCategory(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForGlobalValuationCategoryDefinition.builder()
        .globalValuationCategoryDefinitionsKeyEntity(globalValuationCategoryDefinitionsKeyEntity)
        .globalValuationCategoryEnities(entities)
        .build();
  }

}
