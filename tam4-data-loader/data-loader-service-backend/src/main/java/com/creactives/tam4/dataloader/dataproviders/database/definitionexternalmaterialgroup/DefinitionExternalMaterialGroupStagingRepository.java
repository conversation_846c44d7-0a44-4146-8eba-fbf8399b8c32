package com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DefinitionExternalMaterialGroupStagingRepository extends PagingAndSortingRepository<DefinitionExternalMaterialGroupEntity, Long> {

  List<DefinitionExternalMaterialGroupEntity> findByClientAndExternalMaterialGroup(String client, String externalMaterialGroup);

  List<DefinitionExternalMaterialGroupEntity> findByClientAndLanguageAndExternalMaterialGroupIn(String client, String language, List<String> externalMaterialGroups);
}
