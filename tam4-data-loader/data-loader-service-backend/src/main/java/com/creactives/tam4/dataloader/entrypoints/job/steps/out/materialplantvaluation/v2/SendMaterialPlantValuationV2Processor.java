package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.core.exceptions.MissingCurrencyException;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialTotalsProcessor;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.load.MaterialPlantValuationUpsertLoadMessage;
import com.creactives.tam4.messaging.materials.valuation.MaterialPlantValuationDetails;
import com.creactives.tam4.messaging.materials.valuation.PlantValuationContainer;
import com.google.common.collect.ArrayListMultimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_PROCESSOR)
@Log4j2
@StepScope
@RequiredArgsConstructor
public class SendMaterialPlantValuationV2Processor implements ItemProcessor<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage> {

  private final CurrencyConverter currencyConverter;
  private final MaterialCodeNormalizer materialCodeNormalizer;

  private static BigDecimal sumConsumptionQuantitiesForPlant(final Collection<ConsumptionDataRow> consumptions) {
    return consumptions.stream()
        .map(ConsumptionDataRow::getConsumptionQuantity)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private static String getDataCurrencyOrUsePlantOne(@Nullable final String currency, @NotNull final String plantCurrency) {
    return Objects.requireNonNullElse(currency, plantCurrency);
  }

  private static BigDecimal sumOrderedQuantitiesForPlant(final Collection<POHistoryRow> orderedValuesByPlant) {
    return orderedValuesByPlant.stream()
        .map(POHistoryRow::getOrderedQuantity)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private static boolean isTotalsRow(final CharSequence valuationType) {
    return StringUtils.isBlank(valuationType);
  }

  @Override
  public MaterialPlantValuationUpsertLoadMessage process(final AggregateDataForMaterialPlantValuation aggregate) {
    final Map<PlantKey, List<MaterialPlantValuationRow>> materialPlantValuationRows = aggregate
        .getMaterialPlantValuation()
        .stream()
        .collect(Collectors.groupingBy(mpv -> new PlantKey(mpv.getPlantId(), mpv.getClient())));

    final List<PlantValuationContainer> plantValuationContainers = materialPlantValuationRows.entrySet()
        .stream()
        .map(k -> getPlantValuationContainer(k.getKey(), k.getValue(), aggregate))
        .sorted()
        .collect(Collectors.toList());
    return MaterialPlantValuationUpsertLoadMessage.builder()
        .client(aggregate.getClient())
        .materialCode(materialCodeNormalizer.removeZeroes(aggregate.getMaterialCode()))
        .plantValuations(plantValuationContainers)
        .build();
  }

  private PlantValuationContainer getPlantValuationContainer(final PlantKey plantKey,
                                                             final List<MaterialPlantValuationRow> materialPlantValuationRows,
                                                             final AggregateDataForMaterialPlantValuation aggregate) {
    final PlantValuationContainer plantValuationContainer = new PlantValuationContainer();
    plantValuationContainer.setPlantKey(plantKey);
    plantValuationContainer.setCountryCode(getCountryKey(aggregate, plantKey));

    final List<ConsumptionDataRow> consumptionValues = aggregate.getConsumptionData()
        .stream()
        .filter(x -> plantKey.equalsByClientAndCode(x.getClient(), x.getPlantCode()))
        .collect(Collectors.toList());

    final List<POHistoryRow> orderedValuesByPlant = aggregate.getPoHistory()
        .stream()
        .filter(x -> plantKey.equalsByClientAndCode(x.getClient(), x.getPlantCode()))
        .collect(Collectors.toList());

    final PlantCurrencyRow plantCurrencies = aggregate.getPlantCurrencies()
        .stream()
        .filter(pc -> plantKey.equalsByClientAndCode(pc.getClient(), pc.getPlant()))
        .findFirst()
        .orElse(null);
    plantValuationContainer.setMaterialPlantValuations(convertToPlantValuationDetails(plantKey,
        materialPlantValuationRows,
        orderedValuesByPlant,
        consumptionValues,
        plantCurrencies
    ));
    return plantValuationContainer;
  }

  private String getCountryKey(final AggregateDataForMaterialPlantValuation aggregate, final PlantKey plantKey) {
    return aggregate.getPlants()
        .stream()
        .filter(p -> plantKey.equalsByClientAndCode(p.getClient(), p.getPlant()))
        .map(PlantRow::getCountryKey)
        .findFirst()
        .orElse(null);
  }

  private List<MaterialPlantValuationDetails> convertToPlantValuationDetails(final PlantKey plantKey,
                                                                             final Collection<MaterialPlantValuationRow> materialPlantValuationRows,
                                                                             final List<POHistoryRow> poHistoryRows,
                                                                             final List<ConsumptionDataRow> consumptionDataRows,
                                                                             final PlantCurrencyRow plantCurrencyRow) {
    return materialPlantValuationRows.stream()
        .map(Row -> {
              final String valuationType = Row.getValuationType();
              if (!AddMaterialTotalsProcessor.DEFAULT_VALUATION.equals(valuationType.trim())) {
                log.warn("Skipping material plant valuation Row with id: {} because valuationtype is not empty: {}", Row.getId(), valuationType);
                return null;
              }
              if (null == plantCurrencyRow) {
                throw new MissingCurrencyException(plantKey);
              }
              final String plantCurrency = plantCurrencyRow.getCurrency();
              final MaterialPlantValuationDetails.MaterialPlantValuationDetailsBuilder materialPlantValuationDetailsBuilder =
                  MaterialPlantValuationDetails
                      .builder()
                      .valuationType(valuationType)
                      .client(plantKey.getClient())
                      .code(plantKey.getCode())
                      .valuationClass(Row.getValuationClass())
                      .valuationCategory(Row.getValuationCategory())
                      .stockQuantity(Row.getInventoryAmount())
                      .stockAmount(Row.getTotalValue())
                      .movingAveragePrice(Row.getMovingAveragePrice())
                      .priceUnit(Row.getPriceUnit())
                      .priceControlIndicator(Row.getPriceControlIndicator())
                      .standardPrice(Row.getStandardPrice())
                      .originMaterial(Row.getOriginMaterial())
                      .usageMaterial(Row.getUsageMaterial())
                      .standardPriceEUR(calculateStandardPriceEUR(Row, plantCurrency))
                      .movingAveragePriceEUR(calculateMovingAveragePriceEUR(Row, plantCurrency))
                      .totalConsumptionAmountEUR(calculateTotalConsumptionAmountEUR(consumptionDataRows, plantCurrency))
                      .totalOrderedAmountEUR(calculateTotalOrderedAmountEUR(poHistoryRows, plantCurrency))
                      .totalStockAmountEUR(calculateTotalStockAmountEUR(Row, plantCurrency));


              addConsumptionAndOrderedToTotalsRow(materialPlantValuationDetailsBuilder, poHistoryRows, consumptionDataRows, valuationType, plantCurrency);

              return materialPlantValuationDetailsBuilder.build();
            }
        )
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private void addConsumptionAndOrderedToTotalsRow(final MaterialPlantValuationDetails.MaterialPlantValuationDetailsBuilder materialPlantValuationDetailsBuilder,
                                                   final List<POHistoryRow> orderedValuesByPlant,
                                                   final List<ConsumptionDataRow> consumptions,
                                                   final CharSequence valuationType,
                                                   final String plantCurrency) {
    if (isTotalsRow(valuationType)) {
      if (CollectionUtils.isNotEmpty(consumptions)) {
        final BigDecimal consumptionAmountSum = sumConsumptionAmountsForPlant(consumptions, plantCurrency);
        final BigDecimal consumptionQuantitySum = sumConsumptionQuantitiesForPlant(consumptions);
        materialPlantValuationDetailsBuilder
            .consumptionAmount(consumptionAmountSum)
            .consumptionQuantity(consumptionQuantitySum);
      }
      if (CollectionUtils.isNotEmpty(orderedValuesByPlant)) {
        final BigDecimal orderedAmountSum = sumOrderedAmountsForPlant(orderedValuesByPlant, plantCurrency);
        final BigDecimal orderedQuantitySum = sumOrderedQuantitiesForPlant(orderedValuesByPlant);
        materialPlantValuationDetailsBuilder
            .orderedAmount(orderedAmountSum)
            .orderedQuantity(orderedQuantitySum);
      }
    }
  }

  private BigDecimal sumConsumptionAmountsForPlant(final Collection<ConsumptionDataRow> consumptions, final String plantCurrency) {
    return getConsumptionValueInSpecificCurrency(consumptions, plantCurrency, plantCurrency);
  }

  private BigDecimal sumOrderedAmountsForPlant(final Collection<POHistoryRow> orderedValuesByPlant, final String plantCurrency) {
    return getOrderedAmountInSpecificCurrency(orderedValuesByPlant, plantCurrency, plantCurrency);
  }

  @NotNull
  private BigDecimal getConsumptionValueInSpecificCurrency(final Collection<ConsumptionDataRow> consumptions, final String plantCurrency, final String targetCurrency) {
    final ArrayListMultimap<String, ConsumptionDataRow> groupConsumptionsByCurrency = ArrayListMultimap.create();
    for (final ConsumptionDataRow consumption : consumptions) {
      final String currencyForConsumption = getDataCurrencyOrUsePlantOne(consumption.getCurrency(), plantCurrency);
      groupConsumptionsByCurrency.put(currencyForConsumption, consumption);
    }
    BigDecimal consumption = BigDecimal.ZERO;
    for (final String currency : groupConsumptionsByCurrency.keySet()) {
      final List<ConsumptionDataRow> consumptionDataRows = groupConsumptionsByCurrency.get(currency);
      BigDecimal consumptionForCurrency = BigDecimal.ZERO;
      for (final ConsumptionDataRow consumptionDataRow : consumptionDataRows) {
        final BigDecimal consumptionAmount = consumptionDataRow.getConsumptionAmount();
        if (null != consumptionAmount) {
          consumptionForCurrency = consumptionForCurrency.add(consumptionAmount);
        }
      }
      consumption = consumption.add(currencyConverter.convertToCurrency(currency, consumptionForCurrency, targetCurrency));
    }
    return consumption;
  }

  @NotNull
  private BigDecimal getOrderedAmountInSpecificCurrency(final Collection<POHistoryRow> histories, final String plantCurrency, final String targetCurrency) {
    final ArrayListMultimap<String, POHistoryRow> groupHistoriesByCurrency = ArrayListMultimap.create();
    for (final POHistoryRow history : histories) {
      final String currencyForHistory = getDataCurrencyOrUsePlantOne(history.getCurrency(), plantCurrency);
      groupHistoriesByCurrency.put(currencyForHistory, history);
    }
    BigDecimal orderedAmount = BigDecimal.ZERO;
    for (final String currency : groupHistoriesByCurrency.keySet()) {
      final List<POHistoryRow> historyDataRows = groupHistoriesByCurrency.get(currency);
      BigDecimal historyForCurrency = BigDecimal.ZERO;
      for (final POHistoryRow historyDataRow : historyDataRows) {
        final BigDecimal ordered = historyDataRow.getOrderedAmount();
        if (null != ordered) {
          historyForCurrency = historyForCurrency.add(ordered);
        }
      }
      orderedAmount = orderedAmount.add(currencyConverter.convertToCurrency(currency, historyForCurrency, targetCurrency));
    }
    return orderedAmount;
  }

  private BigDecimal calculateStandardPriceEUR(final MaterialPlantValuationRow entity,
                                               final String currency) {
    return calculateValueToEUR(currency, entity.getStandardPrice());
  }

  private BigDecimal calculateMovingAveragePriceEUR(final MaterialPlantValuationRow entity,
                                                    final String currency) {
    return calculateValueToEUR(currency, entity.getMovingAveragePrice());
  }

  @Nullable
  private BigDecimal calculateTotalOrderedAmountEUR(final List<POHistoryRow> historyDataRows,
                                                    final String plantCurrency
  ) {
    if (null != historyDataRows) {
      return getOrderedAmountInSpecificCurrency(historyDataRows, plantCurrency, currencyConverter.getDefaultCurrency());
    }
    return null;
  }

  @Nullable
  protected BigDecimal calculateTotalConsumptionAmountEUR(final List<ConsumptionDataRow> consumptionDataRow,
                                                          final String plantCurrency
  ) {
    if (null != consumptionDataRow) {
      return getConsumptionValueInSpecificCurrency(consumptionDataRow, plantCurrency, currencyConverter.getDefaultCurrency());
    }
    return null;
  }

  private BigDecimal calculateTotalStockAmountEUR(final MaterialPlantValuationRow entity,
                                                  final String currency) {
    return calculateValueToEUR(currency, entity.getTotalValue());
  }


  private BigDecimal calculateValueToEUR(final String currency, final BigDecimal valueToBeConvertedToBigDecimal) {
    return currencyConverter.convertToDefaultCurrency(currency, valueToBeConvertedToBigDecimal);
  }

}
