package com.creactives.tam4.dataloader.entrypoints.job.steps.in.basicmaterial;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.basicmaterial.BasicMaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.basicmaterial.BasicMaterialStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class BasicMaterialEntitiesWriter implements ItemWriter<BasicMaterialEntity> {

  private final BasicMaterialStagingRepository basicMaterialStagingRepository;

  @Override
  public void write(final List<? extends BasicMaterialEntity> list) throws Exception {
    for (final BasicMaterialEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    basicMaterialStagingRepository.saveAll(list);
  }
}
