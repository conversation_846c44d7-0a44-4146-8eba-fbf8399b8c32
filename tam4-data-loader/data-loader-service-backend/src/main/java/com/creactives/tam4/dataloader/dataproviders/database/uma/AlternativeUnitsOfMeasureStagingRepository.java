package com.creactives.tam4.dataloader.dataproviders.database.uma;

import org.springframework.data.repository.CrudRepository;

import java.util.List;

/**
 * Created on 6/11/2019 4:30 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
public interface AlternativeUnitsOfMeasureStagingRepository extends CrudRepository<AlternativeUnitsOfMeasureEntity, Long> {

  List<AlternativeUnitsOfMeasureEntity> findByClientAndMaterialCode(final String client, final String materialCode);

  List<AlternativeUnitsOfMeasureEntity> findByClientAndMaterialCodeAndAlternativeUnitOfMeasurementIn(final String client, final String materialCode, final List<String> alternativeUnitOfMeasurement);

}
