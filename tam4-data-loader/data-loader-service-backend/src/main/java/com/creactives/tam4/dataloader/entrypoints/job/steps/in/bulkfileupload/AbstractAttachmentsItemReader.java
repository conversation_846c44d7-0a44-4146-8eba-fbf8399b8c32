package com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload;

import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.file.ResourceAwareItemReaderItemStream;
import org.springframework.batch.item.support.AbstractItemCountingItemStreamItemReader;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ClassUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public abstract class AbstractAttachmentsItemReader<T> extends AbstractItemCountingItemStreamItemReader<T> implements
    ResourceAwareItemReaderItemStream<T>, InitializingBean {

  private Resource resource;
  private Iterator<File> iterator;

  public AbstractAttachmentsItemReader() {
    super();
    this.setName(ClassUtils.getShortName(this.getClass()));
  }

  @Override
  public void open(final ExecutionContext executionContext) throws ItemStreamException {
    final String inputFolder = (String) executionContext.get("inputFolder");
    if (inputFolder != null) {
      resource = new FileSystemResource(new File(inputFolder));
    }
    super.open(executionContext);
  }

  @Override
  public void setResource(final Resource resource) {

  }

  @Override
  protected T doRead() throws Exception {
    if (iterator.hasNext()) {
      return (T) iterator.next();
    }
    return null;
  }

  @Override
  protected void doOpen() throws Exception {
    List<File> directoriesLocal = Collections.emptyList();
    final Path pathToAttachments = resource.getFile().toPath();
    if (pathToAttachments.toFile().exists()) {
      directoriesLocal = Files.walk(pathToAttachments, 1)
          .filter(Files::isDirectory)
          .map(Path::toFile)
          .collect(Collectors.toList());
    }

    if (directoriesLocal.isEmpty()) {
      throw new IllegalStateException("No directories in " + pathToAttachments);
    }
    directoriesLocal.remove(0);
    iterator = directoriesLocal.iterator();
  }

  @Override
  protected void doClose() throws Exception {

  }

  @Override
  public void afterPropertiesSet() throws Exception {

  }

}
