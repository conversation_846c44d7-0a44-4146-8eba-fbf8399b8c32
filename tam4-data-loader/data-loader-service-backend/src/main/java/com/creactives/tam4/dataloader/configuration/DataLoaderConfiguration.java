package com.creactives.tam4.dataloader.configuration;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.validation.annotation.Validated;

import java.io.File;
import java.nio.file.Paths;

/**
 * Created on 1/14/2019 1:59 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Log4j2
@Configuration
@Validated
@EnableAsync
@RefreshScope
@Getter
@Setter
@ConfigurationProperties(prefix = "data-loader")
public class DataLoaderConfiguration {

  private String outputDataPath;
  private boolean writeToDatabase;
  private String validFromDateExpectedFormat = "dd/MM/yyyy";
  private String effectiveOutDateExpectedFormat = "dd/MM/yyyy";
  private String revisionNumberExpectedFormat = "yyyy-MM-dd";
  private String createdOnExpectedFormat = "dd/MM/yyyy";
  private boolean multipleDomains;
  //  private boolean doPadding;
  private String apiExportDateFormat = "YYYY-MM-dd";
  @Value("${data-loader.connect-timeout:1000}")
  private int connectTimeout = 1000;
  @Value("${data-loader.read-timeout:8000}")
  private int readTimeout = 8000;
  private boolean apiRestrictValidation;

  @Bean(name = "output-data-dir")
  public File outputDataDir() {
    return Paths.get(outputDataPath).toFile();
  }


}
