package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.common.batch.SimpleJdbcPartitioner;
import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.utils.CacheTableConfig;
import com.creactives.tam4.dataloader.entrypoints.job.utils.CachedTableStepListener;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materialdetails.MaterialKey;
import com.creactives.tam4.messaging.materials.commands.MaterialCreationRequestMessage;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 6/12/2019 5:52 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 * <p>
 * ri-factorized by Roberto Gabrieli - 2024-03-11
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class SendMaterialCreatedMessagesStep {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertAggregateToMaterialCreationRequestItemProcessor convertAggregateToMaterialCreationRequestItemProcessor;
  private final MaterialCreationRequestedProvider itemWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final TaskExecutor taskExecutor;
  private final DataSource dataSource;

  @Bean(name = MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES)
  public Step getSendMaterialCreatedMessagesStep(@Value("${creactives.tam4.batch.grid-size:8}") final int gridSize,
                                                 @Value("${data-loader.send-job.materials-chunk-size:1000}") final int chunkSize,
                                                 @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_PARTITIONER) final Partitioner partitioner,
                                                 @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_READER) final JdbcCursorItemReader<AggregateDataForMaterialCreated> itemReader,
                                                 @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_PROCESSOR) final ItemProcessor<AggregateDataForMaterialCreated, MaterialCreationRequestMessage> itemProcessor,
                                                 @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_WRITER) final ItemWriter<MaterialCreationRequestMessage> itemWriter,
                                                 @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_CACHELISTENER) final CachedTableStepListener cachedTableStepListener) {
    final AbstractTaskletStepBuilder<SimpleStepBuilder<AggregateDataForMaterialCreated, MaterialCreationRequestMessage>> taskletStepBuilder = stepBuilderFactory.get(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES)
        .<AggregateDataForMaterialCreated, MaterialCreationRequestMessage>chunk(chunkSize)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(itemWriter)
        .listener(chunkWriterListener)
        .listener(cachedTableStepListener);

    return stepBuilderFactory.get(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_PARTITIONER)
        .partitioner(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES, partitioner)
        .step(taskletStepBuilder.build())
//                             .gridSize(gridSize)
        .taskExecutor(taskExecutor)
        .build();
  }

  @Bean(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_PARTITIONER)
  @JobScope
  public Partitioner partitioner(@Value("${data-loader.send-job.materials-partition-size:10000}") final int partitionSize,
                                 @Value("#{jobExecution}") final JobExecution jobExecution,
                                 @Value("#{jobParameters['clientCode']}") final String clientCode,
                                 @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {

    final Map<String, Object> params = new HashMap<>();
    params.put("jobExecutionId", jobExecution.getJobId());
    params.put("clientCode", clientCode);
    params.put("ignoreStatus", ignoreStatus);

    final String _ignoreStatus = DataLoaderUtils.getIgnoreStatus(ignoreStatus);
    if (StringUtils.isNotBlank(_ignoreStatus)) {
      params.put("synchronizationState", _ignoreStatus);
    }

    return SimpleJdbcPartitioner.builder()
        .jdbcTemplate(new NamedParameterJdbcTemplate(dataSource))
        .qryForKeys(MaterialCreatedConstants.getPartitionerQuery(clientCode, ignoreStatus, true))
        .params(params)
        .partitionSize(partitionSize)
        .stringKey(MaterialCreatedConstants.STRING_KEY)
        .build();
  }

  @Bean(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_WRITER)
  @StepScope
  public ItemWriter<MaterialCreationRequestMessage> buildAsyncWriter() {
    return new CompositeItemWriterBuilder<MaterialCreationRequestMessage>().delegates(List.of(itemWriter,
        updateStagingTable("characteristics_staging", "material_code"),
        updateStagingTable("units_of_measure_staging", "material_code"),
        updateStagingTable("long_descriptions_staging", "material"),
        updateStagingTable("short_descriptions_staging", "material"),
        updateStagingTable("materials_data_staging", "material_code"))
    ).build();
  }

  @Bean(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_PROCESSOR)
  @StepScope
  public ItemProcessor<AggregateDataForMaterialCreated, MaterialCreationRequestMessage> buildAsyncProcessor() {
    final List<ItemProcessor<?, ?>> delegates = new ArrayList<>();
    delegates.add(convertAggregateToMaterialCreationRequestItemProcessor);

    return new CompositeItemProcessorBuilder<AggregateDataForMaterialCreated, MaterialCreationRequestMessage>()
        .delegates(delegates)
        .build();
  }


  private JdbcBatchItemWriter<MaterialCreationRequestMessage> updateStagingTable(final String tableName,
                                                                                 final String matCodeColumn) {
    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (materialCreationRequestMessage, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      final MaterialKey materialKey = materialCreationRequestMessage.getMaterialDetails().getMaterialKey();
      ps.setString(3, materialKey.getClient());
      ps.setString(4, materialKey.getMaterialCode());
    });
  }

  @Bean(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_CACHELISTENER)
  @StepScope
  public CachedTableStepListener loadCachesForStep(
      final NamedParameterJdbcTemplate jdbcTemplate,
      @Value("#{jobParameters['clientCode']}") final String clientCode
  ) {
    final String query = "select client, code, name from suppliers_definitions_staging";
    final String whereCondition;
    if (StringUtils.isNotBlank(clientCode)) {
      whereCondition = " where client = :clientCode";
    }else{
      whereCondition = StringUtils.EMPTY;
    }
    return new CachedTableStepListener(jdbcTemplate,
        List.of(CacheTableConfig
            .builder()
            .tableName("suppliers_definitions_staging")
            .query(query + whereCondition)
            .keyFields(List.of("client","code"))
            .inputParamNames(List.of("clientCode"))
            .build()));
  }
}

