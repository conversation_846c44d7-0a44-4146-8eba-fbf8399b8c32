package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcertificatecategory;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.QMCertificateCategoryUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SendQMCertificateCategoryAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToQMCertificateCategoryRequestItemProcessor itemProcessor;
  private final QMCertificateCategoryItemReader itemReader;
  private final QMCertificateCategoryWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-qm-certificate-category-add-or-update")
        .<QMCertificateCategoryEntity, QMCertificateCategoryUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(new CompositeItemWriterBuilder<QMCertificateCategoryUpsertRequestMessage>()
            .delegates(List.of(writer,
                updateStagingTable("qm_certificate_category_staging", "certificate_type"),
                updateStagingTable("certificate_type_staging", "certificate_type")
            )).build()
        )
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<QMCertificateCategoryUpsertRequestMessage> updateStagingTable(final String tableName, final String matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getCertificateType());
    });
  }

}
