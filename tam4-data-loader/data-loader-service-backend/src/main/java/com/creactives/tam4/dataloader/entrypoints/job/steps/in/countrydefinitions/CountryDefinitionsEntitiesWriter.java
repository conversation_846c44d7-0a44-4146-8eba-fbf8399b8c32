package com.creactives.tam4.dataloader.entrypoints.job.steps.in.countrydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountriesDefinitionKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountriesDefinitionStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class CountryDefinitionsEntitiesWriter implements ItemWriter<CountryDefinitionsEntity> {

  private final CountriesDefinitionStagingRepository countriesDefinitionStagingRepository;
  private final CountriesDefinitionKeysStagingRepository countriesDefinitionKeysStagingRepository;

  @Override
  public void write(final List<? extends CountryDefinitionsEntity> list) throws Exception {
    for (final CountryDefinitionsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    countriesDefinitionStagingRepository.saveAll(list);
    for (final CountryDefinitionsEntity item : list) {
      if (countriesDefinitionKeysStagingRepository.findByClientAndCountry(item.getClient(), item.getCountry()).isEmpty()) {
        countriesDefinitionKeysStagingRepository.save(CountryDefinitionsKeyEntity.builder()
            .client(item.getClient())
            .country(item.getCountry())
            .enabled(true)
            .build());
      }
    }
  }
}
