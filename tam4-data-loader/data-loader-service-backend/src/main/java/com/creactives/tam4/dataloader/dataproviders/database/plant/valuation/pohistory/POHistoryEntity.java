package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Table(name = "history_order_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class POHistoryEntity implements ClientEnrichableEntity, Serializable {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "history_order_staging_generator")
  @SequenceGenerator(name = "history_order_staging_generator", sequenceName = "history_order_staging_seq", allocationSize = 100000)
  private long id;

  @Column
  private String client;

  @Column
  private String materialCode;

  @Column
  private BigDecimal orderedQuantity;

  @Column
  private String plantCode;

  @Column
  private String price;

  @Column
  private BigDecimal orderedAmount;

  @Column
  private String currency;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
