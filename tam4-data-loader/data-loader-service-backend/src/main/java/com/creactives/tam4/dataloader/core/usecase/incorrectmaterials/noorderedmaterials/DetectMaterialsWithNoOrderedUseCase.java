package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noorderedmaterials;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialsWithNoOrderedUseCase {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialsWithNoOrderedUseCase(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoOrdered(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-ordered")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoOrdered(client))
        .build();
  }
}
