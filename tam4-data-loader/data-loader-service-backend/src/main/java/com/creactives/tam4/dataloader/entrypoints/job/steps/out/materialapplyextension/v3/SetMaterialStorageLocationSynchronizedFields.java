package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SetMaterialStorageLocationSynchronizedFields implements ItemProcessor<AggregateDataForMaterialExtension, AggregateDataForMaterialExtension> {

  private final MaterialStorageLocationsStagingRepository materialStorageLocationsStagingRepository;

  @Override
  @Transactional
  public AggregateDataForMaterialExtension process(final AggregateDataForMaterialExtension aggregate) {
    final List<Long> materialStorageLocationEntityIds = aggregate.getMaterialStorageLocationEntities()
        .parallelStream()
        .map(MaterialStorageLocationEntity::getId)
        .collect(Collectors.toList());

    final List<MaterialStorageLocationEntity> materialStorageLocationEntities =
        materialStorageLocationsStagingRepository.findAllByIdInAndSynchronizationState(materialStorageLocationEntityIds,
            SyncStatus.PENDING.getCode());

    materialStorageLocationEntities.parallelStream()
        .forEach(materialStorageLocationEntity -> {
          materialStorageLocationEntity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
          materialStorageLocationEntity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
        });

    return aggregate;
  }
}
