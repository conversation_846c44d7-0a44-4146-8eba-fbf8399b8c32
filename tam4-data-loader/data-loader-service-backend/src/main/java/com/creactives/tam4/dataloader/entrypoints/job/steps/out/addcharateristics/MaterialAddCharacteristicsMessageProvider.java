package com.creactives.tam4.dataloader.entrypoints.job.steps.out.addcharateristics;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MaterialAddCharacteristicsMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 1/8/2019 4:53 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class MaterialAddCharacteristicsMessageProvider implements ItemWriter<MaterialAddCharacteristicsMessage> {
  private final WriteMessageService sendMessages;

  @Autowired
  public MaterialAddCharacteristicsMessageProvider(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }

  @Override
  public void write(final List<? extends MaterialAddCharacteristicsMessage> list) {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
