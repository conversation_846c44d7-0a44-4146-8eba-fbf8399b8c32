package com.creactives.tam4.dataloader.dataproviders.database.material;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Table(name = "materials_data_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "MARA", description = "General Material Data")
public class MaterialEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "materials_data_staging_generator")
  @SequenceGenerator(name = "materials_data_staging_generator", sequenceName = "materials_data_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client", nullable = false)
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_code", nullable = false)
  @FieldDoc(required = true, sapField = "MATNR", description = "Material Number")
  private String materialCode;

  @Column(name = "deletion_flag")
  @FieldDoc(sapField = "LVORM", description = "Flag Material for Deletion at Client Level")
  private boolean deletionFlag;

  @Column(name = "material_type")
  @FieldDoc(required = true, sapField = "MTART", referenceTable = "T134", description = "Material Type")
  private String materialType;

  @Column(name = "industry_sector")
  @FieldDoc(sapField = "MBRSH", referenceTable = "T137", description = "Industry sector")
  private String industrySector;

  @Column(name = "material_group")
  @FieldDoc(sapField = "MATKL", referenceTable = "T023", description = "Material Group")
  private String materialGroup;

  @Column(name = "old_material_number")
  @FieldDoc(sapField = "BISMT", description = "Old material number")
  private String oldMaterialNumber;

  @Column(name = "base_unit_of_measurement")
  @FieldDoc(sapField = "MEINS", referenceTable = "T006", description = "Base Unit of Measure")
  private String baseUnitOfMeasurement;

  @Column(name = "product_division")
  @FieldDoc(sapField = "Division", referenceTable = "TSPA", description = "Division")
  private String productDivision;

  @Column(name = "authorization_group")
  @FieldDoc(sapField = "BEGRU", description = "Authorization Group")
  private String authorizationGroup;

  @Column(name = "cross_plant_material_status")
  @FieldDoc(sapField = "MSTAE", referenceTable = "T141", description = "Cross-Plant Material Status")
  private String crossPlantMaterialStatus;

  @Column(name = "material_status_valid_from_date")
  @FieldDoc(sapField = "MSTDE", description = "Date from which the cross-plant material status is valid")
  private Long materialStatusValidFromDate;

  @Column(name = "manufacturer_part_number")
  @FieldDoc(sapField = "MFRPN", description = "Manufacturer Part Number")
  private String manufacturerPartNumber;

  @Column(name = "manufacturer_code")
  @FieldDoc(sapField = "MFRNR", description = "Number of a Manufacturer")
  private String manufacturerCode;

  @Column(name = "generic_item_group")
  @FieldDoc(sapField = "MTPOS_MARA", referenceTable = "TPTM", description = "General item category group")
  private String genericItemGroup;

  @Column(name = "revision_number")
  @FieldDoc(sapField = "KZREV", description = "Revision Level Has Been Assigned to the Material")
  private String revisionNumber;

  @Column(name = "purchasing_measurement_units")
  @FieldDoc(sapField = "BSTME", referenceTable = "T006", description = "Purchase Order Unit of Measure")
  private String purchasingMeasurementUnits;

  @Column(name = "material_created_on")
  @FieldDoc(sapField = "ERSDA", description = "Created On")
  private String materialCreatedOn;

  @Column(name = "external_material_group")
  @FieldDoc(sapField = "EXTWG", referenceTable = "TWEW", description = "External Material Group")
  private String externalMaterialGroup;

  @Column(name = "weight_unit")
  @FieldDoc(sapField = "GEWEI", description = "Weight Unit")
  private String weightUnit;

  @Column(name = "net_weight")
  @FieldDoc(sapField = "NTGEW", description = "Net Weight")
  private BigDecimal netWeight;

  @Column(name = "gross_weight")
  @FieldDoc(sapField = "BRGEW", description = "Gross Weight")
  private BigDecimal grossWeight;

  @Column(name = "size_dimension")
  @FieldDoc(sapField = "GROES", description = "Size/dimensions")
  private String sizeDimension;

  @Column(name = "hazardous_material_number")
  @FieldDoc(sapField = "STOFF", referenceTable = "MGEF", description = "Hazardous material number")
  private String hazardousMaterialNumber;

  @Column(name = "basic_material")
  @FieldDoc(sapField = "WRKST", referenceTable = "TWSPR", description = "Basic Material")
  private String basicMaterial;

  @Column(name = "document_number")
  @FieldDoc(sapField = "ZEINR", description = "Document number (without document management system)")
  private String documentNumber;

  @Column(name = "batch_management_requirement_indicator")
  @FieldDoc(sapField = "XCHPF", description = "Batch management requirement indicator")
  private boolean batchManagementRequirementIndicator;

  @Column(name = "laboratory")
  @FieldDoc(sapField = "LABOR", referenceTable = "T024L", description = "Laboratory/design office")
  private String laboratory;

  @Column(name = "ignore")
  private boolean ignore;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "semantically_analyzed")
  private boolean semanticallyAnalyzed;

  @Column(name = "md_domain")
  private String mdDomain = "M";


  //FIXME: Custom fields required by A2A T4-1756
  @Column(name = "famiglia")
  private String famiglia;

  @Column(name = "sotto_famiglia")
  private String sottoFamiglia;

  @Column(name = "specifica_tecnica")
  private String specificaTecnica;

  @Column(name = "edizione")
  private String edizione;

  @Column(name = "revisione")
  private String revisione;

  @Column(name = "data_custom")
  private Long dataCustom;

  // FIXME: Custom fields required by Perenco T4-1538
  @Column(name = "product_hierarchy")
  private String productHierarchy;

  @Column(name = "volume")
  private BigDecimal volume;

  @Column(name = "volume_unit")
  private String volumeUnit;

  @Column(name = "internation_article_number")
  private String internationalArticleNumberEanUpc;

  @Column(name = "service_valuation_class")
  private String serviceValuationClass;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;

}
