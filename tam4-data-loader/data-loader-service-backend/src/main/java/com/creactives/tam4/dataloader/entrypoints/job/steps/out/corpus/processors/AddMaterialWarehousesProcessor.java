package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Component
public class AddMaterialWarehousesProcessor implements ItemProcessor<Material, Material> {

  public static final String SAP_CHECKED = "X";
  private final MaterialStorageLocationsStagingRepository materialStorageLocationsStagingRepository;
  private final MaterialPlantStagingRepository materialPlantStagingRepository;

  //  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final List<MaterialStorageLocationEntity> storageLocationEntities = materialStorageLocationsStagingRepository
        .findByClientAndMaterialCode(item.getClient(),
            item.getMaterialCode());
    final List<String> warehouses = new ArrayList<>();
    if (storageLocationEntities != null && !storageLocationEntities.isEmpty()) {
      for (final MaterialStorageLocationEntity materialStorageLocationEntity : storageLocationEntities) {
        final Optional<MaterialPlantEntity> optionalPlantEntity = materialPlantStagingRepository.findByClientAndMaterialCodeAndPlantId(materialStorageLocationEntity.getClient(), materialStorageLocationEntity.getMaterialCode(), materialStorageLocationEntity.getPlantCode());
        if (optionalPlantEntity.isPresent()) {
          final MaterialPlantEntity plantEntity = optionalPlantEntity.get();
          final boolean storageDeleted = SAP_CHECKED.equals(materialStorageLocationEntity.getFlagMaterialForDeletionAtStorageLocationLevel());
          if (!plantEntity.isDeletionFlag() && !storageDeleted) {
            warehouses.add(materialStorageLocationEntity.getStorageLocation());
          }
        }
      }
    }
    if (!warehouses.isEmpty()) {
      warehouses.sort(Comparator.naturalOrder());
      return Material.from(item).warehouses(warehouses).build();
    }
    return item;
  }
}
