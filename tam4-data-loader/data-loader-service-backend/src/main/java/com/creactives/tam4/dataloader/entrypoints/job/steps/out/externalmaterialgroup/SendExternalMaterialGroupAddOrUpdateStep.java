package com.creactives.tam4.dataloader.entrypoints.job.steps.out.externalmaterialgroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.ExternalMaterialGroupUpsertRequestMessage;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
public class SendExternalMaterialGroupAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToExternalMaterialGroupRequestItemProcessor itemProcessor;
  private final ExternalMaterialGroupItemReader itemReader;
  private final ExternalMaterialGroupWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  public SendExternalMaterialGroupAddOrUpdateStep(final StepBuilderFactory stepBuilderFactory,
                                                  final ConvertEntityToExternalMaterialGroupRequestItemProcessor itemProcessor,
                                                  final ExternalMaterialGroupItemReader itemReader,
                                                  final ExternalMaterialGroupWriter writer,
                                                  final ChunkWriterListener chunkWriterListener,
                                                  final DataSource dataSource) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.itemProcessor = itemProcessor;
    this.itemReader = itemReader;
    this.writer = writer;
    this.chunkWriterListener = chunkWriterListener;
    this.dataSource = dataSource;
  }

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-external-material-group-add-or-update")
        .<ExternalMaterialGroupEntity, ExternalMaterialGroupUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(new CompositeItemWriterBuilder<ExternalMaterialGroupUpsertRequestMessage>().delegates(List.of(writer,
            updateStagingTable("external_material_group_staging", "external_material_group"),
            updateStagingTable("description_external_material_group_staging", "external_material_group")
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<ExternalMaterialGroupUpsertRequestMessage> updateStagingTable(final String table, final String columnCondition) {
    return DataLoaderUtils.updateStagingTable(dataSource, table, columnCondition,
        (externalMaterialGroupUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, externalMaterialGroupUpsertRequestMessage.getClient());
          ps.setString(4, externalMaterialGroupUpsertRequestMessage.getExternalMaterialGroup());
        });
  }
}
