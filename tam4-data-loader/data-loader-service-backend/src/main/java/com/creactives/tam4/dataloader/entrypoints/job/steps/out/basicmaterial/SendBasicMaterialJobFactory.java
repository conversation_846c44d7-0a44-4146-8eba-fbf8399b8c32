package com.creactives.tam4.dataloader.entrypoints.job.steps.out.basicmaterial;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendBasicMaterialJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendBasicMaterialAddOrUpateStep step;

  public SendBasicMaterialJobFactory(final JobBuilderFactory jobBuilderFactory,
                                     final StepBuilderFactory stepBuilderFactory,
                                     final SendBasicMaterialAddOrUpateStep sendBasicMaterialAddOrUpateStep,
                                     final SendBasicMaterialAddOrUpateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendBasicMaterialFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: TWSPR")
        .build("send-basic-material");
  }

}
