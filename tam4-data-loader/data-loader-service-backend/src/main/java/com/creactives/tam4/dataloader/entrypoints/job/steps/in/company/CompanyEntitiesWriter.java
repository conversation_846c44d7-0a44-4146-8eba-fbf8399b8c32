package com.creactives.tam4.dataloader.entrypoints.job.steps.in.company;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class CompanyEntitiesWriter implements ItemWriter<CompanyEntity> {

  private final CompanyStagingRepository companyStagingRepository;

  @Override
  public void write(final List<? extends CompanyEntity> list) throws Exception {
    for (final CompanyEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    companyStagingRepository.saveAll(list);
  }
}
