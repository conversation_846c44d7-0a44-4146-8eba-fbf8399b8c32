package com.creactives.tam4.dataloader.configuration;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Slf4jReporter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class MetricReporterService {
  private final MetricRegistry metricRegistry;
  private Slf4jReporter currentReporter;

  public void start(final long period, final TimeUnit unit) {
    currentReporter = Slf4jReporter.forRegistry(metricRegistry).build();
    currentReporter.start(period, unit);
  }

  public void restart(final long period, final TimeUnit unit) {
    this.stop();
    this.start(period, unit);
  }

  public void stop() {
    if (currentReporter != null) {
      currentReporter.stop();
    }
  }

}
