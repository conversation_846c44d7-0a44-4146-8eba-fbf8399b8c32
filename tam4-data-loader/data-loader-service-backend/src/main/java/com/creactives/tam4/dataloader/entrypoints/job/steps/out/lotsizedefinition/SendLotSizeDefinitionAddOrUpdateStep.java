package com.creactives.tam4.dataloader.entrypoints.job.steps.out.lotsizedefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition.LotSizeDefinitionEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.LotSizeDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SendLotSizeDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToLotSizeDefinitionRequestItemProcessor itemProcessor;
  private final LotSizeDefinitionItemReader itemReader;
  private final LotSizeDefinitionWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-lot-size-add-or-update")
        .<LotSizeDefinitionEntity, LotSizeDefinitionUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(new CompositeItemWriterBuilder<LotSizeDefinitionUpsertRequestMessage>().delegates(List.of(writer,
            updateStagingTable("lot_size_definition_staging", "lot_size")
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<LotSizeDefinitionUpsertRequestMessage> updateStagingTable(final String tableName, final String matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getLotSize());
    });
  }
}
