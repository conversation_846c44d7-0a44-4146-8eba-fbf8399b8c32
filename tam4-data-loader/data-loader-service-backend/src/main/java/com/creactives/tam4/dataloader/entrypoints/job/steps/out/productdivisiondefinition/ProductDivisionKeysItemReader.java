package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyStagingRepository;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@StepScope
public class ProductDivisionKeysItemReader extends RepositoryItemReader<ProductDivisionKeyEntity> {

  public ProductDivisionKeysItemReader(final ProductDivisionKeyStagingRepository productDivisionKeyStagingRepository,
                                       @Value("#{jobParameters['clientCode']}") final String clientCode) {
    this.setRepository(productDivisionKeyStagingRepository);
    this.setPageSize(100);
    if (clientCode != null) {
      this.setMethodName("findAllByClient");
      this.setArguments(List.of(clientCode));
    } else {
      this.setMethodName("findAll");
    }

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC
    );
    this.setSort(sorts);
  }

}
