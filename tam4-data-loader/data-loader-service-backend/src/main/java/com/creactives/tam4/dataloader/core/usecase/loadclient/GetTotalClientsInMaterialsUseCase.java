package com.creactives.tam4.dataloader.core.usecase.loadclient;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
public class GetTotalClientsInMaterialsUseCase {

  private final GetTotalClientsInMaterialsInDatabase getTotalClientsInMaterialsInDatabase;

  public List<String> totalClients() {
    final List<String> result = getTotalClientsInMaterialsInDatabase.totalClients();
    return !result.isEmpty() ? result : Collections.emptyList();
  }

}
