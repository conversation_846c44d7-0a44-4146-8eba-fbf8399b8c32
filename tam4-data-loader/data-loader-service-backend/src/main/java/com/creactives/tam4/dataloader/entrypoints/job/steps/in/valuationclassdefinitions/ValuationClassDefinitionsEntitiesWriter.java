package com.creactives.tam4.dataloader.entrypoints.job.steps.in.valuationclassdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassesDefinitionsKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassesDefinitionsStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class ValuationClassDefinitionsEntitiesWriter implements ItemWriter<ValuationClassDefinitionsEntity> {

  private final ValuationClassesDefinitionsKeysStagingRepository valuationClassesDefinitionsKeysStagingRepository;
  private final ValuationClassesDefinitionsStagingRepository valuationClassesDefinitionsStagingRepository;

  @Override
  public void write(final List<? extends ValuationClassDefinitionsEntity> list) throws Exception {
    for (final ValuationClassDefinitionsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    valuationClassesDefinitionsStagingRepository.saveAll(list);
    valuationClassesDefinitionsKeysStagingRepository.saveAll(list.stream()
        .map(valuationClassDefinitionsEntity -> ValuationClassDefinitionsKeyEntity.builder()
            .client(valuationClassDefinitionsEntity.getClient())
            .valuationClass(valuationClassDefinitionsEntity.getValuationClass())
            .enabled(true)
            .build())
        .collect(Collectors.toMap(k -> k.getClient() + '/' + k.getValuationClass(),
            v -> v,
            (v1, v2) -> v1)
        ).values());
  }
}
