package com.creactives.tam4.dataloader.entrypoints.job.steps.out.basicmaterial;

import com.creactives.tam4.dataloader.dataproviders.database.basicmaterial.BasicMaterialEntity;
import com.creactives.tam4.messaging.materials.commands.BasicMaterialUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToBasicMaterialRequestItemProcessor implements ItemProcessor<BasicMaterialEntity, BasicMaterialUpsertRequestMessage> {

  @Override
  public BasicMaterialUpsertRequestMessage process(final BasicMaterialEntity basicMaterialEntity) throws Exception {
    return BasicMaterialUpsertRequestMessage.builder()
        .client(basicMaterialEntity.getClient())
        .basicMaterial(basicMaterialEntity.getBasicMaterial())
        .build();
  }
}
