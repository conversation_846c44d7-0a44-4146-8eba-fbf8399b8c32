package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.core.exceptions.MissingCurrencyException;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialTotalsProcessor;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.load.MaterialPlantValuationUpsertLoadMessage;
import com.creactives.tam4.messaging.materials.valuation.MaterialPlantValuationDetails;
import com.creactives.tam4.messaging.materials.valuation.PlantValuationContainer;
import com.google.common.collect.ArrayListMultimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * Created on 6/14/2019 10:29 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialPlantValuationItemProcessor implements ItemProcessor<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage> {

  private final CurrencyConverter currencyConverter;
  private final MaterialCodeNormalizer materialCodeNormalizer;

  private static BigDecimal sumConsumptionQuantitiesForPlant(final Collection<ConsumptionDataEntity> consumptions) {
    return consumptions.stream()
        .map(ConsumptionDataEntity::getConsumptionQuantity)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private static String getDataCurrencyOrUsePlantOne(@Nullable final String currency, @NotNull final String plantCurrency) {
    return Objects.requireNonNullElse(currency, plantCurrency);
  }

  private static BigDecimal sumOrderedQuantitiesForPlant(final Collection<POHistoryEntity> orderedValuesByPlant) {
    return orderedValuesByPlant.stream()
        .map(POHistoryEntity::getOrderedQuantity)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private static boolean isTotalsRow(final CharSequence valuationType) {
    return StringUtils.isBlank(valuationType);
  }

  @Override
  public MaterialPlantValuationUpsertLoadMessage process(final AggregateDataForMaterialPlantValuation aggregate) {
    final List<MaterialPlantValuationEntity> materialPlantValuationEntities = aggregate.getMaterialPlantValuationEntities();

    final Map<String, List<MaterialPlantValuationEntity>> groupedByPlant = materialPlantValuationEntities.stream()
        .collect(Collectors.groupingBy(MaterialPlantValuationEntity::getPlantId));
    final List<PlantValuationContainer> plantValuationContainers = groupedByPlant.entrySet()
        .stream()
        .map(valuationsPerPlant -> {
          final PlantValuationContainer plantValuationContainer = new PlantValuationContainer();
          final String plantCode = valuationsPerPlant.getKey();
          final List<MaterialPlantValuationEntity> valuations = valuationsPerPlant.getValue();
          plantValuationContainer.setPlantKey(new PlantKey(plantCode, aggregate.getMaterialEntity().getClient()));
          plantValuationContainer.setCountryCode(getCountryKey(aggregate, plantCode));
          final Map<String, List<ConsumptionDataEntity>> consumptionValues = aggregate.getConsumptionValuesByPlant();
          /*final List<Long> ids = consumptionValues.values()
              .stream()
              .flatMap(Collection::stream)
              .map(ConsumptionDataEntity::getId)
              .collect(Collectors.toList());
*/
          final Map<String, List<POHistoryEntity>> orderedValuesByPlant = aggregate.getOrderedValuesByPlant();
          final Map<String, PlantCurrencyEntity> plantCurrencies = aggregate.getPlantCurrencies();
          plantValuationContainer.setMaterialPlantValuations(convertToPlantValuationDetails(valuations, orderedValuesByPlant.get(plantCode),
              consumptionValues.get(plantCode), plantCurrencies
          ));
          return plantValuationContainer;
        })
        .collect(Collectors.toList());
/*
    final List<Long> consumptionValuesByPlantIds = aggregate.getConsumptionValuesByPlant().values().stream()
        .flatMap(Collection::stream)
        .map(ConsumptionDataEntity::getId)
        .collect(Collectors.toList());

    final List<Long> orderedValuesByPlantIds = aggregate.getOrderedValuesByPlant().values()
        .stream()
        .flatMap(Collection::stream)
        .map(POHistoryEntity::getId)
        .collect(Collectors.toList());

    final List<String> plantCurrenciesCodes = aggregate.getPlantCurrencies().values().stream().map(PlantCurrencyEntity::getPlant).collect(Collectors.toList());

    final List<Long> materialPlantValuationEntitiesIds = aggregate.getMaterialPlantValuationEntities()
        .stream()
        .map(MaterialPlantValuationEntity::getId)
        .collect(Collectors.toList());*/

    return MaterialPlantValuationUpsertLoadMessage.builder()
        .client(aggregate.getMaterialEntity().getClient())
        .materialCode(materialCodeNormalizer.removeZeroes(aggregate.getMaterialEntity().getMaterialCode()))
        .plantValuations(plantValuationContainers)
//                                                  .consumptionValuesByPlantIds(consumptionValuesByPlantIds)
//                                                  .orderedValuesByPlantIds(orderedValuesByPlantIds)
//                                                  .plantCurrenciesCodes(plantCurrenciesCodes)
//                                                  .materialPlantValuationEntitiesIds(materialPlantValuationEntitiesIds)
        .build();
  }

  private String getCountryKey(final AggregateDataForMaterialPlantValuation aggregate, final String plantCode) {
    final PlantEntity plantEntity = getPlant(aggregate, plantCode);
    if (plantEntity != null) {
      return plantEntity.getCountryKey();
    }
    return null;
  }

  @Nullable
  private PlantEntity getPlant(final AggregateDataForMaterialPlantValuation aggregate, final String plantCode) {
    if (MapUtils.isEmpty(aggregate.getPlants())) {
      return null;
    }

    if (null == aggregate.getPlants().get(plantCode)) {
      return null;
    }

    return aggregate.getPlants().get(plantCode);
  }

  private List<MaterialPlantValuationDetails> convertToPlantValuationDetails(final Collection<MaterialPlantValuationEntity> materialPlantValuationEntities,
                                                                             final List<POHistoryEntity> poHistoryEntities,
                                                                             final List<ConsumptionDataEntity> consumptionDataEntities,
                                                                             final Map<String, PlantCurrencyEntity> plantCurrencies) {
    return materialPlantValuationEntities.stream()
        .map(entity -> {
              final String valuationType = entity.getValuationType();
              if (!AddMaterialTotalsProcessor.DEFAULT_VALUATION.equals(valuationType.trim())) {
                log.warn("Skipping material plant valuation entity with id: {} because valuationType is not empty: {}", entity.getId(), valuationType);
                return null;
              }
              final String client = entity.getClient();
              final String plantCode = entity.getPlantId();
              final PlantCurrencyEntity plantCurrencyEntity = plantCurrencies.get(plantCode);
              if (null == plantCurrencyEntity) {
                throw new MissingCurrencyException(new PlantKey(plantCode, client));
              }
              final String plantCurrency = plantCurrencyEntity.getCurrency();
              final MaterialPlantValuationDetails.MaterialPlantValuationDetailsBuilder materialPlantValuationDetailsBuilder = MaterialPlantValuationDetails.builder()
                  .valuationType(valuationType)
                  .client(client)
                  .code(plantCode)
                  .valuationClass(entity.getValuationClass())
                  .valuationCategory(entity.getValuationCategory())
                  .stockQuantity(entity.getInventoryAmount())
                  .stockAmount(entity.getTotalValue())
                  .movingAveragePrice(entity.getMovingAveragePrice())
                  .priceUnit(entity.getPriceUnit())
                  .priceControlIndicator(entity.getPriceControlIndicator())
                  .standardPrice(entity.getStandardPrice())
                  .originMaterial(entity.getOriginMaterial())
                  .usageMaterial(entity.getUsageMaterial())
                  .standardPriceEUR(calculateStandardPriceEUR(entity,
                      plantCurrency))
                  .movingAveragePriceEUR(calculateMovingAveragePriceEUR(entity,
                      plantCurrency))
                  .totalConsumptionAmountEUR(calculateTotalConsumptionAmountEUR(consumptionDataEntities,
                      plantCurrency))
                  .totalOrderedAmountEUR(calculateTotalOrderedAmountEUR(poHistoryEntities,
                      plantCurrency))
                  .totalStockAmountEUR(calculateTotalStockAmountEUR(entity,
                      plantCurrency));

              addConsumptionAndOrderedToTotalsRow(materialPlantValuationDetailsBuilder, poHistoryEntities, consumptionDataEntities, valuationType, plantCurrency);

              return materialPlantValuationDetailsBuilder.build();
            }
        )
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private void addConsumptionAndOrderedToTotalsRow(final MaterialPlantValuationDetails.MaterialPlantValuationDetailsBuilder materialPlantValuationDetailsBuilder,
                                                   final List<POHistoryEntity> orderedValuesByPlant,
                                                   final List<ConsumptionDataEntity> consumptions,
                                                   final CharSequence valuationType,
                                                   final String plantCurrency) {
    if (isTotalsRow(valuationType)) {
      if (CollectionUtils.isNotEmpty(consumptions)) {
        final BigDecimal consumptionAmountSum = sumConsumptionAmountsForPlant(consumptions, plantCurrency);
        final BigDecimal consumptionQuantitySum = sumConsumptionQuantitiesForPlant(consumptions);
        materialPlantValuationDetailsBuilder
            .consumptionAmount(consumptionAmountSum)
            .consumptionQuantity(consumptionQuantitySum);
      }
      if (CollectionUtils.isNotEmpty(orderedValuesByPlant)) {
        final BigDecimal orderedAmountSum = sumOrderedAmountsForPlant(orderedValuesByPlant, plantCurrency);
        final BigDecimal orderedQuantitySum = sumOrderedQuantitiesForPlant(orderedValuesByPlant);
        materialPlantValuationDetailsBuilder
            .orderedAmount(orderedAmountSum)
            .orderedQuantity(orderedQuantitySum);
      }
    }
  }

  private BigDecimal sumConsumptionAmountsForPlant(final Collection<ConsumptionDataEntity> consumptions, final String plantCurrency) {
    return getConsumptionValueInSpecificCurrency(consumptions, plantCurrency, plantCurrency);
  }

  private BigDecimal sumOrderedAmountsForPlant(final Collection<POHistoryEntity> orderedValuesByPlant, final String plantCurrency) {
    return getOrderedAmountInSpecificCurrency(orderedValuesByPlant, plantCurrency, plantCurrency);
  }

  @NotNull
  private BigDecimal getConsumptionValueInSpecificCurrency(final Collection<ConsumptionDataEntity> consumptions, final String plantCurrency, final String targetCurrency) {
    final ArrayListMultimap<String, ConsumptionDataEntity> groupConsumptionsByCurrency = ArrayListMultimap.create();
    for (final ConsumptionDataEntity consumption : consumptions) {
      final String currencyForConsumption = getDataCurrencyOrUsePlantOne(consumption.getCurrency(), plantCurrency);
      groupConsumptionsByCurrency.put(currencyForConsumption, consumption);
    }
    BigDecimal consumption = BigDecimal.ZERO;
    for (final String currency : groupConsumptionsByCurrency.keySet()) {
      final List<ConsumptionDataEntity> consumptionDataEntities = groupConsumptionsByCurrency.get(currency);
      BigDecimal consumptionForCurrency = BigDecimal.ZERO;
      for (final ConsumptionDataEntity consumptionDataEntity : consumptionDataEntities) {
        final BigDecimal consumptionAmount = consumptionDataEntity.getConsumptionAmount();
        if (null != consumptionAmount) {
          consumptionForCurrency = consumptionForCurrency.add(consumptionAmount);
        }
      }
      consumption = consumption.add(currencyConverter.convertToCurrency(currency, consumptionForCurrency, targetCurrency));
    }
    return consumption;
  }

  @NotNull
  private BigDecimal getOrderedAmountInSpecificCurrency(final Collection<POHistoryEntity> histories, final String plantCurrency, final String targetCurrency) {
    final ArrayListMultimap<String, POHistoryEntity> groupHistoriesByCurrency = ArrayListMultimap.create();
    for (final POHistoryEntity history : histories) {
      final String currencyForHistory = getDataCurrencyOrUsePlantOne(history.getCurrency(), plantCurrency);
      groupHistoriesByCurrency.put(currencyForHistory, history);
    }
    BigDecimal orderedAmount = BigDecimal.ZERO;
    for (final String currency : groupHistoriesByCurrency.keySet()) {
      final List<POHistoryEntity> historyDataEntities = groupHistoriesByCurrency.get(currency);
      BigDecimal historyForCurrency = BigDecimal.ZERO;
      for (final POHistoryEntity historyDataEntity : historyDataEntities) {
        final BigDecimal ordered = historyDataEntity.getOrderedAmount();
        if (null != ordered) {
          historyForCurrency = historyForCurrency.add(ordered);
        }
      }
      orderedAmount = orderedAmount.add(currencyConverter.convertToCurrency(currency, historyForCurrency, targetCurrency));
    }
    return orderedAmount;
  }

  private BigDecimal calculateStandardPriceEUR(final MaterialPlantValuationEntity entity,
                                               final String currency) {
    return calculateValueToEUR(currency, entity.getStandardPrice());
  }

  private BigDecimal calculateMovingAveragePriceEUR(final MaterialPlantValuationEntity entity,
                                                    final String currency) {
    return calculateValueToEUR(currency, entity.getMovingAveragePrice());
  }

  @Nullable
  private BigDecimal calculateTotalOrderedAmountEUR(final List<POHistoryEntity> historyDataEntities,
                                                    final String plantCurrency
  ) {
    if (null != historyDataEntities) {
      return getOrderedAmountInSpecificCurrency(historyDataEntities, plantCurrency, currencyConverter.getDefaultCurrency());
    }
    return null;
  }

  @Nullable
  public BigDecimal calculateTotalConsumptionAmountEUR(final List<ConsumptionDataEntity> consumptionDataEntity,
                                                       final String plantCurrency
  ) {
    if (null != consumptionDataEntity) {
      return getConsumptionValueInSpecificCurrency(consumptionDataEntity, plantCurrency, currencyConverter.getDefaultCurrency());
    }
    return null;
  }

  private BigDecimal calculateTotalStockAmountEUR(final MaterialPlantValuationEntity entity,
                                                  final String currency) {
    return calculateValueToEUR(currency, entity.getTotalValue());
  }


  private BigDecimal calculateValueToEUR(final String currency, final BigDecimal valueToBeConvertedToBigDecimal) {
    return currencyConverter.convertToDefaultCurrency(currency, valueToBeConvertedToBigDecimal);
  }

}
