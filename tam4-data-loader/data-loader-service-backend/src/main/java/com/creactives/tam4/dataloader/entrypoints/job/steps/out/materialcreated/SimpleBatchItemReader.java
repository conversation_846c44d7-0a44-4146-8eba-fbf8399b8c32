package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import org.springframework.batch.item.ItemReader;

import java.util.Iterator;

public abstract class SimpleBatchItemReader<T> implements ItemReader<T> {
  private Iterator<T> currentPage;

  @Override
  public T read() throws Exception {
    if (isFinished()) {
      currentPage = readPage();
    }
    if (currentPage.hasNext()) {
      return currentPage.next();
    } else {
      return null;
    }
  }

  private boolean isFinished() {
    return currentPage == null || !currentPage.hasNext();
  }

  protected abstract Iterator<T> readPage();
}
