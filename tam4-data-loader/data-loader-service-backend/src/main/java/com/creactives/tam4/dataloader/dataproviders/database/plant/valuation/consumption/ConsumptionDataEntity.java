package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Table(name = "consumption_order_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "", description = "Consumption data",
    additionalDescription = "There is no strict equivalent to SAP for this table as it is the result of a report.")
public class ConsumptionDataEntity implements ClientEnrichableEntity, Serializable {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "consumption_order_staging_generator")
  @SequenceGenerator(name = "consumption_order_staging_generator", sequenceName = "consumption_order_staging_seq", allocationSize = 100000)
  private long id;

  @Column
  @FieldDoc(required = true, sapField = "", description = "Client", referenceTable = "T000")
  private String client;

  @Column
  @FieldDoc(required = true, sapField = "", description = "Material Number", referenceTable = "MARA")
  private String materialCode;

  @Column
  @FieldDoc(required = true, sapField = "", description = "Consumption quantity in the last 6/12/48 months in material base unit of measurement.")
  private BigDecimal consumptionQuantity;

  @Column
  @FieldDoc(required = true, sapField = "", referenceTable = "T001W", description = "Plant code")
  private String plantCode;

  @Column
  @FieldDoc(required = true, sapField = "", description = "Average unitary price for the materials")
  private String unitaryPriceConsumption;

  @Column
  @FieldDoc(required = true, sapField = "", description = "Consumption amount in currency in the last 6/12/48 months")
  private BigDecimal consumptionAmount;

  @Column
  @FieldDoc(required = true, sapField = "", referenceTable = "TCURC", description = "Currency")
  private String currency;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
