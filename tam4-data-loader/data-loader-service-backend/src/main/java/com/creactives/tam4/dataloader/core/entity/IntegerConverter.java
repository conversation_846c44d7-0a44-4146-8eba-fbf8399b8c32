package com.creactives.tam4.dataloader.core.entity;

import org.apache.commons.lang3.StringUtils;


public final class IntegerConverter {


  public static Integer toInteger(final String number) {
    if (number == null || StringUtils.isBlank(number)) {
      return null;
    }
    try {
      return Integer.parseInt(number);
    } catch (final Exception e) {
      throw new UnsupportedOperationException("Impossible to parse int " + number + " -> " + e.getMessage());
    }
  }

}
