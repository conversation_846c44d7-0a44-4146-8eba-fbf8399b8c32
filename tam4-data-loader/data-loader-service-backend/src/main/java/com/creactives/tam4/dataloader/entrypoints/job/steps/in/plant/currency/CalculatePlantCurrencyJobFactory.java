package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.currency;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyRepository;
import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 5:35 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
public class CalculatePlantCurrencyJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final PlantCurrencyRepository plantCurrencyRepository;
  private final CalculatePlantCurrencyStep calculatePlantCurrencyStep;

  @Bean
  public JobFactory calculatePlantCurrencyFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .onDelete(plantCurrencyRepository::deleteAll)
        .loadStep(calculatePlantCurrencyStep)
        .setTable("")
        .setDescription("Calculate plant currency")
        .build("calculate-plant-currency");
  }

}
