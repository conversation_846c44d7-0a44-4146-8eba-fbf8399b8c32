package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialgroup;

import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions.MaterialGroupDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions.MaterialGroupDefinitionsStagingRepository;
import com.creactives.tam4.messaging.materials.commands.MaterialGroupDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
@Log4j2
public class ConvertEntityToMaterialGroupRequestItemProcessor implements ItemProcessor<MaterialGroupEntity, MaterialAndMessageCouple> {


  private final MaterialGroupDefinitionsStagingRepository materialGroupDefinitionsStagingRepository;

  @Override
  public MaterialAndMessageCouple process(final MaterialGroupEntity materialGroupEntity) throws Exception {
    final List<MaterialGroupDefinitionEntity> materialGroupDescriptionEntities = materialGroupDefinitionsStagingRepository.findByClientAndMaterialGroup(materialGroupEntity.getClient(), materialGroupEntity.getMaterialGroup());
    return new MaterialAndMessageCouple(materialGroupEntity,
        MaterialGroupDefinitionUpsertRequestMessage.builder()
            .client(materialGroupEntity.getClient())
            .materialGroup(materialGroupEntity.getMaterialGroup())
            .authorizationGroup(materialGroupEntity.getAuthorizationGroup())
            .division(materialGroupEntity.getDivision())
            .descriptions(materialGroupDescriptionEntities.stream()
                .filter(materialGroupDescriptionEntity -> materialGroupDescriptionEntity.getLanguage() != null)
                .collect(Collectors.toMap(MaterialGroupDefinitionEntity::getLanguage,
                    MaterialGroupDefinitionEntity::getDescription
                )))
            .enabled(materialGroupEntity.getEnabled())
            .build()
    );
  }
}
