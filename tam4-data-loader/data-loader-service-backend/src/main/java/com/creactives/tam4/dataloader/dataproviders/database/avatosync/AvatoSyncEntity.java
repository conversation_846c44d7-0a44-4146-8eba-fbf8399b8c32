package com.creactives.tam4.dataloader.dataproviders.database.avatosync;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "avato_sync_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AvatoSyncEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "avato_sync_staging_generator")
  @SequenceGenerator(name = "avato_sync_staging_generator", sequenceName = "avato_sync_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "entity_name")
  private String entityName;

  @Column(name = "start_id")
  private Integer startId;

  @Column(name = "max_id")
  private Integer maxId;

}
