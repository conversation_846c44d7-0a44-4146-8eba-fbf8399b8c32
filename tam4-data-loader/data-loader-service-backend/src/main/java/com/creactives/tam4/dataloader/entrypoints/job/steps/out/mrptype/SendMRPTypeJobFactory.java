package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrptype;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendMRPTypeJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMRPTypeAddOrUpdateStep step;

  public SendMRPTypeJobFactory(final JobBuilderFactory jobBuilderFactory, final StepBuilderFactory stepBuilderFactory, final SendMRPTypeAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendMRPTypeFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T438A, T438T")
        .build("send-mrp-type");
  }
}
