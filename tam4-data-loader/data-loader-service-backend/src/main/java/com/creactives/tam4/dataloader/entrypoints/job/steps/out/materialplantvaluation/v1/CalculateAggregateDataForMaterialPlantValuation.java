package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryStagingRepository;
import com.creactives.tam4.messaging.PlantKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Log4j2
public class CalculateAggregateDataForMaterialPlantValuation implements ItemProcessor<MaterialEntity, AggregateDataForMaterialPlantValuation> {

  private final MaterialPlantValuationStagingRepository materialPlantValuationStagingRepository;
  private final POHistoryStagingRepository poHistoryStagingRepository;
  private final ConsumptionDataStagingRepository consumptionDataStagingRepository;
  private final PlantStagingRepository plantStagingRepository;
  private final PlantCurrencyRepository plantCurrencyRepository;

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForMaterialPlantValuation process(final @NotNull MaterialEntity materialEntity) {
    final Map<String, List<POHistoryEntity>> plantOrdered = getOrderedValueByPlant(materialEntity);
    final Map<String, List<ConsumptionDataEntity>> plantConsumptions = getPlantToConsumptionMap(materialEntity);
    final List<MaterialPlantValuationEntity> materialPlantValuations = materialPlantValuationStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());

    final List<PlantKey> plantKeys = materialPlantValuations.stream()
        .map(it -> new PlantKey(it.getPlantId(), it.getClient()))
        .distinct().collect(Collectors.toList());
    return AggregateDataForMaterialPlantValuation.builder()
        .materialEntity(materialEntity)
        .materialPlantValuationEntities(materialPlantValuations)
        .orderedValuesByPlant(plantOrdered)
        .consumptionValuesByPlant(plantConsumptions)
        .plants(findPlantDefinitions(plantKeys))
        .plantCurrencies(findPlantCurrencies(plantKeys))
        .build();
  }

  private Map<String, PlantEntity> findPlantDefinitions(final Collection<PlantKey> plantKeys) {
    return plantKeys.stream()
        .map(plantKey -> plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(plantKey.getClient(), plantKey.getCode())
        )
        .filter(Objects::nonNull)
        .collect(Collectors.toMap(PlantEntity::getPlant, Function.identity()));
  }

  private Map<String, PlantCurrencyEntity> findPlantCurrencies(final Collection<PlantKey> plantKeys) {
    return plantKeys.stream()
        .map(plantKey -> plantCurrencyRepository.findByClientAndPlant(plantKey.getClient(), plantKey.getCode())
        )
        .filter(Objects::nonNull)
        .collect(Collectors.toMap(PlantCurrencyEntity::getPlant, Function.identity()));
  }

  private Map<String, List<ConsumptionDataEntity>> getPlantToConsumptionMap(final MaterialEntity materialEntity) {
    final List<ConsumptionDataEntity> consumptions = consumptionDataStagingRepository.findAllByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    return consumptions.stream().collect(Collectors.groupingBy(ConsumptionDataEntity::getPlantCode));
  }

  private Map<String, List<POHistoryEntity>> getOrderedValueByPlant(final MaterialEntity materialEntity) {
    final List<POHistoryEntity> ordered = poHistoryStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    return ordered.stream().collect(Collectors.groupingBy(POHistoryEntity::getPlantCode));
  }
}
