package com.creactives.tam4.dataloader.dataproviders.database.materialtypes;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "material_type_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MaterialTypesEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_type_staging_generator")
  @SequenceGenerator(name = "material_type_staging_generator", sequenceName = "material_type_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "material_type")
  private String materialType;

  @Column(name = "maintenance_status")
  private String maintenanceStatus;

  @Column(name = "price_control_indicator")
  private String priceControlIndicator;

  @Column(name = "price_control_mandatory")
  private String priceControlMandatory;

  @Column(name = "category_group")
  private String categoryGroup;

  @Column(name = "authorization_group")
  private String authorizationGroup;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
