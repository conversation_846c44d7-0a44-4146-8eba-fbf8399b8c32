package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel;


import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelEntity;
import com.creactives.tam4.messaging.materials.commands.MRPMaterialLevelUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToMRPMaterialLevelRequestItemProcessor implements ItemProcessor<MRPMaterialLevelEntity, MRPMaterialLevelUpsertRequestMessage> {


  @Override
  public MRPMaterialLevelUpsertRequestMessage process(final MRPMaterialLevelEntity mrpMaterialLevelEntity) throws Exception {
    return MRPMaterialLevelUpsertRequestMessage.builder()
        .client(mrpMaterialLevelEntity.getClient())
        .mrpGroup(mrpMaterialLevelEntity.getMrpGroup())
        .plant(mrpMaterialLevelEntity.getPlant())
        .build();
  }
}
