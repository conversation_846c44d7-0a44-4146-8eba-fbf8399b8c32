package com.creactives.tam4.dataloader.dataproviders.database.qmcontrol;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "qm_control_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class QMControlEntity implements ClientEnrichableEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "qm_control_staging_generator")
  @SequenceGenerator(name = "qm_control_staging_generator", sequenceName = "qm_control_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "qm_control_key")
  private String qmControlKey;

  @Column(name = "technical_delivery_terms")
  private String technicalDeliveryTerms;

  @Column(name = "quality_assurance_agreement")
  private String qualityAssuranceAgreement;

  @Column(name = "vendor_release")
  private String vendorRelease;

  @Column(name = "certification_required")
  private String certificationRequired;

  @Column(name = "documentation")
  private String documentation;

  @Column(name = "notification_type")
  private String notificationType;

  @Column(name = "block_inactive")
  private String blockInactive;

  @Column(name = "block_invoice")
  private String blockInvoice;

  @Column(name = "rejection_gr")
  private String rejectionGR;

  @Column(name = "control_gr")
  private String controlGR;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
