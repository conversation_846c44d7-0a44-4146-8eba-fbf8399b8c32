package com.creactives.tam4.dataloader.entrypoints.job.steps.in.industrysectordefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorsDefinitionKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorsDefinitionStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class IndustrySectorsDefinitionEntitiesWriter implements ItemWriter<IndustrySectorDefinitionEntity> {

  private final IndustrySectorsDefinitionKeysStagingRepository industrySectorsDefinitionKeysStagingRepository;
  private final IndustrySectorsDefinitionStagingRepository industrySectorsDefinitionStagingRepository;

  @Override
  public void write(final List<? extends IndustrySectorDefinitionEntity> list) throws Exception {
    for (final IndustrySectorDefinitionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    industrySectorsDefinitionStagingRepository.saveAll(list);
    for (final IndustrySectorDefinitionEntity item : list) {
      if (industrySectorsDefinitionKeysStagingRepository.findByClientAndIndustrySector(item.getClient(), item.getIndustrySector()).isEmpty()) {
        industrySectorsDefinitionKeysStagingRepository.save(IndustrySectorEntity.builder()
            .client(item.getClient())
            .industrySector(item.getIndustrySector())
            .enabled(true)
            .build());
      }
    }
  }
}
