package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendItemCategoryGroupDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendItemCategoryGroupDefinitionAddOrUpdateStep step;

  public SendItemCategoryGroupDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                   final StepBuilderFactory stepBuilderFactory,
                                                   final SendItemCategoryGroupDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }


  @Bean
  public JobFactory sendItemCategoryGroupDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: TPTMT, TPTMT_enabled")
        .build("send-item-category-group-definition");
  }

}
