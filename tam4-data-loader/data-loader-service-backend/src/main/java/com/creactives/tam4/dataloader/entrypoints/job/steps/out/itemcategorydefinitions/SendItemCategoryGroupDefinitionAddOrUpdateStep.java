package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.ItemCategoryGroupDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
public class SendItemCategoryGroupDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForItemCategorylGroupDefinition calculateAggregateForItemCategorylGroupDefinition;
  private final ConvertAggregateToItemCategoryGroupDefinitionRequestItemProcessor convertAggregateToItemCategoryGroupDefinitionRequestItemProcessor;
  private final ItemCategoryGroupDefinitionWriter itemCategoryGroupDefinitionWriter;
  private final ItemCategoryGroupDefinitionsKeysItemReader itemCategoryGroupDefinitionsKeysItemReader;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-item-category-group-definition-add-or-update")
        .<ItemCategoryGroupDefinitionsKeyEntity, ItemCategoryGroupDefinitionUpsertRequestMessage>chunk(1000)
        .reader(itemCategoryGroupDefinitionsKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<ItemCategoryGroupDefinitionsKeyEntity, ItemCategoryGroupDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForItemCategorylGroupDefinition,
                convertAggregateToItemCategoryGroupDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<ItemCategoryGroupDefinitionUpsertRequestMessage>().delegates(List.of(itemCategoryGroupDefinitionWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<ItemCategoryGroupDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "item_category_group_definitions_staging", "item_category_group",
        (itemCategoryGroupDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, itemCategoryGroupDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, itemCategoryGroupDefinitionUpsertRequestMessage.getItemCategoryGroup());
        });
  }
}

