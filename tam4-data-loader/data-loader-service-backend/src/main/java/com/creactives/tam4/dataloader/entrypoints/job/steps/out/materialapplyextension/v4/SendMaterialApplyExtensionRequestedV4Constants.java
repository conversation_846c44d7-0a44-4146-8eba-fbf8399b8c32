package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import lombok.experimental.UtilityClass;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@UtilityClass
public class SendMaterialApplyExtensionRequestedV4Constants {

  public static final String JOB_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME = "SendMaterialApplyExtensionRequested::jon";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME = "SendMaterialApplyExtensionRequested::step";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PARTITIONER = STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME + "::partitioner";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_READER = STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME + "::reader";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_PROCESSOR = STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME + "::processor";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITER = STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME + "::writer";
  public static final String STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME_WRITELISTENER = STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME + "::writeListener";

  public static final String PARTITIONER_BASE_QUERY = "select distinct partitionKey from ( select " +
      " mpds.client || '/' || mpds.material_code partitionKey" +
      " from materials_plant_data_staging mpds " +
      " where 1=1  ";

  public static final String CHUNK_BASE_QUERY = "select partitionKey, client, material_code, mpds_data, msls_data "
      + "from (select distinct mpds_in.client || '/' || mpds_in.material_code partitionKey, mpds_in.client"
      + ", mpds_in.material_code, mpds_in.synchronization_state"
      + ", jsonb_agg(distinct mpds_in) mpds_data"
      + ", COALESCE("
      + "jsonb_agg(distinct msls_in) filter(where msls_in.material_code is not null),"
      + "'[]'::jsonb) as msls_data from (select mpds_in2.*, pds_in2.country_key "
      + "from materials_plant_data_staging mpds_in2 "
      + "inner join plant_definitions_staging pds_in2 "
      + "on pds_in2.client = mpds_in2.client and "
      + "pds_in2.plant = mpds_in2.plant_id) mpds_in "
      + "left join material_storage_locations_staging msls_in on "
      + "mpds_in.client = msls_in.client "
      + "and mpds_in.material_code = msls_in.material_code "
      + "and mpds_in.plant_id = msls_in.plant_code "
      + "where 1 = 1 "
      + "group by mpds_in.client,"
      + "mpds_in.material_code,"
      + "mpds_in.synchronization_state "
      + "order by mpds_in.client,"
      + "mpds_in.material_code) mpds "
      + "where 1 = 1 ";

  public static final String PARTITIONER_ORDER_BY = " order by client, material_code ) outerq order by partitionKey";
  public static final String CHUNK_GROUP_AN_ORDER_BY = " order by  mpds.client, mpds.material_code ";

  public static String getPartitionerQuery(final String clientCode, final String ignoreStatus, final boolean namedParameterQuery) {
    final String query = PARTITIONER_BASE_QUERY +
        DataLoaderUtils.buildConditionPartitioner(clientCode, ignoreStatus, "mpds", namedParameterQuery) +
        PARTITIONER_ORDER_BY;

    return query;
  }

  public static String getChunkQuery(final String clientCode, final String ignoreStatus) {
    final String query = CHUNK_BASE_QUERY +
        DataLoaderUtils.buildConditionChunk(clientCode, ignoreStatus, "mpds", false) +
        CHUNK_GROUP_AN_ORDER_BY;

    return query;
  }

}
