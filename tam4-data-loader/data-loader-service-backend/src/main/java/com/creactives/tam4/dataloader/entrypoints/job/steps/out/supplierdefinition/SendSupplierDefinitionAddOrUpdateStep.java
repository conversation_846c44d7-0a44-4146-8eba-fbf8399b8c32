package com.creactives.tam4.dataloader.entrypoints.job.steps.out.supplierdefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.VendorUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
public class SendSupplierDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToSupplierRequestItemProcessor convertEntityToSupplierRequestItemProcessor;
  private final SupplierItemReader supplierItemReader;
  private final SupplierWriter supplierWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  public TaskletStep configure() {

    return stepBuilderFactory.get("send-supplier-definition-add-or-update")
        .<SupplierEntity, VendorUpsertRequestMessage>chunk(10000)
        .listener(chunkWriterListener)
        .reader(supplierItemReader)
        .processor(convertEntityToSupplierRequestItemProcessor)
        .writer(new CompositeItemWriterBuilder<VendorUpsertRequestMessage>().delegates(List.of(supplierWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<VendorUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "suppliers_definitions_staging", "code",
        (vendorUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, vendorUpsertRequestMessage.getClient());
          ps.setString(4, vendorUpsertRequestMessage.getVendorCode());
        });
  }

  @Override
  public Step configureStep() {
    return configure();
  }
}
