package com.creactives.tam4.dataloader.dataproviders.database.plant;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * Created on 6/11/2019 4:09 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface MaterialPlantStagingRepository extends JpaRepository<MaterialPlantEntity, String> {

  //  <T> Collection<T> findAllByClient(String client, Class<T> type);
  @Query("select plantData from MaterialPlantEntity plantData")
  List<MaterialPlantEntity> findStuff();

  @Query("select distinct new com.creactives.tam4.dataloader.dataproviders.database.material.MaterialKey(plantData.client, plantData.materialCode)"
      + " from MaterialPlantEntity plantData")
  List<MaterialKey> findAllByDistinct();

//  @Query("select distinct plantData.client, plantData.materialCode"
//         + " from MaterialPlantEntity plantData")
//  List<Object> findAllByDistinct();

  List<MaterialPlantEntity> findByClientAndMaterialCode(String client, String materialCode);

  List<MaterialPlantEntity> findByClientAndMaterialCodeAndPlantIdIn(String client, String materialCode, List<String> plantIds);

  Optional<MaterialPlantEntity> findByClientAndMaterialCodeAndPlantId(String client, String materialCode, String plantId);

  List<MaterialPlantEntity> findAllByIdInAndSynchronizationState(List<Long> ids, String status);

  @Query(value = "UPDATE MaterialPlantEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE synchronizationState = :stateOld AND id in (:ids)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("stateOld") String stateOld,
                   @Param("ids") List<Long> ids);

}
