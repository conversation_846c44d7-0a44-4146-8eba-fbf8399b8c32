package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1;

import com.creactives.tam4.dataloader.core.entity.DateConverter;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.SimpleJdbcBatchItemReader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.creactives.tam4.dataloader.core.entity.BigDecimalConverter.toBigDecimal;
import static com.creactives.tam4.dataloader.core.entity.IntegerConverter.toInteger;

@Component
public class OptimizedMaterialExtensionReader extends SimpleJdbcBatchItemReader<Long, AggregateDataForMaterialExtension> {

  public static final int MP_PLANT_ID_IDX = 1;
  public static final int MP_STATUS_IDX = 2;
  public static final int MP_CLIENT_IDX = 3;
  public static final int MP_LEAD_TIME_IN_DAYS_IDX = 4;
  public static final int MP_VALID_FROM_DATE_IDX = 5;
  public static final int MP_LOT_SIZE_IDX = 6;
  public static final int MP_MIN_LOT_SIZE_IDX = 7;
  public static final int MP_SERIABLE_IDX = 8;
  public static final int MP_REORDER_POINT_IDX = 9;
  public static final int MP_SAFETY_STOCK_IDX = 10;
  public static final int MP_MINIMUM_SAFETY_STOCK_IDX = 11;
  public static final int MP_MRP_TYPE_IDX = 12;
  public static final int MP_MRP_GROUP_IDX = 13;
  public static final int MP_PURCHASING_GROUP_IDX = 14;
  public static final int MP_FOLLOW_UP_MATERIAL_IDX = 15;
  public static final int MP_LOGISTIC_HANDLING_GROUP_IDX = 16;
  public static final int MP_MRP_CONTROLLER_IDX = 17;
  public static final int MP_IN_HOUSE_PRODUCTION_TIME_IDX = 18;
  public static final int MP_INDIVIDUAL_COLL_IDX = 19;
  public static final int MP_GOODS_RECEIPT_PROCESSING_TIME_IN_DAYS_IDX = 20;
  public static final int MP_CONTROL_KEY_FOR_QUALITY_MANAGEMENT_IDX = 21;
  public static final int MP_CERTIFICATE_TYPE_IDX = 22;
  public static final int MP_BATCH_MANAGEMENT_REQUIREMENT_IDX = 23;
  public static final int MP_SCHEDULING_MARGIN_KEY_FOR_FLOATS_IDX = 24;
  public static final int MP_INTRASTAT_CODE = 25;
  public static final int MP_controlCodeConsumptionTaxesForeignTrade = 26;
  public static final int MP_materialCFOPCategory = 27;
  public static final int MP_periodIndicator = 28;
  public static final int MP_specialProcurementType = 29;
  public static final int MP_checkingGroupAvailabilityCheck = 30;

  public static final int MSL_ID_IDX = 0;
  public static final int MSL_STORAGE_BIN_IDX = 1;
  public static final int MSL_BLOCKED_STOCK_IDX = 2;
  public static final int MSL_STOCK_IN_QUALITY_INSPECTION_IDX = 3;
  public static final int MSL_STOCK_IN_TRANSFER_IDX = 4;
  public static final int MSL_STORAGE_LOCATION_IDX = 5;
  public static final int MSL_VALUATED_UNRESTRICTED_USE_STOCK_IDX = 6;
  public static final int MSL_PLANT_CODE_IDX = 7;


  private final String BASE_QUERY = "SELECT" +
      "    mds.*," +
      "    array_agg(distinct concat(mpd.id, ':', mpd.plant_id, ':', mpd.status, ':', mpd.client, ':', mpd.lead_time_in_days, ':', mpd.valid_from_date, ':', mpd.lot_size, ':'," +
      "      mpd.min_lot_size, ':', mpd.seriable, ':', mpd.reorder_point, ':', mpd.safety_stock, ':', mpd.minimum_safety_stock, ':'," +
      "      mpd.mrp_type, ':', mpd.mrp_group, ':', mpd.purchasing_group, ':', mpd.follow_up_material, ':', mpd.logistics_handling_group, ':'," +
      "      mpd.mrp_controller, ':', mpd.in_house_production_time, ':', mpd.individual_coll, ':', mpd.goods_receipt_processing_time_in_days, ':'," +
      "      mpd.control_key_for_quality_management, ':', mpd.certificate_type, ':', mpd.batch_management_requirement, ':', mpd.scheduling_margin_key_for_floats" +
      "    )) AS materialPlant," +
      "    array_agg(distinct concat(ps.plant, ':', ps.country_key)) AS plants," +
      "    array_agg(distinct concat(msl.id, ':', msl.storage_bin, ':', msl.blocked_stock::text, ':', msl.stock_in_quality_inspection::text, ':', msl.stock_in_transfer::text, ':', msl.storage_location, ':', msl.valuated_unrestricted_use_stock::text, ':', msl.plant_code)) AS storage" +
      "  FROM materials_data_staging mds" +
      "    LEFT JOIN materials_plant_data_staging mpd ON mds.client = mpd.client AND mds.material_code = mpd.material_code " +
      "    LEFT JOIN material_storage_locations_staging msl ON mds.client = msl.client AND mds.material_code = msl.material_code " +
      "    LEFT JOIN plant_definitions_staging ps ON mpd.client = ps.client AND mpd.plant_id = ps.plant ";
  private final String GROUP_AN_ORDER_BY = "  GROUP BY mds.id" +
      "  ORDER BY mds.id";

  private final String CONDITIONS = " mds.ignore is false ";

  private final JdbcTemplate jdbcTemplate;
  private final int fetchSize = 10000;

  private final DateConverter dateConverter;

  public OptimizedMaterialExtensionReader(final JdbcTemplate jdbcTemplate, final DateConverter dateConverter) {
    this.jdbcTemplate = jdbcTemplate;
    this.dateConverter = dateConverter;
  }

  private static Map<String, PlantEntity> readPlants(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("plants");
    final String[] array = (String[]) country.getArray();
    final Map<String, PlantEntity> out = new HashMap<>();
    if (array != null) {
      for (final String countryEncoded : array) {
        if (StringUtils.isNotBlank(countryEncoded.replaceAll(":", "").trim())) {
          final int i = countryEncoded.indexOf(':');
          final String key = countryEncoded.substring(0, i);
          final String value = countryEncoded.substring(i + 1);
          out.put(key, PlantEntity.builder().plant(key).countryKey(value).build());
        }
      }
    }
    return out;
  }

  private static List<MaterialStorageLocationEntity> readStorage(final ResultSet rs) throws SQLException {
    final Array storage = rs.getArray("storage");
    final String[] array = (String[]) storage.getArray();
    final List<MaterialStorageLocationEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String storageEncoded : array) {
        if (StringUtils.isNotBlank(storageEncoded.replaceAll(":", "").trim())) {
          final String[] split = Arrays.stream(storageEncoded.split(":", -1)).map(StringUtils::trimToNull).toArray(String[]::new);
          out.add(MaterialStorageLocationEntity.builder()
              .id(Long.parseLong(split[MSL_ID_IDX]))
              .storageBin(split[MSL_STORAGE_BIN_IDX])
              .blockedStock(toBigDecimal(split[MSL_BLOCKED_STOCK_IDX]))
              .stockInQualityInspection(toBigDecimal(split[MSL_STOCK_IN_QUALITY_INSPECTION_IDX]))
              .stockInTransfer(toBigDecimal(split[MSL_STOCK_IN_TRANSFER_IDX]))
              .storageLocation(split[MSL_STORAGE_LOCATION_IDX])
              .valuatedUnrestrictedUseStock(toBigDecimal(split[MSL_VALUATED_UNRESTRICTED_USE_STOCK_IDX]))
              .plantCode(split[MSL_PLANT_CODE_IDX])
              .build());

        }
      }
    }
    return out;
  }

  private static MaterialEntity readMaterial(final ResultSet rs) throws SQLException {
    return MaterialEntity.builder()
        .id(rs.getLong("id"))
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .deletionFlag(rs.getBoolean("deletion_flag"))
        .materialType(rs.getString("material_type"))
        .industrySector(rs.getString("industry_sector"))
        .materialGroup(rs.getString("material_group"))
        .oldMaterialNumber(rs.getString("old_material_number"))
        .baseUnitOfMeasurement(rs.getString("base_unit_of_measurement"))
        .productDivision(rs.getString("product_division"))
        .authorizationGroup(rs.getString("authorization_group"))
        .crossPlantMaterialStatus(rs.getString("cross_plant_material_status"))
        .materialStatusValidFromDate(rs.getLong("material_status_valid_from_date"))
        .manufacturerPartNumber(rs.getString("manufacturer_part_number"))
        .manufacturerCode(rs.getString("manufacturer_code"))
        .genericItemGroup(rs.getString("generic_item_group"))
        .revisionNumber(rs.getString("revision_number"))
        .purchasingMeasurementUnits(rs.getString("purchasing_measurement_units"))
        .materialCreatedOn(rs.getString("material_created_on"))
        .externalMaterialGroup(rs.getString("external_material_group"))
        .weightUnit(rs.getString("weight_unit"))
        .netWeight(rs.getBigDecimal("net_weight"))
        .grossWeight(rs.getBigDecimal("gross_weight"))
        .sizeDimension(rs.getString("size_dimension"))
        .hazardousMaterialNumber(rs.getString("hazardous_material_number"))
        .ignore(rs.getBoolean("ignore"))
        .createdOn(rs.getTimestamp("created_on"))
        .lastModifiedOn(rs.getTimestamp("last_modified_on"))
        .synchronizedOn(rs.getTimestamp("synchronized_on"))
        .synchronizationConfirmedOn(rs.getTimestamp("synchronization_confirmed_on"))
        .synchronizationState(rs.getString("synchronization_state"))
        .semanticallyAnalyzed(rs.getBoolean("semantically_analyzed"))
        .mdDomain(rs.getString("md_domain"))

        ////FIXME: Custom fields required by A2A T4-1756
        .famiglia(rs.getString("famiglia"))
        .sottoFamiglia(rs.getString("sotto_famiglia"))
        .specificaTecnica(rs.getString("specifica_tecnica"))
        .edizione(rs.getString("edizione"))
        .revisione(rs.getString("revisione"))
        .dataCustom(rs.getLong("data_custom"))

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(rs.getString("product_hierarchy"))

        .build();
  }

  @Override
  protected Long getKey(final AggregateDataForMaterialExtension entity) {
    return entity.getMaterialEntity().getId();
  }

  @Override
  protected List<AggregateDataForMaterialExtension> queryForFirstPage() {
    return jdbcTemplate.query(BASE_QUERY + " WHERE " + CONDITIONS + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        fetchSize
    );
  }

  private AggregateDataForMaterialExtension map(final ResultSet rs) throws SQLException {
    return AggregateDataForMaterialExtension.builder()
        .materialEntity(readMaterial(rs))
        .materialPlantEntities(readMaterialPlantEntity(rs))
        .materialStorageLocationEntities(readStorage(rs))
        .plants(readPlants(rs))
        .build();
  }

  private List<MaterialPlantEntity> readMaterialPlantEntity(final ResultSet rs) throws SQLException {

    final Array country = rs.getArray("materialPlant");
    final String[] array = (String[]) country.getArray();
    final List<MaterialPlantEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String countryEncoded : array) {
        if (StringUtils.isNotBlank(countryEncoded.replaceAll(":", "").trim())) {
          final String[] split = Arrays.stream(countryEncoded.split(":", -1)).map(StringUtils::trimToNull).toArray(String[]::new);
          out.add(MaterialPlantEntity.builder()
              .status(split[MP_STATUS_IDX])
              .plantId(split[MP_PLANT_ID_IDX])
              .purchasingGroup(split[MP_PURCHASING_GROUP_IDX])
              .leadTimeInDays(toInteger(split[MP_LEAD_TIME_IN_DAYS_IDX]))
              .minimumSafetyStock(toBigDecimal(split[MP_MINIMUM_SAFETY_STOCK_IDX]))
              .minLotSize(toBigDecimal(split[MP_MIN_LOT_SIZE_IDX]))
              .logisticsHandlingGroup(split[MP_LOGISTIC_HANDLING_GROUP_IDX])
              .followUpMaterial(split[MP_FOLLOW_UP_MATERIAL_IDX])
              .mrpGroup(split[MP_MRP_GROUP_IDX])
              .safetyStock(toBigDecimal(split[MP_SAFETY_STOCK_IDX]))
              .seriable(split[MP_SERIABLE_IDX])
              .client(split[MP_CLIENT_IDX])
              .validFromDate(dateConverter.convertValidFromDate(split[MP_VALID_FROM_DATE_IDX]))
              .lotSize(split[MP_LOT_SIZE_IDX])
              .reorderPoint(toBigDecimal(split[MP_REORDER_POINT_IDX]))
              .mrpType(split[MP_MRP_TYPE_IDX])
              .mrpController(split[MP_MRP_CONTROLLER_IDX])
              .inHouseProductionTime(toInteger(split[MP_IN_HOUSE_PRODUCTION_TIME_IDX]))
              .individualColl(split[MP_INDIVIDUAL_COLL_IDX])
              .goodsReceiptProcessingTimeInDays(toInteger(split[MP_GOODS_RECEIPT_PROCESSING_TIME_IN_DAYS_IDX]))
              .controlKeyForQualityManagement(split[MP_CONTROL_KEY_FOR_QUALITY_MANAGEMENT_IDX])
              .certificateType(split[MP_CERTIFICATE_TYPE_IDX])
              .batchManagementRequirement(Boolean.parseBoolean(split[MP_BATCH_MANAGEMENT_REQUIREMENT_IDX]))
              .schedulingMarginKeyForFloats(split[MP_SCHEDULING_MARGIN_KEY_FOR_FLOATS_IDX])
              .intrastatCode(split[MP_INTRASTAT_CODE])
              .controlCodeConsumptionTaxesForeignTrade(split[MP_controlCodeConsumptionTaxesForeignTrade])
              .materialCFOPCategory(split[MP_materialCFOPCategory])
              .periodIndicator(split[MP_periodIndicator])
              .specialProcurementType(split[MP_specialProcurementType])
              .checkingGroupAvailabilityCheck(split[MP_checkingGroupAvailabilityCheck])
              .build());

        }
      }
    }
    return out;
  }

  @Override
  protected List<AggregateDataForMaterialExtension> queryAfterId(final Long id) {
    return jdbcTemplate.query(BASE_QUERY + " where " + CONDITIONS + " and mds.id > ? " + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        id,
        fetchSize
    );
  }


}
