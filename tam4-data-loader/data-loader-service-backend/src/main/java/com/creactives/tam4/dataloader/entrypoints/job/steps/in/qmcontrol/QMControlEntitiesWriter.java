package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcontrol;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class QMControlEntitiesWriter implements ItemWriter<QMControlEntity> {

  private final QMControlStagingRepository qmControlStagingRepository;

  @Override
  public void write(final List<? extends QMControlEntity> list) throws Exception {
    for (final QMControlEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    qmControlStagingRepository.saveAll(list);
  }
}
