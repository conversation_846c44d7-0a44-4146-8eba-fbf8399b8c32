package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcontrol.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class QMControlEnabledEntitiesWriter implements ItemWriter<QMControlEntity> {

  private final QMControlStagingRepository qmControlStagingRepository;

  @Override
  public void write(final List<? extends QMControlEntity> list) throws Exception {
    for (final QMControlEntity entity : list) {
      if (entity.getEnabled()) {
        qmControlStagingRepository.setEnabled(true, entity.getQmControlKey(), entity.getClient());
      }
    }
  }
}
