package com.creactives.tam4.dataloader.core.utils;

import lombok.SneakyThrows;
import org.springframework.aop.TargetSource;
import org.springframework.aop.framework.Advised;
import org.springframework.stereotype.Service;

@Service
public final class ProxyUtils {

  @SneakyThrows
  public static <O> O proxySafe(final O object) {
    if (object instanceof Advised) {
      final Advised advised = (Advised) object;
      final TargetSource targetSource = advised.getTargetSource();
      final Object target = targetSource.getTarget();
      if (target == null || "NullBean".equals(target.getClass().getSimpleName())) {
        return null;
      }
      return (O) advised.getTargetSource().getTarget();
    }
    return object;
  }


}
