package com.creactives.tam4.dataloader.entrypoints.job.steps.in.purchasingdocumentheader;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentheader.PurchasingDocumentHeaderEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentheader.PurchasingDocumentIHeaderStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class PurchasingDocumentHeaderEntitiesWriter implements ItemWriter<PurchasingDocumentHeaderEntity> {

  private final PurchasingDocumentIHeaderStagingRepository purchasingDocumentIHeaderStagingRepository;

  @Override
  public void write(final List<? extends PurchasingDocumentHeaderEntity> list) throws Exception {
    for (final PurchasingDocumentHeaderEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    purchasingDocumentIHeaderStagingRepository.saveAll(list);
  }
}
