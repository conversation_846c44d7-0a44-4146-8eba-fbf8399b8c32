package com.creactives.tam4.dataloader.core.entity;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * Created on 9/5/2019 5:43 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
public class BigDecimalConverter {

  private static final Pattern REMOVE_COMMAS = Pattern.compile(",");

  public static BigDecimal toBigDecimal(final String number) {
    if (number == null || StringUtils.isBlank(number)) {
      return null;
    }

    final String num = handleNegativeSignAtEnd(number);
    try {
      return new BigDecimal(REMOVE_COMMAS.matcher(num).replaceAll(""));
    } catch (final NumberFormatException ex) {
      return new BigDecimal(num.replace(".", "").replace(",", "."));
    }
  }

  public static BigDecimal toBigDecimal(final Double number) {
    if (number == null) {
      return null;
    }
    return new BigDecimal(number);
  }

  @SuppressWarnings("SingleCharacterStartsWith")
  private static String handleNegativeSignAtEnd(final String number) {
    if (number.endsWith("-")) {
      final String sanitizedNumber = StringUtils.removeEnd(number, "-");
      return '-' + sanitizedNumber;
    }
    return number;
  }

}
