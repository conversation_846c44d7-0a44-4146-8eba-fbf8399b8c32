package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.messaging.materials.commands.PlantDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Log4j2
@Component
@RequiredArgsConstructor
public class ConvertAggregateToPlantDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForPlantDefinition, PlantDefinitionUpsertRequestMessage> {

  @Override
  public PlantDefinitionUpsertRequestMessage process(final AggregateDataForPlantDefinition aggregateDataForPlantDefinition) {
    final String currency = Optional.ofNullable(aggregateDataForPlantDefinition.getCurrencyEntity())
        .map(PlantCurrencyEntity::getCurrency)
        .orElse(null);

    return PlantDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForPlantDefinition.getPlantEntity().getClient())
        .plant(aggregateDataForPlantDefinition.getPlantEntity().getPlant())
        .countryCode(aggregateDataForPlantDefinition.getPlantEntity().getCountryKey())
        .description(aggregateDataForPlantDefinition.getPlantEntity().getDescription())
        .currency(currency)
        .enabled(aggregateDataForPlantDefinition.getPlantEntity().isEnabled())
        .language(aggregateDataForPlantDefinition.getPlantEntity().getLanguage())
        .additionalLanguages(aggregateDataForPlantDefinition.getAdditionalLanguages())
        .build();
  }
}
