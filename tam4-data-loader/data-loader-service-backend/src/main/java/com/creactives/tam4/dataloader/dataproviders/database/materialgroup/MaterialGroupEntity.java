package com.creactives.tam4.dataloader.dataproviders.database.materialgroup;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "material_group_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T023", description = "Material Groups")
public class MaterialGroupEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_group_staging_generator")
  @SequenceGenerator(name = "material_group_staging_generator", sequenceName = "material_group_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_group")
  @FieldDoc(required = true, sapField = "MATKL", referenceTable = "T023", description = "Material Group")
  private String materialGroup;

  @Column(name = "division")
  @FieldDoc(sapField = "SPART", referenceTable = "TSPA", description = "Division")
  private String division;

  @Column(name = "authorization_group")
  @FieldDoc(sapField = "BEGRU", referenceTable = "TSPA", description = "Authorization Group")
  private String authorizationGroup;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
