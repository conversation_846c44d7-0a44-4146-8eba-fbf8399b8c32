package com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MRPTypeDefinitionStagingRepository extends PagingAndSortingRepository<MRPTypeDefinitionEntity, Long> {

  List<MRPTypeDefinitionEntity> findByClientAndMrpType(String client, String mrpType);

  List<MRPTypeDefinitionEntity> findByClientInAndMrpTypeIn(List<String> client, List<String> mrpType);

  List<MRPTypeDefinitionEntity> findByClientAndLanguageAndMrpTypeIn(String client, String language, List<String> mrpType);
}
