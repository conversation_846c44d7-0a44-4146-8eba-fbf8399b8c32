package com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PlantStagingRepository extends PagingAndSortingRepository<PlantEntity, Long> {

  PlantEntity findFirstByClientAndPlantOrderByErpSequenceNumDesc(String client, String plant);

  PlantEntity findByClientAndPlant(String client, String plant);

  List<PlantEntity> findByClientAndPlantIn(String client, List<String> plant);

  @Modifying
  @Query("update PlantEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update PlantEntity set enabled= :enabled WHERE plant=:plant AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("plant") String plant, @Param("client") String client);

  @Modifying
  @Query("update PlantEntity set languageCode= :defaultLanguage")
  void setDefaultLanguage(@Param("defaultLanguage") String defaultLanguage);

  Optional<PlantEntity> findFirstByClientAndPlantAndSynchronizationStateOrderByErpSequenceNumDesc(String client, String plant, String status);

  Page<PlantEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<PlantEntity> findAllByClient(String client, Pageable pageable);

  Page<PlantEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
