package com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForGlobalValuationCategoryDefinition {

  private GlobalValuationCategoryDefinitionsKeyEntity globalValuationCategoryDefinitionsKeyEntity;
  private List<GlobalValuationCategoryDefinitionsEntity> globalValuationCategoryEnities;
}
