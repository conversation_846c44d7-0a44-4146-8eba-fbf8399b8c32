package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.currency;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant.PlantItemReader;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.excel.EmptyLineException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 5:25 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */

@Component
public class CalculatePlantCurrencyStep implements StepConfigurer {

  private final PlantCurrencyWriter plantCurrencyWriter;
  private final PlantItemReader plantItemReader;
  private final CalculatePlantCurrencyProcessor calculatePlantCurrencyProcessor;
  private final StepBuilderFactory stepBuilderFactory;

  @Value("${data-loader.send-job.file-loader-chunk-size:1000}")
  private int chunkSize = 1000;

  public CalculatePlantCurrencyStep(final PlantCurrencyWriter plantCurrencyWriter,
                                    final PlantItemReader plantItemReader,
                                    final CalculatePlantCurrencyProcessor calculatePlantCurrencyProcessor,
                                    final StepBuilderFactory stepBuilderFactory) {
    this.plantCurrencyWriter = plantCurrencyWriter;
    this.plantItemReader = plantItemReader;
    this.calculatePlantCurrencyProcessor = calculatePlantCurrencyProcessor;
    this.stepBuilderFactory = stepBuilderFactory;
  }

  @Override
  public Step configureStep() {
    return stepBuilderFactory.get("calculate-plant-currency")
        .<PlantEntity, PlantCurrencyEntity>chunk(chunkSize)
        .reader(plantItemReader)
        .processor(calculatePlantCurrencyProcessor)
        .writer(plantCurrencyWriter)
        .faultTolerant()
        .skipLimit(Integer.MAX_VALUE)
        .skip(EmptyLineException.class)
        .listener(new StatisticsListener())
        .build();
  }

}
