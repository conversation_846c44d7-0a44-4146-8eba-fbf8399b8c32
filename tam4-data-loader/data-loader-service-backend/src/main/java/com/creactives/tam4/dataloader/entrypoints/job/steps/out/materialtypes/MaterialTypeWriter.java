package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialtypes;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MaterialTypeDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
@Service
public class MaterialTypeWriter implements ItemWriter<MaterialTypeDefinitionUpsertRequestMessage> {

  private final WriteMessageService sendMessages;


  public MaterialTypeWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }


  @Override
  public void write(final List<? extends MaterialTypeDefinitionUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
