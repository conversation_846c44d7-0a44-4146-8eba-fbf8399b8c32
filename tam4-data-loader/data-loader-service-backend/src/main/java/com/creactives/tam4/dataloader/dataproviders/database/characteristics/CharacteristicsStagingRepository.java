package com.creactives.tam4.dataloader.dataproviders.database.characteristics;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CharacteristicsStagingRepository extends PagingAndSortingRepository<CharacteristicsEntity, Long> {

  List<CharacteristicsEntity> findAllByClientAndMaterialCode(final String client, final String materialCode);

  List<CharacteristicsEntity> findAllByClientAndMaterialCodeAndCodeIn(final String client, final String materialCode, final List<String> codes);
}
