package com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface ValuationClassesDefinitionsKeysStagingRepository extends PagingAndSortingRepository<ValuationClassDefinitionsKeyEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  <S extends ValuationClassDefinitionsKeyEntity> @NotNull S save(@NotNull S entity);

  @Modifying
  @Query("update ValuationClassDefinitionsKeyEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update ValuationClassDefinitionsKeyEntity set enabled= :enabled WHERE valuationClass=:valuationClass AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("valuationClass") String valuationClass, @Param("client") String client);

  Page<ValuationClassDefinitionsKeyEntity> findAllByClient(String client, Pageable pageable);
}
