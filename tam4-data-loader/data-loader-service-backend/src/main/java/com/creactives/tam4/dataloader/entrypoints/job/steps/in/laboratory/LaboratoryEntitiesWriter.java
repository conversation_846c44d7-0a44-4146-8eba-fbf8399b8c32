package com.creactives.tam4.dataloader.entrypoints.job.steps.in.laboratory;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.laboratory.LaboratoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.laboratory.LaboratoryStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class LaboratoryEntitiesWriter implements ItemWriter<LaboratoryEntity> {

  private final LaboratoryStagingRepository laboratoryStagingRepository;

  @Override
  public void write(final List<? extends LaboratoryEntity> list) throws Exception {
    for (final LaboratoryEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    laboratoryStagingRepository.saveAll(list);
  }
}
