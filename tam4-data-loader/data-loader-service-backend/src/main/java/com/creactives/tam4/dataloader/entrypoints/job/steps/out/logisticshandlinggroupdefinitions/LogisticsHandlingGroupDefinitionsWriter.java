package com.creactives.tam4.dataloader.entrypoints.job.steps.out.logisticshandlinggroupdefinitions;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.LogisticsHandlingGroupUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LogisticsHandlingGroupDefinitionsWriter implements ItemWriter<LogisticsHandlingGroupUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  private final LogisticsHandlingGroupDefinitionsStagingRepository logisticsHandlingGroupDefinitionsStagingRepository;

  public LogisticsHandlingGroupDefinitionsWriter(final WriteMessageService sendMessages, final LogisticsHandlingGroupDefinitionsStagingRepository logisticsHandlingGroupDefinitionsStagingRepository) {
    this.sendMessages = sendMessages;

    this.logisticsHandlingGroupDefinitionsStagingRepository = logisticsHandlingGroupDefinitionsStagingRepository;
  }

  @Override
  public void write(final List<? extends LogisticsHandlingGroupUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);

    final List<String> client = list.stream().map(l -> l.getClient()).collect(Collectors.toList());
    final List<String> logisticsHandlingGroup = list.stream().map(LogisticsHandlingGroupUpsertRequestMessage::getLogisticsHandlingGroup).collect(Collectors.toList());

    final List<LogisticsHandlingGroupEntity> logisticsHandlingGroupEntities = logisticsHandlingGroupDefinitionsStagingRepository.findByClientInAndLogisticsHandlingGroupIn(client, logisticsHandlingGroup);
    for (final LogisticsHandlingGroupEntity entity :
        logisticsHandlingGroupEntities) {
      entity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
      entity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
    }

    logisticsHandlingGroupDefinitionsStagingRepository.saveAll(logisticsHandlingGroupEntities);
  }
}
