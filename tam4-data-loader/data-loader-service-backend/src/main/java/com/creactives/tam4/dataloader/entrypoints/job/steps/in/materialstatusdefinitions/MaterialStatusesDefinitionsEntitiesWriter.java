package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialstatusdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsKeyStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Log4j2
@StepScope
public class MaterialStatusesDefinitionsEntitiesWriter implements ItemWriter<MaterialStatusDefinitionsEntity> {

  private final MaterialStatusDefinitionsStagingRepository materialStatusDefinitionsStagingRepository;
  private final MaterialStatusDefinitionsKeyStagingRepository materialStatusDefinitionsKeyStagingRepository;
  private final List<String> obsolete;

  public MaterialStatusesDefinitionsEntitiesWriter(final MaterialStatusDefinitionsStagingRepository materialStatusDefinitionsStagingRepository,
                                                   final MaterialStatusDefinitionsKeyStagingRepository materialStatusDefinitionsKeyStagingRepository,
                                                   @Value("${data-loader.obsolete}") final List<String> obsolete) {
    this.materialStatusDefinitionsStagingRepository = materialStatusDefinitionsStagingRepository;
    this.materialStatusDefinitionsKeyStagingRepository = materialStatusDefinitionsKeyStagingRepository;
    this.obsolete = obsolete;
  }

  @Override
  public void write(final List<? extends MaterialStatusDefinitionsEntity> itemsList) throws Exception {
    for (final MaterialStatusDefinitionsEntity entity : itemsList) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    materialStatusDefinitionsStagingRepository.saveAll(itemsList);

    for (final MaterialStatusDefinitionsEntity item : itemsList) {
      if (materialStatusDefinitionsKeyStagingRepository.findByClientAndMaterialStatus(item.getClient(), item.getMaterialStatus()).isEmpty()) {
        materialStatusDefinitionsKeyStagingRepository.save(MaterialStatusEntity.builder()
            .client(item.getClient())
            .materialStatus(item.getMaterialStatus())
            .enabled(true)
            .obsolete(obsolete.contains(item.getMaterialStatus()))
            .build());
      }
    }
  }
}
