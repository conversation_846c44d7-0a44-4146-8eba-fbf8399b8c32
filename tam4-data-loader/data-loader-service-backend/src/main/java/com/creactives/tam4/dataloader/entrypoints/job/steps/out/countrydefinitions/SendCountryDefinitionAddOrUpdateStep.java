package com.creactives.tam4.dataloader.entrypoints.job.steps.out.countrydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.CountryDefinitionUpsertRequestMessage;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
public class SendCountryDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForCountryDefinition calculateAggregateForCountryDefinition;
  private final ConvertAggregateToCountryDefinitionRequestItemProcessor convertAggregateToCountryDefinitionRequestItemProcessor;

  private final CountryDefinitionWriter countryDefinitionWriter;
  private final CountryKeysDefinitionsItemReader countryKeysDefinitionsItemReader;
  private final DataSource dataSource;

  public SendCountryDefinitionAddOrUpdateStep(final StepBuilderFactory stepBuilderFactory,
                                              final CalculateAggregateForCountryDefinition calculateAggregateForCountryDefinition,
                                              final ConvertAggregateToCountryDefinitionRequestItemProcessor convertAggregateToCountryDefinitionRequestItemProcessor,
                                              final CountryKeysDefinitionsItemReader countryKeysDefinitionsItemReader,
                                              final CountryDefinitionWriter countryDefinitionWriter,
                                              final DataSource dataSource) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.calculateAggregateForCountryDefinition = calculateAggregateForCountryDefinition;
    this.convertAggregateToCountryDefinitionRequestItemProcessor = convertAggregateToCountryDefinitionRequestItemProcessor;
    this.countryKeysDefinitionsItemReader = countryKeysDefinitionsItemReader;
    this.countryDefinitionWriter = countryDefinitionWriter;
    this.dataSource = dataSource;
  }

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-countrydefinitions-definition-add-or-update")
        .<CountryDefinitionsKeyEntity, CountryDefinitionUpsertRequestMessage>chunk(1000)
        .reader(countryKeysDefinitionsItemReader)
        .processor(new CompositeItemProcessorBuilder<CountryDefinitionsKeyEntity, CountryDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForCountryDefinition,
                convertAggregateToCountryDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<CountryDefinitionUpsertRequestMessage>().delegates(List.of(countryDefinitionWriter,
            updateStagingTable("country_definitions_staging", "country")
//                                     updateStagingTable("country_keys_staging", "country")
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<CountryDefinitionUpsertRequestMessage> updateStagingTable(final String tableName, final String matCodeColumn) {
    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (msg, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, msg.getClient());
      ps.setString(4, msg.getCountry());
    });
  }

}
