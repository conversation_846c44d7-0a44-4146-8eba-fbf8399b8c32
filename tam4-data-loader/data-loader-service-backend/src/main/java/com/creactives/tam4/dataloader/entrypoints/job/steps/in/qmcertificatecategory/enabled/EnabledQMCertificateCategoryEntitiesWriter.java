package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcertificatecategory.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class EnabledQMCertificateCategoryEntitiesWriter implements ItemWriter<QMCertificateCategoryEntity> {

  private final QMCertificateCategoryStagingRepository qmCertificateCategoryStagingRepository;


  @Override
  public void write(final List<? extends QMCertificateCategoryEntity> list) throws Exception {
    for (final QMCertificateCategoryEntity entity : list) {
      if (entity.getEnabled()) {
        qmCertificateCategoryStagingRepository.setEnabled(true, entity.getCertificateType(), entity.getClient());
      }
    }
  }
}
