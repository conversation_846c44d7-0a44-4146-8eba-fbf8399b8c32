package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import com.creactives.tam4.messaging.materials.commands.MaterialStatusDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Log4j2
public class ConvertAggregateToMaterialStatusDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForMaterialStatusDefinition, MaterialStatusDefinitionUpsertRequestMessage> {

  @Override
  public MaterialStatusDefinitionUpsertRequestMessage process(final AggregateDataForMaterialStatusDefinition aggregateDataForMaterialStatusDefinition) throws Exception {
    final MaterialStatusEntity materialStatusEntity1 = aggregateDataForMaterialStatusDefinition.getMaterialStatusEntity();
    return MaterialStatusDefinitionUpsertRequestMessage.builder()
        .client(materialStatusEntity1.getClient())
        .materialStatus(materialStatusEntity1.getMaterialStatus())
        .descriptions(aggregateDataForMaterialStatusDefinition.getMaterialStatusEntities().stream()
            .filter(materialStatusEntity -> materialStatusEntity.getLanguage() != null)
            .collect(Collectors.toMap(MaterialStatusDefinitionsEntity::getLanguage,
                MaterialStatusDefinitionsEntity::getDescription
            ))
        )
        .enabled(materialStatusEntity1.getEnabled())
        .obsolete(materialStatusEntity1.getObsolete())
        .build();
  }
}
