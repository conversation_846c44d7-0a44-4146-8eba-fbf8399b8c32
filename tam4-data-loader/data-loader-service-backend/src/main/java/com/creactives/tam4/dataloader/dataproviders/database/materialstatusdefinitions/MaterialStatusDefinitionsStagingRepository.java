package com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialStatusDefinitionsStagingRepository extends PagingAndSortingRepository<MaterialStatusDefinitionsEntity, Long> {

  List<MaterialStatusDefinitionsEntity> findByClientAndMaterialStatus(String client, String materialStatus);

  List<MaterialStatusDefinitionsEntity> findByClientAndLanguageAndMaterialStatusIn(String client, String language, List<String> materialStatus);

  List<MaterialStatusDefinitionsEntity> findByClientAndMaterialStatusAndSynchronizationState(String client, String materialStatus, String synchronizationState);

}
