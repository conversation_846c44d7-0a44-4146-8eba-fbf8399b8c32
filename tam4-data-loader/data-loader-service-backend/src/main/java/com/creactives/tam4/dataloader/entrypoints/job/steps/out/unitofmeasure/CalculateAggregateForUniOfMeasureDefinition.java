package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitsOfMeasureStagingRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Log4j2
@StepScope
public class CalculateAggregateForUniOfMeasureDefinition implements ItemProcessor<UnitOfMeasureKeyEntity, AggregateDataForUnitOfMeasureDefinition> {

  private final UnitsOfMeasureStagingRepository unitsOfMeasureStagingRepository;

  private final boolean ignoreStatus;

  public CalculateAggregateForUniOfMeasureDefinition(final UnitsOfMeasureStagingRepository unitsOfMeasureStagingRepository,
                                                     @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.unitsOfMeasureStagingRepository = unitsOfMeasureStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)

  public AggregateDataForUnitOfMeasureDefinition process(final UnitOfMeasureKeyEntity unitOfMeasureKeyEntity) throws Exception {
    final List<UnitOfMeasureEntity> entities = ignoreStatus ?
        unitsOfMeasureStagingRepository.findByClientAndUnitOfMeasure(unitOfMeasureKeyEntity.getClient(),
            unitOfMeasureKeyEntity.getUnitOfMeasure()
        )
        : unitsOfMeasureStagingRepository.findByClientAndUnitOfMeasureAndSynchronizationState(unitOfMeasureKeyEntity.getClient(),
        unitOfMeasureKeyEntity.getUnitOfMeasure(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForUnitOfMeasureDefinition.builder()
        .unitOfMeasureKeyEntity(unitOfMeasureKeyEntity)
        .unitOfMeasureEntities(entities)
        .build();
  }
}
