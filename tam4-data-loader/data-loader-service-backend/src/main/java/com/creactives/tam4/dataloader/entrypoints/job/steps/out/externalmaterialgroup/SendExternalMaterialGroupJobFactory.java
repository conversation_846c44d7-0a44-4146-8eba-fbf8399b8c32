package com.creactives.tam4.dataloader.entrypoints.job.steps.out.externalmaterialgroup;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendExternalMaterialGroupJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendExternalMaterialGroupAddOrUpdateStep step;


  public SendExternalMaterialGroupJobFactory(final JobBuilderFactory jobBuilderFactory,
                                             final StepBuilderFactory stepBuilderFactory,
                                             final SendExternalMaterialGroupAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendExternalMaterialGroupFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: TWEW, TWEWT, TWEW_Enabled")
        .build("send-external-material-group");
  }
}
