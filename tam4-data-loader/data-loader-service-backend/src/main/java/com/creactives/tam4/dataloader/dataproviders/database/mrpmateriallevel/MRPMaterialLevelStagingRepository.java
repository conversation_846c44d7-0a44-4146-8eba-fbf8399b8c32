package com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MRPMaterialLevelStagingRepository extends PagingAndSortingRepository<MRPMaterialLevelEntity, Long> {

List<MRPMaterialLevelEntity> findByClientAndPlantAndMrpGroupIn(String client, String plant, List<String> mrpGroups);

  Page<MRPMaterialLevelEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MRPMaterialLevelEntity> findAllByClient(String client, Pageable pageable);

  Page<MRPMaterialLevelEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
