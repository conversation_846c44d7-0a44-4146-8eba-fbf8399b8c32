package com.creactives.tam4.dataloader.entrypoints.job.steps.in.logisticshandlinggroupdefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class LogisticsHandlingGroupDefinitionsEnabledEntitiesWriter implements ItemWriter<LogisticsHandlingGroupEntity> {

  private final LogisticsHandlingGroupDefinitionsStagingRepository stagingRepository;

  @Override
  public void write(final List<? extends LogisticsHandlingGroupEntity> list) throws Exception {
    for (final LogisticsHandlingGroupEntity entity : list) {
      if (entity.getEnabled()) {
        stagingRepository.setEnabled(true, entity.getLogisticsHandlingGroup(), entity.getClient());
      }
    }
  }
}
