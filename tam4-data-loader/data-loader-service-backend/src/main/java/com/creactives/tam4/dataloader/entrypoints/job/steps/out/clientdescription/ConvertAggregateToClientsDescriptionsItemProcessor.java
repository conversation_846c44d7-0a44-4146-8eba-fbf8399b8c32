package com.creactives.tam4.dataloader.entrypoints.job.steps.out.clientdescription;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.messaging.SendClientsDescriptionsMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertAggregateToClientsDescriptionsItemProcessor implements ItemProcessor<Client, SendClientsDescriptionsMessage> {

  @Override
  public SendClientsDescriptionsMessage process(final Client item) throws Exception {
    return SendClientsDescriptionsMessage.builder().clients(item.getCode()).descriptions(item.getDescription()).build();
  }
}
