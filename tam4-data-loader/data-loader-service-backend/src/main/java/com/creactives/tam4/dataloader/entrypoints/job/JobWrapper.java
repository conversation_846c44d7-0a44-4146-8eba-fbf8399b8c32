package com.creactives.tam4.dataloader.entrypoints.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersIncrementer;
import org.springframework.batch.core.JobParametersValidator;
import org.springframework.lang.Nullable;

/**
 * Date: 2/25/2021 Time: 1:35 PM
 */
public class Job<PERSON><PERSON>per implements Job {
  private final Job delegate;
  private final JobMetadata metadata;

  public JobWrapper(final Job delegate, final JobMetadata metadata) {
    this.delegate = delegate;
    this.metadata = metadata;
  }

  public JobMetadata getMetadata() {
    return metadata;
  }

  @Override
  public String getName() {
    return delegate.getName();
  }

  @Override
  public boolean isRestartable() {
    return delegate.isRestartable();
  }

  @Override
  public void execute(final JobExecution execution) {
    delegate.execute(execution);
  }

  @Override
  @Nullable
  public JobParametersIncrementer getJobParametersIncrementer() {
    return delegate.getJobParametersIncrementer();
  }

  @Override
  public JobParametersValidator getJobParametersValidator() {
    return delegate.getJobParametersValidator();
  }
}
