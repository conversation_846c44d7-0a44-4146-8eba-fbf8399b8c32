package com.creactives.tam4.dataloader.dataproviders.database.currency;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "currency_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TCURC", description = "Currency Codes")
public class CurrencyEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "currency_staging_generator")
  @SequenceGenerator(name = "currency_staging_generator", sequenceName = "currency_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", description = "Client", referenceTable = "T000")
  private String client;

  @Column(name = "currency_key")
  @FieldDoc(required = true, sapField = "WAERS", description = "Currency Key")
  private String currencyKey;

  @Column(name = "iso_currency_code")
  @FieldDoc(sapField = "ISOCD", description = "ISO currency code")
  private String isoCurrencyCode;

  @Column(name = "alternative_currency_key")
  @FieldDoc(sapField = "ALTWR", description = "Alternative key for currencies")
  private String alternativeCurrencyKey;

  @Column(name = "valid_date")
  @FieldDoc(sapField = "GDATU", description = "Date until which the currency is valid")
  private String validDate;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
