package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureEntity;
import com.google.common.collect.ArrayListMultimap;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForMaterialCreated {

  private final ArrayListMultimap<String, String> characteristics;
  private MaterialEntity materialEntity;
  private List<ShortDescriptionEntity> shortDescriptionEntities;
  private List<LongDescriptionEntity> poDescriptionEntities;
  private List<LongDescriptionEntity> longDescriptionEntities;
  private List<LongDescriptionEntity> internalNoteDescriptionEntities;
  private List<LongDescriptionEntity> inspectionDescriptionEntities;
  private List<AlternativeUnitsOfMeasureEntity> alternativeUnitsOfMeasureEntities;
  //  private List<Attribute> technicalAttributes;
  private Boolean genericMasterData;

}
