package com.creactives.tam4.dataloader.entrypoints.job.steps.in.approvedmanufacturerparts;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.approvedmanufacturerparts.ApprovedManufacturedPartsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.approvedmanufacturerparts.ApprovedManufacturerPartsStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ApprovedManufacturerPartsEntitiesWriter implements ItemWriter<ApprovedManufacturedPartsEntity> {

  private final ApprovedManufacturerPartsStagingRepository approvedManufacturerPartsStagingRepository;

  @Override
  public void write(final List<? extends ApprovedManufacturedPartsEntity> list) throws Exception {
    for (final ApprovedManufacturedPartsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    approvedManufacturerPartsStagingRepository.saveAll(list);
  }
}
