package com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "unit_of_measure_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class UnitOfMeasureEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "unit_of_measure_definitions_staging_generator")
  @SequenceGenerator(name = "unit_of_measure_definitions_staging_generator", sequenceName = "unit_of_measure_definitions_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "unit_of_measure")
  private String unitOfMeasure;

  @Column(name = "external_unit_of_measure_commercial")
  private String externalUnitOfMeasureCommercial;

  @Column(name = "external_unit_of_measure_technical")
  private String externalUnitOfMeasureTechnical;

  @Column(name = "description")
  private String description;

  @Column(name = "short_description")
  private String shortDescription;

  @Column(name = "language")
  private String language;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
