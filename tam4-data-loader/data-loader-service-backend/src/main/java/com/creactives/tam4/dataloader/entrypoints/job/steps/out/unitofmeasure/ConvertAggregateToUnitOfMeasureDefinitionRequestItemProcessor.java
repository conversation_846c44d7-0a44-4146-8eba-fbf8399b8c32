package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;


import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.messaging.materials.commands.UnitOfMeasureDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Log4j2
public class ConvertAggregateToUnitOfMeasureDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForUnitOfMeasureDefinition, UnitOfMeasureDefinitionUpsertRequestMessage> {

  private static String getDescription(final UnitOfMeasureEntity unitOfMeasureEntity) {
    if (StringUtils.isNotBlank(unitOfMeasureEntity.getDescription())) {
      return unitOfMeasureEntity.getDescription();
    } else {
      if (StringUtils.isNotBlank(unitOfMeasureEntity.getShortDescription())) {
        return unitOfMeasureEntity.getShortDescription();
      }
      return "";
    }
  }

  @Override
  public UnitOfMeasureDefinitionUpsertRequestMessage process(final AggregateDataForUnitOfMeasureDefinition aggregateDataForUnitOfMeasureDefinition) throws Exception {
    return UnitOfMeasureDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForUnitOfMeasureDefinition.getUnitOfMeasureKeyEntity().getClient())
        .unitOfMeasure(aggregateDataForUnitOfMeasureDefinition.getUnitOfMeasureKeyEntity().getUnitOfMeasure())
        .descriptions(aggregateDataForUnitOfMeasureDefinition.getUnitOfMeasureEntities().stream()
            .filter(unitOfMeasureEntity -> unitOfMeasureEntity.getLanguage() != null)
            .collect(Collectors.toMap(UnitOfMeasureEntity::getLanguage,
                ConvertAggregateToUnitOfMeasureDefinitionRequestItemProcessor::getDescription
            ))
        )
        .build();
  }
}
