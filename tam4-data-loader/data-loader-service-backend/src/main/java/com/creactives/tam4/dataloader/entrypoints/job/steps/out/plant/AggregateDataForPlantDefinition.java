package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Data
@Builder
public class AggregateDataForPlantDefinition {

  private PlantEntity plantEntity;
  private PlantCurrencyEntity currencyEntity;
  private Set<String> additionalLanguages;
}
