package com.creactives.tam4.dataloader.dataproviders.database.basicmaterial;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BasicMaterialStagingRepository extends PagingAndSortingRepository<BasicMaterialEntity, Long> {

  List<BasicMaterialEntity> findByClientAndBasicMaterialIn(String client, List<String> basicMaterials);

  Page<BasicMaterialEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<BasicMaterialEntity> findAllByClient(String client, Pageable pageable);

  Page<BasicMaterialEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
