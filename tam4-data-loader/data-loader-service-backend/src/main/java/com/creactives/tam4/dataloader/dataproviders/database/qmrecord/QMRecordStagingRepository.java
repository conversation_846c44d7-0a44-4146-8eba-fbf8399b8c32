package com.creactives.tam4.dataloader.dataproviders.database.qmrecord;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface QMRecordStagingRepository extends PagingAndSortingRepository<QMRecordsEntity, Long> {
  List<QMRecordsEntity> findByClientAndMaterialNumberAndInternalCounterAndPlantIn(String client,
                                                                                  String materialNumber,
                                                                                  String internalCounter,
                                                                                  Collection<String> plant);
}
