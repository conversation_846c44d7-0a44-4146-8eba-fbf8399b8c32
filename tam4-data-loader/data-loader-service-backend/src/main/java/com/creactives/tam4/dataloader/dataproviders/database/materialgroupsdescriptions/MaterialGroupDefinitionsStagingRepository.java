package com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Created on 6/11/2019 5:01 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface MaterialGroupDefinitionsStagingRepository extends PagingAndSortingRepository<MaterialGroupDefinitionEntity, Long> {

  Optional<MaterialGroupDefinitionEntity> findByClientAndMaterialGroupAndLanguage(String client, String materialGroup, String language);

  List<MaterialGroupDefinitionEntity> findByClientAndMaterialGroup(String client, String materialGroup);

  List<MaterialGroupDefinitionEntity> findByClientAndMaterialGroupIn(String client, List<String> materialGroup);
}
