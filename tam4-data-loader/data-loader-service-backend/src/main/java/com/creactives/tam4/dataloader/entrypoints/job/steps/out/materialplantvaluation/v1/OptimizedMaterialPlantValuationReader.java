package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.SimpleJdbcBatchItemReader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.creactives.tam4.dataloader.core.entity.BigDecimalConverter.toBigDecimal;

@Component
@StepScope
public class
OptimizedMaterialPlantValuationReader extends SimpleJdbcBatchItemReader<Long, AggregateDataForMaterialPlantValuation> {

  private final String BASE_QUERY = "SELECT " +
      "    mds.*, " +
      "    array_agg(distinct concat(pd.plant, ':', pd.country_key)) AS plants, " +
      "    array_agg(distinct concat(pc.plant, ':', pc.currency)) AS currency, " +
      "    array_agg(distinct concat(ho.id, ':', ho.plant_code, ':', ho.ordered_quantity::text, ':', ho.ordered_amount::text, ':', ho.currency)) AS history, " +
      "    array_agg(distinct concat(co.id, ':', co.plant_code, ':', co.consumption_quantity::text, ':', co.consumption_amount::text, ':', co.currency)) AS consumption, " +
      "    array_agg(distinct concat(mvd.id, ':', mvd.valuation_type, ':', mvd.plant_id, ':', mvd.valuation_class, ':', mvd.valuation_category, ':', mvd.inventory_amount::text, ':', mvd.total_value::text, ':', mvd.moving_average_price::text, ':', mvd.standard_price::text, ':', mvd.price_unit::text, ':', mvd.price_control_indicator::text, ':', mvd.usage_material::text, ':', mvd.origin_material::text, ':', mvd.client)) AS valuation " +
      "  FROM materials_data_staging mds " +
      "    LEFT JOIN plants_valuation_data_staging mvd ON mds.client = mvd.client AND mds.material_code = mvd.material_code " +
      "    LEFT JOIN history_order_staging ho on mds.client = ho.client AND mds.material_code = ho.material_code " +
      "    LEFT JOIN consumption_order_staging co on mds.client = co.client AND mds.material_code = co.material_code " +
      "    LEFT JOIN plant_definitions_staging pd ON mvd.client = pd.client AND mvd.plant_id = pd.plant " +
      "    LEFT JOIN plant_currency pc on pd.client = pc.client AND pd.plant = pc.plant ";
  private final String GROUP_AN_ORDER_BY = "  GROUP BY mds.id" +
      "  ORDER BY mds.id";

  private final JdbcTemplate jdbcTemplate;
  private final int fetchSize;

  private final String clientCode;

  private final boolean ignoreStatus;

  public OptimizedMaterialPlantValuationReader(final JdbcTemplate jdbcTemplate,
                                               @Value("#{jobParameters['clientCode']}") final String clientCode,
                                               @Value("#{jobParameters['ignore_status']}") final String ignoreStatus,
                                               @Value("${data-loader.send-job.materials-chunk-size:1000}") final int pageSize) {
    this.jdbcTemplate = jdbcTemplate;
    this.clientCode = clientCode;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
    this.fetchSize = pageSize;
  }

  private static Map<String, PlantEntity> readPlants(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("plants");
    final String[] array = (String[]) country.getArray();
    final Map<String, PlantEntity> out = new HashMap<>();
    if (array != null) {
      for (final String plantsEncoded : array) {
        if (plantsEncoded != null && StringUtils.isNotBlank(plantsEncoded.replaceAll(":", "").trim())) {
          final int i = plantsEncoded.indexOf(':', -1);
          final String key = plantsEncoded.substring(0, i);
          final String value = plantsEncoded.substring(i + 1);
          out.put(key, PlantEntity.builder().plant(key).countryKey(value).build());
        }
      }
    }
    return out;
  }

  private static Map<String, PlantCurrencyEntity> readCurrency(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("currency");
    final String[] array = (String[]) country.getArray();
    final Map<String, PlantCurrencyEntity> out = new HashMap<>();
    if (array != null) {
      for (final String currencyEncoded : array) {
        if (currencyEncoded != null && StringUtils.isNotBlank(currencyEncoded.replaceAll(":", "").trim())) {
          final int i = currencyEncoded.indexOf(':', -1);
          final String key = currencyEncoded.substring(0, i);
          final String value = currencyEncoded.substring(i + 1);
          out.put(key, PlantCurrencyEntity.builder().plant(key).currency(value).build());
        }
      }
    }
    return out;
  }

  private static List<MaterialPlantValuationEntity> readValuation(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("valuation");
    final String[] array = (String[]) country.getArray();
    final List<MaterialPlantValuationEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String valuationsEncoded : array) {
        if (valuationsEncoded != null && StringUtils.isNotBlank(valuationsEncoded.replaceAll(":", "").trim())) {
          final String[] split = valuationsEncoded.split(":", -1);
          out.add(MaterialPlantValuationEntity.builder()
              .id(Long.parseLong(split[0]))
              .valuationType(split[1])
              .plantId(split[2])
              .valuationClass(split[3])
              .valuationCategory(split[4])
              .inventoryAmount(toBigDecimal(split[5]))
              .totalValue(toBigDecimal(split[6]))
              .movingAveragePrice(toBigDecimal(split[7]))
              .standardPrice(toBigDecimal(split[8]))
              .priceUnit(Integer.parseInt(split[9]))
              .priceControlIndicator(split[10])
              .usageMaterial(split[11])
              .originMaterial(split[12])
              .client(split[13])
              .build());

        }
      }
    }
    return out;
  }

  private static Map<String, List<POHistoryEntity>> readHistory(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("history");
    final String[] array = (String[]) country.getArray();
    final Collection<POHistoryEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String historyEncoded : array) {
        if (historyEncoded != null && StringUtils.isNotBlank(historyEncoded.replaceAll(":", "").trim())) {
          final String[] split = historyEncoded.split(":", -1);
          for (int i = 0; i < split.length; i += 5) {
            out.add(POHistoryEntity.builder()
                .id(Long.parseLong(split[i]))
                .plantCode(split[i + 1])
                .orderedQuantity(toBigDecimal(split[i + 2]))
                .orderedAmount(toBigDecimal(split[i + 3]))
                .currency(split[i + 4])
                .build());

          }
        }
      }
    }
    return out.stream()
        .collect(Collectors.groupingBy(POHistoryEntity::getPlantCode));
  }

  private static Map<String, List<ConsumptionDataEntity>> readConsumption(final ResultSet rs) throws SQLException {
    final Array country = rs.getArray("consumption");
    final String[] array = (String[]) country.getArray();
    final Collection<ConsumptionDataEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String consumptionEncoded : array) {
        if (consumptionEncoded != null && StringUtils.isNotBlank(consumptionEncoded.replaceAll(":", "").trim())) {
          final String[] split = consumptionEncoded.split(":", -1);
          for (int i = 0; i < split.length; i += 5) {
            out.add(ConsumptionDataEntity.builder()
                .id(Long.parseLong(split[i]))
                .plantCode(split[i + 1])
                .consumptionQuantity(toBigDecimal(split[i + 2]))
                .consumptionAmount(toBigDecimal(split[i + 3]))
                .currency(split[i + 4])
                .build());

          }
        }
      }
    }
    return out.stream()
        .collect(Collectors.groupingBy(ConsumptionDataEntity::getPlantCode));
  }

  @Override
  protected Long getKey(final AggregateDataForMaterialPlantValuation entity) {
    return entity.getMaterialEntity().getId();
  }

  @Override
  protected List<AggregateDataForMaterialPlantValuation> queryForFirstPage() {
    final String condition = buildCondition();
    return jdbcTemplate.query(BASE_QUERY + " WHERE " + condition + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        fetchSize
    );
  }

  @Override
  protected List<AggregateDataForMaterialPlantValuation> queryAfterId(final Long id) {
    final String condition = buildCondition();
    return jdbcTemplate.query(BASE_QUERY + " where " + condition + " and mds.id > ? " + GROUP_AN_ORDER_BY + " limit ?",
        (rs, rowNum) -> map(rs),
        id,
        fetchSize
    );
  }

  private String buildCondition() {
    final StringBuilder condition = new StringBuilder(" mds.ignore is false ");
//        condition.append(MessageFormat.format("and mds.synchronization_state = ''{0}''", SyncStatus.PENDING.getCode()));
    if (clientCode != null) {
      condition.append(MessageFormat.format(" and mds.client = ''{0}''", clientCode));
    }
    if (!ignoreStatus) {
      condition.append(MessageFormat.format(" and mds.synchronization_state = ''{0}''", SyncStatus.PENDING.getCode()));
    }
    return condition.toString();
  }

  private AggregateDataForMaterialPlantValuation map(final ResultSet rs) throws SQLException {
    return AggregateDataForMaterialPlantValuation.builder()
        .materialEntity(readMaterial(rs))
        .materialPlantValuationEntities(readValuation(rs))
        .orderedValuesByPlant(readHistory(rs))
        .plantCurrencies(readCurrency(rs))
        .plants(readPlants(rs))
        .consumptionValuesByPlant(readConsumption(rs))
        .build();
  }

  private MaterialEntity readMaterial(final ResultSet rs) throws SQLException {
    return MaterialEntity.builder()
        .id(rs.getLong("id"))
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .deletionFlag(rs.getBoolean("deletion_flag"))
        .materialType(rs.getString("material_type"))
        .industrySector(rs.getString("industry_sector"))
        .materialGroup(rs.getString("material_group"))
        .oldMaterialNumber(rs.getString("old_material_number"))
        .baseUnitOfMeasurement(rs.getString("base_unit_of_measurement"))
        .productDivision(rs.getString("product_division"))
        .authorizationGroup(rs.getString("authorization_group"))
        .crossPlantMaterialStatus(rs.getString("cross_plant_material_status"))
        .materialStatusValidFromDate(rs.getLong("material_status_valid_from_date"))
        .manufacturerPartNumber(rs.getString("manufacturer_part_number"))
        .manufacturerCode(rs.getString("manufacturer_code"))
        .genericItemGroup(rs.getString("generic_item_group"))
        .revisionNumber(rs.getString("revision_number"))
        .purchasingMeasurementUnits(rs.getString("purchasing_measurement_units"))
        .materialCreatedOn(rs.getString("material_created_on"))
        .externalMaterialGroup(rs.getString("external_material_group"))
        .weightUnit(rs.getString("weight_unit"))
        .netWeight(rs.getBigDecimal("net_weight"))
        .grossWeight(rs.getBigDecimal("gross_weight"))
        .sizeDimension(rs.getString("size_dimension"))
        .hazardousMaterialNumber(rs.getString("hazardous_material_number"))
        .ignore(rs.getBoolean("ignore"))
        .createdOn(rs.getTimestamp("created_on"))
        .lastModifiedOn(rs.getTimestamp("last_modified_on"))
        .synchronizedOn(rs.getTimestamp("synchronized_on"))
        .synchronizationConfirmedOn(rs.getTimestamp("synchronization_confirmed_on"))
        .synchronizationState(rs.getString("synchronization_state"))
        .semanticallyAnalyzed(rs.getBoolean("semantically_analyzed"))
        .mdDomain(rs.getString("md_domain"))

        ////FIXME: Custom fields required by A2A T4-1756
        .famiglia(rs.getString("famiglia"))
        .sottoFamiglia(rs.getString("sotto_famiglia"))
        .specificaTecnica(rs.getString("specifica_tecnica"))
        .edizione(rs.getString("edizione"))
        .revisione(rs.getString("revisione"))
        .dataCustom(rs.getLong("data_custom"))

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(rs.getString("product_hierarchy"))

        .build();
  }

}
