package com.creactives.tam4.dataloader.dataproviders.database.mmvmaterialStatus;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MMVMaterialStatusStagingRepository extends PagingAndSortingRepository<MMVMaterialStatusEntity, Long> {

  List<MMVMaterialStatusEntity> findByClientAndMaterialStatusIn(String client, List<String> mmvMaterialStatus);
}
