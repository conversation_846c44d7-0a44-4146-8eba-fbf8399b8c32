package com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency;

import com.creactives.tam4.messaging.PlantKey;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 12/9/2019 6:49 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@CacheConfig(cacheNames = "plantCurrency")
@Service
@Log4j2
public class PlantCurrencyDataProvider implements GetPlantCurrency {

  private static final String CACHE_NAME = "plantCurrency";

  private final PlantCurrencyRepository plantCurrencyRepository;

  private final CacheManager cacheManager;

  public PlantCurrencyDataProvider(final PlantCurrencyRepository plantCurrencyRepository,
                                   final CacheManager cacheManager) {
    this.plantCurrencyRepository = plantCurrencyRepository;
    this.cacheManager = cacheManager;
  }

  @Cacheable
  @Override
  public Map<PlantKey, String> getPlantCurrencies() {
    final List<PlantCurrencyEntity> plantCurrencyEntities = Lists.newArrayList(plantCurrencyRepository.findAll());
    log.info("Caching {} plant currencies.", plantCurrencyEntities.size());
    return plantCurrencyEntities.stream()
        .collect(Collectors.toMap(it -> new PlantKey(it.getPlant(), it.getClient()),
            PlantCurrencyEntity::getCurrency));
  }

  public void refresh() {
    if (cacheManager.getCache(CACHE_NAME) != null) {
      cacheManager.getCache(CACHE_NAME).clear();
      log.info("Evict {} cache", CACHE_NAME);
    }
  }

}
