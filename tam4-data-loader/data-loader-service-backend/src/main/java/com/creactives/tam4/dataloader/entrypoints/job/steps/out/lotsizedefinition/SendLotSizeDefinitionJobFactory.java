package com.creactives.tam4.dataloader.entrypoints.job.steps.out.lotsizedefinition;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendLotSizeDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendLotSizeDefinitionAddOrUpdateStep step;

  public SendLotSizeDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                         final StepBuilderFactory stepBuilderFactory,
                                         final SendLotSizeDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  private JobFactory sendLotSizeDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T439T")
        .build("send-lot-size-definition");
  }
}
