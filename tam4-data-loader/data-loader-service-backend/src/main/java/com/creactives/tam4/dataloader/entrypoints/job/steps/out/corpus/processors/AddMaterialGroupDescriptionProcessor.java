package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.FindMaterialGroupDescription;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
@RequiredArgsConstructor
@Component
public class AddMaterialGroupDescriptionProcessor implements ItemProcessor<Material, Material> {
  private final FindMaterialGroupDescription findMaterialGroupDescription;

  //  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final String materialGroupId = item.getMaterialGroupId();
    if (StringUtils.isNotBlank(materialGroupId)) {
      return Material.from(item)
          .materialGroupDescription(findMaterialGroupDescription.findMaterialGroupDescription(item.getClient(), materialGroupId))
          .build();
    }
    return item;
  }
}
