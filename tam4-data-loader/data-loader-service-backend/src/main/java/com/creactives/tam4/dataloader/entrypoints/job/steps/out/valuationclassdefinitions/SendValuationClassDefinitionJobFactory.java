package com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendValuationClassDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendValuationClassDefinitionAddOrUpdateStep step;

  public SendValuationClassDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                final StepBuilderFactory stepBuilderFactory,
                                                final SendValuationClassDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendValuationClassDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T025T")
        .build("send-valuation-class-definition");
  }
}
