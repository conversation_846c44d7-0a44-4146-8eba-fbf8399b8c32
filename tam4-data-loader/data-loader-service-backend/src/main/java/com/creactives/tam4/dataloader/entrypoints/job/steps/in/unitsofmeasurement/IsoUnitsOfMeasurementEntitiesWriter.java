package com.creactives.tam4.dataloader.entrypoints.job.steps.in.unitsofmeasurement;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.ISOUnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.IsoUnitsOfMeasurementStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class IsoUnitsOfMeasurementEntitiesWriter implements ItemWriter<ISOUnitsOfMeasurementEntity> {

  private final IsoUnitsOfMeasurementStagingRepository unitsOfMeasurementStagingRepository;

  @Override
  public void write(final List<? extends ISOUnitsOfMeasurementEntity> list) throws Exception {
    for (final ISOUnitsOfMeasurementEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    unitsOfMeasurementStagingRepository.saveAll(list);
  }
}
