package com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitsOfMeasureStagingRepository extends PagingAndSortingRepository<UnitOfMeasureEntity, Long> {

  List<UnitOfMeasureEntity> findByClientAndUnitOfMeasure(String client, String unitOfMeasure);

  List<UnitOfMeasureEntity> findByClientAndLanguageAndUnitOfMeasureIn(String client, String language, List<String> unitOfMeasure);

  List<UnitOfMeasureEntity> findByClientAndUnitOfMeasureAndSynchronizationState(String client, String unitOfMeasure, String status);

}
