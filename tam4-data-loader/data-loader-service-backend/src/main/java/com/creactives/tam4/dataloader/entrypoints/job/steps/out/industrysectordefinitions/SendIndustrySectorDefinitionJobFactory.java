package com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendIndustrySectorDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendIndustrySectorDefinitionAddOrUpdateStep step;

  public SendIndustrySectorDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                final StepBuilderFactory stepBuilderFactory,
                                                final SendIndustrySectorDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendIndustrySectorDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T137T, T137T_Enabled")
        .build("send-industry-sector-definition");
  }
}
