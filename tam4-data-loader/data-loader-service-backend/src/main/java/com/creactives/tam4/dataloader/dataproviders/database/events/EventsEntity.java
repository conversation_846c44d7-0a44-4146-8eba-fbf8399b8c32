package com.creactives.tam4.dataloader.dataproviders.database.events;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "event_staging")
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventsEntity {

  @Id
  @Column(name = "id")
  private long id;
  @Column(name = "correlation_id")
  private String correlationId;
  @Column(name = "entity")
  private String entity;
  @Column(name = "entity_id")
  private String entityId;
  @Column(name = "event_type")
  private String eventType;
  @Column(name = "description")
  private String description;
  @Column(name = "rule_id")
  private Integer ruleId;
  @Column(name = "status")
  private String status;
  @Column(name = "attributes")
  private String attributes;
  @Column(name = "new_value")
  private String newValue;
  @Column(name = "old_value")
  private String oldValue;
  @Column(name = "created_on")
  private Timestamp createdOn;
  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;
  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;
  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;
  @Column(name = "synchronization_state")
  private String synchronizationState;
  @Column(name = "log_system")
  private String logSystem;


}
