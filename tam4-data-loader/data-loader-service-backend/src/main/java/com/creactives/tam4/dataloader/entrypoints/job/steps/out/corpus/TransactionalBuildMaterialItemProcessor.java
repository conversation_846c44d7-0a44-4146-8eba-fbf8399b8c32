package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus;

import com.creactives.tam4.dataloader.configuration.IncludeConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddCharacteristicsProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddLongDescriptionsItemProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddLongInspectionDescriptionsItemProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddLongInternalNoteDescriptionsItemProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddLongPoDescriptionsItemProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialGroupDescriptionProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialPlantsProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialTotalsProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialWarehousesProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddShortDescriptionsItemProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddSupplierNameProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.CreateMaterialProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.NormalizeStuffProcessor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class TransactionalBuildMaterialItemProcessor implements
    ItemProcessor<MaterialEntity, Material> {

  private final CompositeItemProcessor<MaterialEntity, Material> itemProcessor;

  public TransactionalBuildMaterialItemProcessor(final IncludeConfiguration includeConfiguration,
                                                 final CreateMaterialProcessor createMaterialProcessor,
                                                 final AddMaterialGroupDescriptionProcessor addMaterialGroupDescriptionProcessor,
                                                 final AddShortDescriptionsItemProcessor addShortDescriptionsItemProcessor,
                                                 final AddSupplierNameProcessor addSupplierNameProcessor,
                                                 final AddMaterialPlantsProcessor addMaterialPlantsProcessor,
                                                 final AddMaterialWarehousesProcessor addMaterialWarehousesProcessor,
                                                 final AddMaterialTotalsProcessor addMaterialTotalsProcessor,
                                                 final AddCharacteristicsProcessor addCharacteristicsProcessor,
                                                 final AddLongDescriptionsItemProcessor addLongDescriptionsItemProcessor,
                                                 final AddLongPoDescriptionsItemProcessor addLongPoDescriptionsItemProcessor,
                                                 final AddLongInternalNoteDescriptionsItemProcessor addLongInternalNoteDescriptionsItemProcessor,
                                                 final AddLongInspectionDescriptionsItemProcessor addLongInspectionDescriptionsItemProcessor,
                                                 final NormalizeStuffProcessor normalizeStuffProcessor) {
    final List<ItemProcessor<?, Material>> processors = new ArrayList<>();
    processors.add(createMaterialProcessor);
    customizeProcessor(includeConfiguration, addMaterialGroupDescriptionProcessor,
        addShortDescriptionsItemProcessor, addSupplierNameProcessor, addMaterialPlantsProcessor,
        addMaterialWarehousesProcessor, addMaterialTotalsProcessor, addCharacteristicsProcessor,
        addLongDescriptionsItemProcessor, addLongPoDescriptionsItemProcessor,
        addLongInternalNoteDescriptionsItemProcessor, addLongInspectionDescriptionsItemProcessor,
        processors);
    processors.add(normalizeStuffProcessor);
    itemProcessor = new CompositeItemProcessorBuilder<MaterialEntity, Material>()
        .delegates(processors)
        .build();
  }

  private void customizeProcessor(final IncludeConfiguration includeConfiguration,
                                  final ItemProcessor<Material, Material> addMaterialGroupDescriptionProcessor,
                                  final ItemProcessor<Material, Material> addShortDescriptionsItemProcessor,
                                  final ItemProcessor<Material, Material> addSupplierNameProcessor,
                                  final ItemProcessor<Material, Material> addMaterialPlantsProcessor,
                                  final ItemProcessor<Material, Material> addMaterialWarehousesProcessor,
                                  final ItemProcessor<Material, Material> addMaterialTotalsProcessor,
                                  final ItemProcessor<Material, Material> addCharacteristicsProcessor,
                                  final ItemProcessor<Material, Material> addLongDescriptionsItemProcessor,
                                  final ItemProcessor<Material, Material> addLongPoDescriptionsItemProcessor,
                                  final ItemProcessor<Material, Material> addLongInternalNoteDescriptionsItemProcessor,
                                  final ItemProcessor<Material, Material> addLongInspectionDescriptionsItemProcessor,
                                  final List<ItemProcessor<?, Material>> processors) {

    if (includeConfiguration.isMaterialGroupDescription()) {
      processors.add(addMaterialGroupDescriptionProcessor);
    }
    if (includeConfiguration.isShortDescriptions()) {
      processors.add(addShortDescriptionsItemProcessor);
    }
    if (includeConfiguration.isLongDescriptionsPO()) {
      processors.add(addLongPoDescriptionsItemProcessor);
    }
    if (includeConfiguration.isLongDescriptions()) {
      processors.add(addLongDescriptionsItemProcessor);
    }
    if (includeConfiguration.isInternalNoteDescriptions()) {
      processors.add(addLongInternalNoteDescriptionsItemProcessor);
    }
    if (includeConfiguration.isInspectionDescriptions()) {
      processors.add(addLongInspectionDescriptionsItemProcessor);
    }
    if (includeConfiguration.isSupplierName()) {
      processors.add(addSupplierNameProcessor);
    }
    if (includeConfiguration.isMaterialPlants()) {
      processors.add(addMaterialPlantsProcessor);
    }
    if (includeConfiguration.isMaterialWarehouses()) {
      processors.add(addMaterialWarehousesProcessor);
    }
    if (includeConfiguration.isMaterialTotals()) {
      processors.add(addMaterialTotalsProcessor);
    }
    if (includeConfiguration.isCharacteristics()) {
      processors.add(addCharacteristicsProcessor);
    }
  }


  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final MaterialEntity materialEntity) throws Exception {
    return itemProcessor.process(materialEntity);
  }
}
