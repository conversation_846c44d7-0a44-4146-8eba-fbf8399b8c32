package com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorsDefinitionStagingRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Log4j2
@Component
@StepScope
public class CalculateAggregateForIndustrySectorDefinition implements ItemProcessor<IndustrySectorEntity, AggregateDataForIndustrySectorDefinition> {

  private final IndustrySectorsDefinitionStagingRepository industrySectorsDefinitionStagingRepository;

  private final boolean ignoreStatus;

  public CalculateAggregateForIndustrySectorDefinition(final IndustrySectorsDefinitionStagingRepository industrySectorsDefinitionStagingRepository,
                                                       @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.industrySectorsDefinitionStagingRepository = industrySectorsDefinitionStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  public AggregateDataForIndustrySectorDefinition process(final @NotNull IndustrySectorEntity industrySectorEntity) throws Exception {
    final List<IndustrySectorDefinitionEntity> entities =
        ignoreStatus ?
            industrySectorsDefinitionStagingRepository.findByClientAndIndustrySector(industrySectorEntity.getClient(),
                industrySectorEntity.getIndustrySector())
            : industrySectorsDefinitionStagingRepository.findByClientAndIndustrySectorAndSynchronizationState(industrySectorEntity.getClient(),
            industrySectorEntity.getIndustrySector(),
            SyncStatus.PENDING.getCode()
        );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForIndustrySectorDefinition.builder()
        .industrySectorEntity(industrySectorEntity)
        .industrySectorEntities(entities)
        .build();
  }

}
