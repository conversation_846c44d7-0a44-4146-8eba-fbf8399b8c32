package com.creactives.tam4.dataloader.entrypoints.job.steps.in.unitofmeasure;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.ISOUnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.ISOUnitsOfMeasureStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class ISOUnitsOfMeasureEntitiesWriter implements ItemWriter<ISOUnitOfMeasureEntity> {

  private final ISOUnitsOfMeasureStagingRepository isoUnitsOfMeasureStagingRepository;

  @Override
  public void write(final List<? extends ISOUnitOfMeasureEntity> list) throws Exception {
    for (final ISOUnitOfMeasureEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    isoUnitsOfMeasureStagingRepository.saveAll(list);


  }
}
