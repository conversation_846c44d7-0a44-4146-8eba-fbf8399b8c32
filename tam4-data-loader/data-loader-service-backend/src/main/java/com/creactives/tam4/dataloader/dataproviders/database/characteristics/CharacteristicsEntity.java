package com.creactives.tam4.dataloader.dataproviders.database.characteristics;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "characteristics_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "AUSP", description = "Characteristic Values")
public class CharacteristicsEntity implements ClientEnrichableEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "characteristics_staging_generator")
  @SequenceGenerator(name = "characteristics_staging_generator", sequenceName = "characteristics_staging_seq", allocationSize = 100000)
  private long id;

  @Column(name = "material_code")
  @FieldDoc(required = true, sapField = "OBJEK", referenceTable = "MARA", description = "Key of object to be classified")
  private String materialCode;

  @Column
  @FieldDoc(required = true, sapField = "ATINN", description = "Internal characteristic")
  private String code;

  @Column
  @FieldDoc(sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column
  @FieldDoc(required = true, sapField = "CABN/ATNAM", description = "Characteristic Name")
  private String description;

  @Column
  @FieldDoc(sapField = "ATWRT", description = "Characteristic Value")
  private String value;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
