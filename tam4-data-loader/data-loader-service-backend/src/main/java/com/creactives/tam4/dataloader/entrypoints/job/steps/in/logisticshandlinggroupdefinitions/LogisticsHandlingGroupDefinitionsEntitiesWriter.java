package com.creactives.tam4.dataloader.entrypoints.job.steps.in.logisticshandlinggroupdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupDefinitionsStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions.LogisticsHandlingGroupEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class LogisticsHandlingGroupDefinitionsEntitiesWriter implements ItemWriter<LogisticsHandlingGroupEntity> {

  private final LogisticsHandlingGroupDefinitionsStagingRepository stagingRepository;

  @Override
  public void write(final List<? extends LogisticsHandlingGroupEntity> list) throws Exception {
    for (final LogisticsHandlingGroupEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    stagingRepository.saveAll(list);
  }
}
