package com.creactives.tam4.dataloader.entrypoints.job.steps.in.unitsofmeasurement;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class UnitsOfMeasurementEntitiesWriter implements ItemWriter<UnitsOfMeasurementEntity> {

  private final UnitsOfMeasurementStagingRepository unitsOfMeasurementStagingRepository;

  @Override
  public void write(final List<? extends UnitsOfMeasurementEntity> list) throws Exception {
    for (final UnitsOfMeasurementEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    unitsOfMeasurementStagingRepository.saveAll(list);
  }
}
