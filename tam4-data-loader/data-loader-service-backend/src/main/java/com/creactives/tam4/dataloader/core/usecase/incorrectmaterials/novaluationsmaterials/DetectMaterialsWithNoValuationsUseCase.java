package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.novaluationsmaterials;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialsWithNoValuationsUseCase {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialsWithNoValuationsUseCase(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoValuations(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-valuations")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoValuations(client))
        .build();
  }
}
