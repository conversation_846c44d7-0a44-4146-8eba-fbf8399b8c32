package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcertificatecategorydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions.QMCertificateCategoryDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions.QMCertificateCategoryDefinitonStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

import static com.creactives.tam4.dataloader.core.entity.SyncStatus.PENDING;

@RequiredArgsConstructor
@Component
@StepScope
public class QMCertificateCategoryDefinitionEntitiesWriter implements ItemWriter<QMCertificateCategoryDefinitionEntity> {

  private final QMCertificateCategoryDefinitonStagingRepository repository;
  private final QMCertificateCategoryStagingRepository qmCertificateCategoryStagingRepository;

  @Override
  public void write(final List<? extends QMCertificateCategoryDefinitionEntity> list) throws Exception {
    for (final QMCertificateCategoryDefinitionEntity entity : list) {
      entity.setSynchronizationState(PENDING.getCode());
    }
    repository.saveAll(list);
    for (final QMCertificateCategoryDefinitionEntity entity : list) {

      if (qmCertificateCategoryStagingRepository.findByClientAndCertificateType(entity.getClient(), entity.getCertificateType()).isEmpty()) {
        qmCertificateCategoryStagingRepository.save(QMCertificateCategoryEntity.builder()
            .client(entity.getClient())
            .certificateType(entity.getCertificateType())
            .enabled(true)
            .synchronizationState(PENDING.getCode())
            .createdOn(new Timestamp(System.currentTimeMillis())).build());
      }
    }
  }
}
