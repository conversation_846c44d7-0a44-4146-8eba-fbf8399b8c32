package com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Table(name = "plant_currency")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PlantCurrencyEntity implements ClientEnrichableEntity, Serializable {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "plant_currency_generator")
  @SequenceGenerator(name = "plant_currency_generator", sequenceName = "plant_currency_seq", allocationSize = 1000)
  private long id;

  @Column
  private String client;

  @Column
  private String plant;

  @Column
  private String currency;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;
}
