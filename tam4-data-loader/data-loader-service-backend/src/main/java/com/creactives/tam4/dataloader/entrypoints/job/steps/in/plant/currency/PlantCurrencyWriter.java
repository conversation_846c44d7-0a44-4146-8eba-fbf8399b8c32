package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.currency;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyDataProvider;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Log4j2
@Component
public class PlantCurrencyWriter implements ItemWriter<PlantCurrencyEntity> {

  private final PlantCurrencyRepository plantCurrencyRepository;

  private final PlantCurrencyDataProvider plantCurrencyDataProvider;

  @Override
  public void write(final List<? extends PlantCurrencyEntity> items) {
    final List<PlantCurrencyEntity> itemsToSave = new ArrayList<>();
    for (final PlantCurrencyEntity entity :
        items) {
      //check if not already existing
      if (plantCurrencyRepository.findByClientAndPlant(entity.getClient(), entity.getPlant())
          == null) {
        entity.setSynchronizationState(SyncStatus.PENDING.getCode());
        itemsToSave.add(entity);
      } else {
        //update entity
        final PlantCurrencyEntity existingEntity = plantCurrencyRepository.findByClientAndPlant(entity.getClient(), entity.getPlant());
        if (!Objects.equals(existingEntity.getCurrency(), entity.getCurrency())) {
          existingEntity.setCurrency(entity.getCurrency());
          existingEntity.setLastModifiedOn(new Timestamp(System.currentTimeMillis()));
          itemsToSave.add(existingEntity);
        }
      }
    }
    plantCurrencyRepository.saveAll(itemsToSave);
    plantCurrencyDataProvider.refresh();
  }
}
