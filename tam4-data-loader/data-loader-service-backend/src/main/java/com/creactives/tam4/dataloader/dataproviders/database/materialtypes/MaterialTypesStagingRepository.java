package com.creactives.tam4.dataloader.dataproviders.database.materialtypes;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialTypesStagingRepository extends PagingAndSortingRepository<MaterialTypesEntity, Long> {

  List<MaterialTypesEntity> findByClientAndMaterialType(String client, String materialType);

  List<MaterialTypesEntity> findByClientAndMaterialTypeIn(String client, List<String> materialType);

  @Modifying
  @Query("update MaterialTypesEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update MaterialTypesEntity set enabled= :enabled WHERE materialType=:materialType AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("materialType") String materialType, @Param("client") String client);

  Page<MaterialTypesEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MaterialTypesEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);

  Page<MaterialTypesEntity> findAllByClient(String client, Pageable pageable);
}
