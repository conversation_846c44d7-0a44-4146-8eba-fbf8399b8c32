package com.creactives.tam4.dataloader.dataproviders.database.lotsizedefinition;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "lot_size_definition_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T439T", description = "Texts for lot-sizing procedures")
public class LotSizeDefinitionEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "lot_size_definition_staging_generator")
  @SequenceGenerator(name = "lot_size_definition_staging_generator", sequenceName = "lot_size_definition_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "lot_size")
  @FieldDoc(required = true, sapField = "DISLS", referenceTable = "T000", description = "Lot size (materials planning)")
  private String lotSize;

  @Column(name = "language")
  @FieldDoc(required = true, sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column(name = "description")
  @FieldDoc(required = true, sapField = "LOSLT", description = "Description of lot-sizing procedure")
  private String description;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
