package com.creactives.tam4.dataloader.dataproviders.database.supplier;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.FindSupplierName;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@CacheConfig(cacheNames = "supplierNames")
public class FindSupplierNameProvider implements FindSupplierName {
  private final SupplierStagingRepository supplierStagingRepository;


  @Cacheable
  @Override
  public String findSupplierName(final String client, final String manufacturer) {
    return supplierStagingRepository
        .findByClientAndCode(client, manufacturer)
        .map(SupplierEntity::getName)
        .orElse(null);
  }

}
