package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForUnitOfMeasureDefinition {

  private UnitOfMeasureKeyEntity unitOfMeasureKeyEntity;
  private List<UnitOfMeasureEntity> unitOfMeasureEntities;
}
