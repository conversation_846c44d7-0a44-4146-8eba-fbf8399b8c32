package com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchasingGroupsStagingRepository extends PagingAndSortingRepository<PurchasingGroupEntity, Long> {

  List<PurchasingGroupEntity> findByClientAndPurchasingGroup(String client, String purchasingGroup);

  List<PurchasingGroupEntity> findByClientInAndPurchasingGroupIn(List<String> client, List<String> purchasingGroup);

  List<PurchasingGroupEntity> findByClientAndPurchasingGroupIn(String client, List<String> purchasingGroup);

  List<PurchasingGroupEntity> findByClientAndPurchasingGroupAndSynchronizationState(String client, String purchasingGroup, String status);

  Page<PurchasingGroupEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<PurchasingGroupEntity> findAllByClient(String client, Pageable pageable);

  Page<PurchasingGroupEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
