package com.creactives.tam4.dataloader.dataproviders.database.logisticshandlinggroupdefinitions;


import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "logistics_handling_group_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TLOGT", description = "Descriptions for Logistics Handling Groups")
public class LogisticsHandlingGroupEntity implements ClientEnrichableEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "logistics_handling_group_staging_generator")
  @SequenceGenerator(name = "logistics_handling_group_staging_generator", sequenceName = "logistics_handling_group_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "logistics_handling_group")
  @FieldDoc(required = true, sapField = "LOGGR", description = "Logistics handling group for workload calculation")
  private String logisticsHandlingGroup;

  @Column(name = "language")
  @FieldDoc(required = true, sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column(name = "description")
  @FieldDoc(required = true, sapField = "LTEXT", description = "Description")
  private String description;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
