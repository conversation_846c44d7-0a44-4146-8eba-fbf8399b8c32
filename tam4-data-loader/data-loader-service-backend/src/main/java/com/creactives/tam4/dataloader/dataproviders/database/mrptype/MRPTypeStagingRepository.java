package com.creactives.tam4.dataloader.dataproviders.database.mrptype;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MRPTypeStagingRepository extends PagingAndSortingRepository<MRPTypeEntity, Long> {

  List<MRPTypeEntity> findByClientAndMrpType(String client, String mrpType);

 List<MRPTypeEntity> findByClientAndMrpTypeIn(String client, List<String> mrpTypes);

  Page<MRPTypeEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MRPTypeEntity> findAllByClient(String client, Pageable pageable);

  Page<MRPTypeEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
