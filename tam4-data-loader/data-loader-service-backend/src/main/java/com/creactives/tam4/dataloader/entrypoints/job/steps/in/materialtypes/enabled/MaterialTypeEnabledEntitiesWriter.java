package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialtypes.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class MaterialTypeEnabledEntitiesWriter implements ItemWriter<MaterialTypesEntity> {

  private final MaterialTypesStagingRepository materialTypesStagingRepository;

  @Override
  public void write(final List<? extends MaterialTypesEntity> list) throws Exception {
    for (final MaterialTypesEntity entity : list) {
      if (entity.getEnabled()) {
        materialTypesStagingRepository.setEnabled(true, entity.getMaterialType(), entity.getClient());
      }
    }
  }
}
