package com.creactives.tam4.dataloader.entrypoints.job.steps.in.businessaddress;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.businessadress.BusinessAddressEntity;
import com.creactives.tam4.dataloader.dataproviders.database.businessadress.BusinessAddressStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class BusinessAddressEntitiesWriter implements ItemWriter<BusinessAddressEntity> {

  private final BusinessAddressStagingRepository businessAddressStagingRepository;

  @Override
  public void write(final List<? extends BusinessAddressEntity> list) throws Exception {
    for (final BusinessAddressEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    businessAddressStagingRepository.saveAll(list);
  }
}
