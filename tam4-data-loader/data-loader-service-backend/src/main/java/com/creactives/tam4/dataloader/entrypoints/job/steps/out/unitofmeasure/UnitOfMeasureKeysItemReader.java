package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitsOfMeasureKeysStagingRepository;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@StepScope
public class UnitOfMeasureKeysItemReader extends RepositoryItemReader<UnitOfMeasureKeyEntity> {

  public UnitOfMeasureKeysItemReader(final UnitsOfMeasureKeysStagingRepository unitsOfMeasureKeysStagingRepository,
                                     @Value("#{jobParameters['clientCode']}") final String clientCode) {
    this.setRepository(unitsOfMeasureKeysStagingRepository);
    this.setPageSize(100);
    if (clientCode != null) {
      this.setMethodName("findAllByClient");
      this.setArguments(List.of(clientCode));
    } else {
      this.setMethodName("findAll");
    }

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC
    );
    this.setSort(sorts);
  }

}
