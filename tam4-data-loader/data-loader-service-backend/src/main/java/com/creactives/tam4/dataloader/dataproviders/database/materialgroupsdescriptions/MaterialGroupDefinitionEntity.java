package com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 5:01 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Table(name = "material_group_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T023T", description = "Material Group Descriptions")
public class MaterialGroupDefinitionEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_group_definitions_staging_generator")
  @SequenceGenerator(name = "material_group_definitions_staging_generator", sequenceName = "material_group_definitions_staging_seq", allocationSize = 1000)
  private long id;


  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_group")
  @FieldDoc(required = true, sapField = "MATKL", referenceTable = "T023", description = "Material Group")
  private String materialGroup;

  @Column(name = "description")
  @FieldDoc(required = true, sapField = "WGBEZ", description = "Material Group Description")
  private String description;

  @Column(name = "language")
  @FieldDoc(required = true, sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column(name = "long_description")
  @FieldDoc(sapField = "WGBEZ60", description = "Long text describing the material group")
  private String longDescription;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
