package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.MRPMaterialLevelUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MRPMaterialLevelWriter implements ItemWriter<MRPMaterialLevelUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

//  private final MRPMaterialLevelStagingRepository mrpMaterialLevelStagingRepository;

  public MRPMaterialLevelWriter(final WriteMessageService sendMessages, final MRPMaterialLevelStagingRepository mrpMaterialLevelStagingRepository) {
    this.sendMessages = sendMessages;

//    this.mrpMaterialLevelStagingRepository = mrpMaterialLevelStagingRepository;
  }


  @Override
  public void write(final List<? extends MRPMaterialLevelUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
