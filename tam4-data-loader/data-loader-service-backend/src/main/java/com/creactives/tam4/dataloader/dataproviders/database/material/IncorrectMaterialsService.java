package com.creactives.tam4.dataloader.dataproviders.database.material;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.GetTotalMaterialsInDatabase;
import com.creactives.tam4.dataloader.core.usecase.loadclient.GetTotalClientsInMaterialsInDatabase;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class IncorrectMaterialsService implements DetectIncorrectMaterialsInDatabase,
    GetTotalMaterialsInDatabase, GetTotalClientsInMaterialsInDatabase {

  @Autowired
  private final MaterialStagingRepository materialStagingRepository;

  private final JdbcTemplate jdbcTemplate;

  @Override
  public Long totalMaterials(final String client) {
    return materialStagingRepository.countByClient(client);
  }

  @Override
  public Long totalIgnoredMaterials(final String client, final boolean isIgnored) {
    return materialStagingRepository.countByClientAndIgnore(client, isIgnored);
  }

  @Override
  public Long totalAnalysedMaterials(final String client, final boolean isAnalysed) {
    return materialStagingRepository.countByClientAndSemanticallyAnalyzed(client, isAnalysed);
  }

  @Override
  public Long detectMaterialsWithNoPlants(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_plants " +
        " from (select m.material_code, count(distinct mp.material_code) mat_plants_count " +
        " from materials_data_staging m" +
        " left join materials_plant_data_staging mp on m.material_code = mp.material_code and m.client = mp.client" +
        " where m.ignore = false and m.client = '" + client + "'" +
        " group by m.material_code) as mcc" +
        " where mat_plants_count = 0 ", Long.class);
  }

  @Override
  public Long detectMaterialsWithNoOrdered(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_ordered " +
        " from (select m.material_code, count(distinct ordered.material_code) ordered_count " +
        " from materials_data_staging m " +
        " left join history_order_staging ordered on m.material_code = ordered.material_code " +
        " where m.ignore = false and m.client = '" + client + "'" +
        " group by m.material_code) as mcc " +
        " where ordered_count= 0 ", Long.class);
  }

  @Override
  public Long detectMaterialsWithNoConsumptions(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_consumptions " +
        " from (select m.material_code, count(distinct consumptions.material_code) consumptions_count " +
        " from materials_data_staging m " +
        " left join consumption_order_staging consumptions on m.material_code = consumptions.material_code " +
        " where m.ignore = false and m.client = '" + client + "'" +
        " group by m.material_code) as mcc " +
        " where consumptions_count = 0 ", Long.class);
  }

  @Override
  public Long detectMaterialsWithNoValuations(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_valuations " +
        " from (select m.material_code, count(distinct valuations.material_code) mat_valuations " +
        "  from materials_data_staging m " +
        "  left join plants_valuation_data_staging valuations on m.material_code = valuations.material_code " +
        "  where m.ignore = false and m.client = '" + client + "'" +
        "  group by m.material_code) as mcc " +
        " where mat_valuations = 0", Long.class);
  }

  @Override
  public Long detectMaterialsWithNoShortText(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_short_text " +
        " from (select m.material_code, count(distinct short_text.material) mat_short_texts " +
        " from materials_data_staging m " +
        " left join short_descriptions_staging short_text on m.material_code = short_text.material " +
        " where m.ignore = false and m.client = '" + client + "'" +
        " group by m.material_code) as mcc " +
        " where mat_short_texts = 0", Long.class);
  }

  @Override
  public Long detectMaterialsWithNoPODescriptions(final String client) {
    return jdbcTemplate.queryForObject("select count(*) materials_without_purchase_orders_descriptions " +
        " from (select m.material_code, count(distinct po_desc.material) mat_po_desc " +
        " from materials_data_staging m " +
        " left join long_descriptions_staging po_desc on m.material_code = po_desc.material " +
        " where m.ignore = false and m.client = '" + client + "'" +
        " group by m.material_code) as mcc " +
        " where mat_po_desc = 0", Long.class);
  }

  @Override
  public List<String> totalClients() {
    return jdbcTemplate.queryForList("select distinct m.client from materials_data_staging m",
        String.class);
  }
}
