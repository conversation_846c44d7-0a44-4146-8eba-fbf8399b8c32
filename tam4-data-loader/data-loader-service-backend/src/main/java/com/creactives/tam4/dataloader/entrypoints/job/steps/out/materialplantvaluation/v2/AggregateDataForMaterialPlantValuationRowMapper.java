package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.creactives.tam4.common.utils.TamStringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class AggregateDataForMaterialPlantValuationRowMapper implements RowMapper<AggregateDataForMaterialPlantValuation> {


  @Override
  public AggregateDataForMaterialPlantValuation mapRow(final ResultSet rs, final int rowNum) throws SQLException {
    return AggregateDataForMaterialPlantValuation.builder()
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .materialPlantValuation(mapMaterialPlantValuationRow(rs.getString("plants_valuation_data")))
        .poHistory(mapPOHistoryRow(rs.getString("history_order")))
        .consumptionData(mapConsumptionDataRow(rs.getString("consumption_order")))
        .plants(mapPlantRow(rs.getString("plant_definitions")))
        .plantCurrencies(mapPlantCurrencyRow(rs.getString("plant_currency")))
        .build();
  }


  private List<MaterialPlantValuationRow> mapMaterialPlantValuationRow(final String json) {
    return TamStringUtils.jsonToListOfObject(json, new TypeReference<>() {
    });
  }

  private List<POHistoryRow> mapPOHistoryRow(final String json) {

    return TamStringUtils.jsonToListOfObject(json, new TypeReference<>() {
    });
  }

  private List<ConsumptionDataRow> mapConsumptionDataRow(final String json) {

    return TamStringUtils.jsonToListOfObject(json, new TypeReference<>() {
    });
  }

  private List<PlantRow> mapPlantRow(final String json) {

    return TamStringUtils.jsonToListOfObject(json, new TypeReference<>() {
    });
  }

  private List<PlantCurrencyRow> mapPlantCurrencyRow(final String json) {

    return TamStringUtils.jsonToListOfObject(json, new TypeReference<>() {
    });
  }
}
