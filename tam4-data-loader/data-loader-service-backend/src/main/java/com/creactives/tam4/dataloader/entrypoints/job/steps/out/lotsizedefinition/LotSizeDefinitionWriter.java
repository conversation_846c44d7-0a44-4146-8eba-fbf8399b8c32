package com.creactives.tam4.dataloader.entrypoints.job.steps.out.lotsizedefinition;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.LotSizeDefinitionUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LotSizeDefinitionWriter implements ItemWriter<LotSizeDefinitionUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  public LotSizeDefinitionWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }

  @Override
  public void write(final List<? extends LotSizeDefinitionUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }

}
