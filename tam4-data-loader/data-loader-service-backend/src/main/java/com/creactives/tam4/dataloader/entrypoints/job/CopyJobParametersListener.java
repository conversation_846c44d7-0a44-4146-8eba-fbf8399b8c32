package com.creactives.tam4.dataloader.entrypoints.job;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.stereotype.Component;

@Component
public class CopyJobParametersListener implements StepExecutionListener {

  @Override
  public void beforeStep(final StepExecution stepExecution) {
    final ExecutionContext jobExecutionContext = stepExecution.getExecutionContext();
    final JobParameters jobParameters = stepExecution.getJobParameters();
    jobParameters.getParameters().forEach((s, jobParameter) -> jobExecutionContext.put(s, jobParameter.getValue()));
  }

  @Override
  public ExitStatus afterStep(final StepExecution stepExecution) {
    //do nothing
    return null; //null is ok
  }
}
