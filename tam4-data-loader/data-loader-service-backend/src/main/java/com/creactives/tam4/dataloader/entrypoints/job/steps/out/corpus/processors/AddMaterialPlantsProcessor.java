package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@RequiredArgsConstructor
@Component
public class AddMaterialPlantsProcessor implements ItemProcessor<Material, Material> {

  private final MaterialPlantStagingRepository materialPlantStagingRepository;

  //  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final List<MaterialPlantEntity> plantEntities = materialPlantStagingRepository
        .findByClientAndMaterialCode(item.getClient(),
            item.getMaterialCode());
    final List<String> plants = new ArrayList<>();
    if (plantEntities != null && !plantEntities.isEmpty()) {
      for (final MaterialPlantEntity plantEntity : plantEntities) {
        if (!plantEntity.isDeletionFlag()) {
          plants.add(plantEntity.getPlantId());
        }
      }
    }
    plants.sort(Comparator.naturalOrder());
    return Material.from(item).plants(plants).build();
  }
}
