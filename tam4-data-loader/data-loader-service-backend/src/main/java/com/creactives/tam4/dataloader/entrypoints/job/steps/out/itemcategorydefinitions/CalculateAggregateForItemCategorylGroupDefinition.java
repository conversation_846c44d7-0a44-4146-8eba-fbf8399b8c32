package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupsDefinitionsStagingRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Log4j2
@StepScope
public class CalculateAggregateForItemCategorylGroupDefinition implements ItemProcessor<ItemCategoryGroupDefinitionsKeyEntity, AggregateDataForItemCategoryGroupDefinition> {

  private final ItemCategoryGroupsDefinitionsStagingRepository itemCategoryGroupsDefinitionsStagingRepository;

  private final boolean ignoreStatus;

  public CalculateAggregateForItemCategorylGroupDefinition(final ItemCategoryGroupsDefinitionsStagingRepository itemCategoryGroupsDefinitionsStagingRepository,
                                                           @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.itemCategoryGroupsDefinitionsStagingRepository = itemCategoryGroupsDefinitionsStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  public AggregateDataForItemCategoryGroupDefinition process(final ItemCategoryGroupDefinitionsKeyEntity itemCategoryGroupDefinitionsKeyEntity) throws Exception {
    final List<ItemCategoryGroupDefinitionsEntity> entities = ignoreStatus ?
        itemCategoryGroupsDefinitionsStagingRepository.findByClientAndItemCategoryGroup(itemCategoryGroupDefinitionsKeyEntity.getClient(),
            itemCategoryGroupDefinitionsKeyEntity.getItemCategoryGroup())
        : itemCategoryGroupsDefinitionsStagingRepository.findByClientAndItemCategoryGroupAndSynchronizationState(itemCategoryGroupDefinitionsKeyEntity.getClient(),
        itemCategoryGroupDefinitionsKeyEntity.getItemCategoryGroup(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForItemCategoryGroupDefinition.builder()
        .itemCategoryGroupDefinitionsKeyEntity(itemCategoryGroupDefinitionsKeyEntity)
        .itemCategoryGroupEntities(entities)
        .build();
  }

}
