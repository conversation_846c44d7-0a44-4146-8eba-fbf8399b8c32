package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MaterialCreatedConstants {

  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES = "SendMaterialCreationMessages::step";
  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES_PARTITIONER = STEP_SEND_MATERIAL_CREATION_MESSAGES + "::partitioner";
  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES_READER = STEP_SEND_MATERIAL_CREATION_MESSAGES + "::reader";
  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES_PROCESSOR = STEP_SEND_MATERIAL_CREATION_MESSAGES + "::processor";
  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES_WRITER = STEP_SEND_MATERIAL_CREATION_MESSAGES + "::writer";
  public static final String STEP_SEND_MATERIAL_CREATION_MESSAGES_CACHELISTENER = STEP_SEND_MATERIAL_CREATION_MESSAGES + "::cacheListener";
  public static final boolean STRING_KEY = false;


  public static final String PARTITIONER_BASE_QUERY = "SELECT" +
      "    mds.id partitionKey" +
      "  FROM materials_data_staging mds" +
      "  where 1=1 ";

  public static final String CHUNK_BASE_QUERY = "SELECT" +
      "    mds.*," +
      "    array_agg(distinct sds.language || '~!_~' || sds.description) AS short_descriptions," +
      "    array_agg(distinct lds.language || '~!_~' || lds.type || '~!_~' || lds.long_description) AS long_descriptions," +
      "    array_agg(distinct ums.alternative_unit_of_measurement || '~!_~' || ums.denominator::text || '~!_~' || ums.numerator) AS unit_of_measure," +
      "    array_agg(distinct cs.description || '~!_~' || cs.value) AS characteristics" +
      "  FROM materials_data_staging mds" +
      "    LEFT JOIN short_descriptions_staging sds ON mds.client = sds.client AND mds.material_code = sds.material and sds.description is not null" +
      "    LEFT JOIN long_descriptions_staging lds on mds.client = lds.client AND mds.material_code = lds.material and lds.long_description is not null and lds.long_description <> ''" +
      "    LEFT JOIN units_of_measure_staging ums on mds.client = ums.client AND mds.material_code = ums.material_code" +
      "    LEFT JOIN characteristics_staging cs on mds.client = cs.client AND mds.material_code = cs.material_code " +
      "    where 1=1 ";

  public static final String PARTITIONER_ORDER_BY = " ORDER BY mds.id";
  public static final String CHUNK_GROUP_AN_ORDER_BY = "  GROUP BY mds.id ORDER BY mds.id";

  public static String buildConditionPartitioner(final String clientCode, final String ignoreStatus, final boolean namedParameterQuery) {
    final StringBuilder condition = new StringBuilder(" and mds.ignore is false ");
    if (clientCode != null) {
      condition.append(" and mds.client = ").append(namedParameterQuery ? ":clientCode" : '?');
    }

    if (namedParameterQuery) {
      DataLoaderUtils.appendStatusNamedParameters(ignoreStatus, condition, "mds");
    } else {
      DataLoaderUtils.appendStatusParams(ignoreStatus, condition, "mds");
    }

    return condition.toString();
  }

  public static String buildConditionChunk(final String clientCode, final String ignoreStatus, final boolean namedParameterQuery) {
    final StringBuilder condition = new StringBuilder(buildConditionPartitioner(clientCode, ignoreStatus, namedParameterQuery));
    if (namedParameterQuery) {
      condition.append(" and mds.id >= :firstIndex ");
      condition.append(" and mds.id <= :lastIndex ");
    } else {
      condition.append(" and mds.id >= ? ");
      condition.append(" and mds.id <= ? ");
    }
    return condition.toString();
  }

  public static String getPartitionerQuery(final String clientCode, final String ignoreStatus, final boolean namedParameterQuery) {
    final String query = PARTITIONER_BASE_QUERY +
        buildConditionPartitioner(clientCode, ignoreStatus, namedParameterQuery) +
        PARTITIONER_ORDER_BY;

    return query;
  }

  public static String getChunkQuery(final String clientCode, final String ignoreStatus) {
    final String query = CHUNK_BASE_QUERY +
        buildConditionChunk(clientCode, ignoreStatus, false) +
        CHUNK_GROUP_AN_ORDER_BY;

    return query;
  }
}
