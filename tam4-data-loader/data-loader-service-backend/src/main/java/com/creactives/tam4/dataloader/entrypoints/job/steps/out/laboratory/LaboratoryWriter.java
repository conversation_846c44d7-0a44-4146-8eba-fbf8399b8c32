package com.creactives.tam4.dataloader.entrypoints.job.steps.out.laboratory;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.laboratory.LaboratoryStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.LaboratoryUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Service
public class LaboratoryWriter implements ItemWriter<LaboratoryUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends LaboratoryUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
