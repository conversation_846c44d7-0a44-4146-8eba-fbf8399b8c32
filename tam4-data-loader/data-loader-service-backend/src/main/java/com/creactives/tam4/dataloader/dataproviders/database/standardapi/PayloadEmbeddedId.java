package com.creactives.tam4.dataloader.dataproviders.database.standardapi;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class PayloadEmbeddedId implements Serializable {

  @Column(name = "message_type")
  private String messageType;

  @Column(name = "record_key")
  private String key;

  @Column(name = "payload_hash")
  private String hash;

  public String extractPayloadKey() {
    return buildPayloadKey(this.getMessageType(), this.getKey(), this.getHash());
  }

  public static String buildPayloadKey(final String messageType, final String key, final String hash ){
    return messageType + '-' + key + '-' + hash;
  }
}
