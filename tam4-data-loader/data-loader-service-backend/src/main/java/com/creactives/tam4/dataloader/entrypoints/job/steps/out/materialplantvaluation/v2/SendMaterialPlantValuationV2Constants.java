package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import lombok.experimental.UtilityClass;

@UtilityClass
public class SendMaterialPlantValuationV2Constants {


  public static final String JOB_SEND_MATERIAL_PLANT_VALUATION = "SendMaterialPlantValuation::jon";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION = "SendMaterialPlantValuation::step";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION_PARTITIONER = STEP_SEND_MATERIAL_PLANT_VALUATION + "::partitioner";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION_READER = STEP_SEND_MATERIAL_PLANT_VALUATION + "::reader";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION_PROCESSOR = STEP_SEND_MATERIAL_PLANT_VALUATION + "::processor";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION_WRITER = STEP_SEND_MATERIAL_PLANT_VALUATION + "::writer";
  public static final String STEP_SEND_MATERIAL_PLANT_VALUATION_WRITELISTENER = STEP_SEND_MATERIAL_PLANT_VALUATION + "::writeListener";

  public static final String PARTITIONER_BASE_QUERY = "select distinct partitionKey from ( " +
      " select pvds.client || '/' || pvds.material_code partitionKey " +
      " from plants_valuation_data_staging pvds " +
      " where 1=1 ";

  public static final String CHUNK_BASE_QUERY = "select partitionKey, client, material_code, synchronization_state, " +
      " plants_valuation_data, " +
      " history_order, " +
      " consumption_order, " +
      " plant_definitions, " +
      " plant_currency " +
      " from ( " +
      " select  " +
      " mpvd.client, " +
      " mpvd.material_code,  " +
      " mpvd.client || '/' || mpvd.material_code partitionKey,  " +
      " mpvd.synchronization_state, " +
      " jsonb_agg(distinct mpvd) plants_valuation_data, " +
      " coalesce(jsonb_agg(distinct ho) FILTER (WHERE ho.id IS NOT NULL), '[]'::jsonb) history_order, " +
      " coalesce(jsonb_agg(distinct mcos) FILTER (WHERE mcos.id IS NOT NULL), '[]'::jsonb) consumption_order, " +
      " coalesce(jsonb_agg(distinct pd) FILTER (WHERE pd.id IS NOT NULL), '[]'::jsonb) plant_definitions, " +
      " coalesce(jsonb_agg(distinct pc) FILTER (WHERE pc.id IS NOT NULL), '[]'::jsonb) plant_currency " +
      " from plants_valuation_data_staging mpvd  " +
      " left join history_order_staging ho on ho.client = mpvd.client and ho.material_code = mpvd.material_code and ho.plant_code = mpvd.plant_id  " +
      " left join consumption_order_staging mcos on mcos.client = mpvd.client and mcos.material_code = mpvd.material_code and mcos.plant_code = mpvd.plant_id  " +
      " LEFT JOIN plant_definitions_staging pd ON mpvd.client = pd.client AND mpvd.plant_id = pd.plant  " +
      " LEFT JOIN plant_currency pc on mpvd.client = pc.client AND mpvd.plant_id = pc.plant " +
      " group by mpvd.client, " +
      " mpvd.material_code, mpvd.synchronization_state " +
      " order by mpvd.client, " +
      " mpvd.material_code " +
      " ) pvds " +
      " where 1=1";

  public static final String PARTITIONER_ORDER_BY = " order by pvds.client, pvds.material_code ) outerq order by partitionKey";
  public static final String CHUNK_GROUP_AN_ORDER_BY = " order by pvds.client, pvds.material_code";

  public static String getPartitionerQuery(final String clientCode, final String ignoreStatus, final boolean namedParameterQuery) {
    final String query = PARTITIONER_BASE_QUERY +
        DataLoaderUtils.buildConditionPartitioner(clientCode, ignoreStatus, "pvds", namedParameterQuery) +
        PARTITIONER_ORDER_BY;

    return query;
  }

  public static String getChunkQuery(final String clientCode, final String ignoreStatus) {
    final String query = CHUNK_BASE_QUERY +
        DataLoaderUtils.buildConditionChunk(clientCode, ignoreStatus, "pvds", false) +
        CHUNK_GROUP_AN_ORDER_BY;

    return query;
  }


}
