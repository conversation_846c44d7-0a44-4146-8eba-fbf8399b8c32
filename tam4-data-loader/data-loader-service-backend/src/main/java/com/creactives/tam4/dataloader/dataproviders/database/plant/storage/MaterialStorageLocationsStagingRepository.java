package com.creactives.tam4.dataloader.dataproviders.database.plant.storage;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created on 6/19/2019 11:24 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface MaterialStorageLocationsStagingRepository extends PagingAndSortingRepository<MaterialStorageLocationEntity, Long> {

  List<MaterialStorageLocationEntity> findByClientAndMaterialCode(String client, String materialCode);

  List<MaterialStorageLocationEntity> findByClientAndMaterialCodeAndStorageLocationIn(String client, String materialCode, List<String> storageLocation);

  List<MaterialStorageLocationEntity> findAllByIdInAndSynchronizationState(List<Long> ids, String status);

}
