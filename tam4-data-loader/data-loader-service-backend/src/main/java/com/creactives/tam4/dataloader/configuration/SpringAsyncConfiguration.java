package com.creactives.tam4.dataloader.configuration;

import com.creactives.tam4.common.bean.Tam4AsyncConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.task.TaskExecutionProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;

@Configuration
@Log4j2
@EnableConfigurationProperties({ExportConfiguration.class, IncludeConfiguration.class})
public class SpringAsyncConfiguration {


  @Bean
  public Tam4AsyncConfig taskAsyncConfig(final TaskExecutionProperties taskExecutionProperties) {
    return new Tam4AsyncConfig(taskExecutionProperties);
  }

  @Bean
  public TaskExecutor taskExecutor(final Tam4AsyncConfig tam4AsyncConfig) {
    return (TaskExecutor) tam4AsyncConfig.getAsyncExecutor();
  }


}
