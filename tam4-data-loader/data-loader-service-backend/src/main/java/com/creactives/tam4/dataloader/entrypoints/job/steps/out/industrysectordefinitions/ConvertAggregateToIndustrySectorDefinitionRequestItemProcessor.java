package com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorDefinitionEntity;
import com.creactives.tam4.messaging.materials.commands.IndustrySectorDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Log4j2
@Component
public class ConvertAggregateToIndustrySectorDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForIndustrySectorDefinition, IndustrySectorDefinitionUpsertRequestMessage> {

  @Override
  public IndustrySectorDefinitionUpsertRequestMessage process(final AggregateDataForIndustrySectorDefinition aggregateDataForIndustrySectorDefinition) throws Exception {
    return IndustrySectorDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForIndustrySectorDefinition.getIndustrySectorEntity().getClient())
        .industrySector(aggregateDataForIndustrySectorDefinition.getIndustrySectorEntity().getIndustrySector())
        .descriptions(aggregateDataForIndustrySectorDefinition.getIndustrySectorEntities().stream()
            .filter(industrySectorEntity -> industrySectorEntity.getLanguage() != null)
            .collect(Collectors.toMap(IndustrySectorDefinitionEntity::getLanguage,
                IndustrySectorDefinitionEntity::getDescription
            ))
        )
        .enabled(aggregateDataForIndustrySectorDefinition.getIndustrySectorEntity().getEnabled())
        .build();
  }
}
