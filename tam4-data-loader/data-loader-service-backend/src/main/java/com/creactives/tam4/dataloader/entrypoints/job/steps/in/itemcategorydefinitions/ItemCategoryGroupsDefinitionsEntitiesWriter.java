package com.creactives.tam4.dataloader.entrypoints.job.steps.in.itemcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupsDefinitionsKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupsDefinitionsStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class ItemCategoryGroupsDefinitionsEntitiesWriter implements ItemWriter<ItemCategoryGroupDefinitionsEntity> {

  private final ItemCategoryGroupsDefinitionsKeysStagingRepository itemCategoryGroupsDefinitionsKeysStagingRepository;
  private final ItemCategoryGroupsDefinitionsStagingRepository itemCategoryGroupsDefinitionsStagingRepository;

  @Override
  public void write(final List<? extends ItemCategoryGroupDefinitionsEntity> list) throws Exception {
    for (final ItemCategoryGroupDefinitionsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    itemCategoryGroupsDefinitionsStagingRepository.saveAll(list);
    for (final ItemCategoryGroupDefinitionsEntity item : list) {
      if (itemCategoryGroupsDefinitionsKeysStagingRepository.findByClientAndItemCategoryGroup(item.getClient(), item.getItemCategoryGroup()).isEmpty()) {
        itemCategoryGroupsDefinitionsKeysStagingRepository.save(ItemCategoryGroupDefinitionsKeyEntity.builder()
            .client(item.getClient())
            .itemCategoryGroup(item.getItemCategoryGroup())
            .enabled(true)
            .build());
      }
    }
  }
}
