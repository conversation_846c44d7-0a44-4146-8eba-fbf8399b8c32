package com.creactives.tam4.dataloader.dataproviders.database.material;

import com.creactives.tam4.dataloader.configuration.ExportConfiguration;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created on 6/12/2019 6:31 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
public class MaterialRepositoryItemReader extends RepositoryItemReader<MaterialEntity> {

  public MaterialRepositoryItemReader(final ExportConfiguration exportConfiguration,
                                      final MaterialStagingRepository materialStagingRepository) {
    this.setRepository(materialStagingRepository);
    this.setPageSize(exportConfiguration.getPageSize());
    this.setMethodName("findAllByIgnore");
    this.setArguments(List.of(false));

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC);
    this.setSort(sorts);
  }

}
