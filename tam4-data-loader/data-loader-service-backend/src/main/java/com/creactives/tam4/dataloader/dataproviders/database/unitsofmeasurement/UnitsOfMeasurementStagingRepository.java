package com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitsOfMeasurementStagingRepository extends PagingAndSortingRepository<UnitsOfMeasurementEntity, Long> {

  List<UnitsOfMeasurementEntity> findByClientAndUnitsOfMeasurement(String client, String unitsOfMeasurement);

}
