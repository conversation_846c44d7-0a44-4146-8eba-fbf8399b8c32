package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.core.entity.BigDecimalConverter;
import com.creactives.tam4.dataloader.core.entity.LongDescriptionCodes;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription.LongDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.uma.AlternativeUnitsOfMeasureEntity;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.google.common.collect.ArrayListMultimap;
import lombok.Setter;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Component(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES_READER)
@StepScope
@Setter
public class OptimizedMaterialReader extends JdbcCursorItemReader<AggregateDataForMaterialCreated> {

  @Value("#{jobParameters['clientCode']}")
  private String clientCode;

  @Value("#{jobParameters['ignore_status']}")
  private String ignoreStatus;

  //  @Value("#{stepExecution}")
  private StepExecution stepExecution;

  public OptimizedMaterialReader(final DataSource dataSource,
                                 @Value("${data-loader.send-job.materials-chunk-size:1000}") final int fetchSize,
                                 @Value("#{jobParameters['clientCode']}") final String clientCode,
                                 @Value("#{jobParameters['ignore_status']}") final String ignoreStatus,
                                 @Value("#{stepExecution}") final StepExecution stepExecution) {

    this.stepExecution = stepExecution;
    setDataSource(dataSource);
    setFetchSize(fetchSize);
    setRowMapper((rs, rowNum) -> map(rs));
    setSql(MaterialCreatedConstants.getChunkQuery(clientCode, ignoreStatus));
    final PreparedStatementSetter preparedStatementSetter = DataLoaderUtils.initPrepareStatementSetter(clientCode, ignoreStatus, stepExecution, MaterialCreatedConstants.STRING_KEY);
    setPreparedStatementSetter(preparedStatementSetter);
  }


  private AggregateDataForMaterialCreated map(final ResultSet rs) throws SQLException {
    final List<LongDescriptionEntity> allLongDescriptionEntities = readLongDescriptions(rs);
    final List<LongDescriptionEntity> longDescriptionsEntities = new ArrayList<>();
    final List<LongDescriptionEntity> longDescriptionsPOEntities = new ArrayList<>();
    final List<LongDescriptionEntity> longDescriptionsInternalNoteEntities = new ArrayList<>();
    final List<LongDescriptionEntity> longDescriptionsInspectionEntities = new ArrayList<>();
    if (!allLongDescriptionEntities.isEmpty()) {
      for (final LongDescriptionEntity longDescriptionEntity : allLongDescriptionEntities) {
        if (longDescriptionEntity.getType().equals(LongDescriptionCodes.BEST.name())) {
          longDescriptionsPOEntities.add(longDescriptionEntity);
        } else if (longDescriptionEntity.getType().equals(LongDescriptionCodes.GRUN.name())) {
          longDescriptionsEntities.add(longDescriptionEntity);
        } else if (longDescriptionEntity.getType().equals(LongDescriptionCodes.IVER.name())) {
          longDescriptionsInternalNoteEntities.add(longDescriptionEntity);
        } else if (longDescriptionEntity.getType().equals(LongDescriptionCodes.PRUE.name())) {
          longDescriptionsInspectionEntities.add(longDescriptionEntity);
        }
      }
    }
    return AggregateDataForMaterialCreated.builder()
        .materialEntity(readMaterial(rs))
        .alternativeUnitsOfMeasureEntities(readAlternativeUnitsOfMeasure(rs))
        .shortDescriptionEntities(readShortDescriptions(rs))
        .poDescriptionEntities(longDescriptionsPOEntities)
        .longDescriptionEntities(longDescriptionsEntities)
        .inspectionDescriptionEntities(longDescriptionsInspectionEntities)
        .internalNoteDescriptionEntities(longDescriptionsInternalNoteEntities)
        .characteristics(readCharacteristics(rs))
        .build();
  }

  private ArrayListMultimap<String, String> readCharacteristics(final ResultSet rs) throws SQLException {
    final Array descriptions = rs.getArray("characteristics");
    final String[] array = (String[]) descriptions.getArray();
    final ArrayListMultimap<String, String> out = ArrayListMultimap.create();
    if (array != null) {
      for (final String descriptionEncoded : array) {
        if (descriptionEncoded != null) {
          final String[] split = descriptionEncoded.split("\\~!_~");
          final String key = split[0];
          final String value = split[1];
          out.put(key, value);
        }
      }
    }
    return out;
  }

  private List<AlternativeUnitsOfMeasureEntity> readAlternativeUnitsOfMeasure(final ResultSet rs) throws SQLException {
    final Array descriptions = rs.getArray("unit_of_measure");
    final String[] array = (String[]) descriptions.getArray();
    final List<AlternativeUnitsOfMeasureEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String descriptionEncoded : array) {
        if (descriptionEncoded != null) {
          final String[] split = descriptionEncoded.split("\\~!_~");
          out.add(AlternativeUnitsOfMeasureEntity.builder()
              .alternativeUnitOfMeasurement(split[0])
              .denominator(BigDecimalConverter.toBigDecimal(split[1]))
              .numerator(BigDecimalConverter.toBigDecimal(split[2]))
              .build());
        }
      }
    }
    return out;
  }

  private List<ShortDescriptionEntity> readShortDescriptions(final ResultSet rs) throws SQLException {
    final Array descriptions = rs.getArray("short_descriptions");
    final String[] array = (String[]) descriptions.getArray();
    final List<ShortDescriptionEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String descriptionEncoded : array) {
        if (descriptionEncoded != null) {
          final String[] split = descriptionEncoded.split("\\~!_~");
          final String language = split[0];
          final String description = split[1];
          out.add(ShortDescriptionEntity.builder()
              .description(description)
              .language(language)
              .build());
        }
      }
    }
    return out;
  }

  private List<LongDescriptionEntity> readLongDescriptions(final ResultSet rs) throws SQLException {
    final Array descriptions = rs.getArray("long_descriptions");
    final String[] array = (String[]) descriptions.getArray();
    final List<LongDescriptionEntity> out = new ArrayList<>();
    if (array != null) {
      for (final String descriptionEncoded : array) {
        if (descriptionEncoded != null) {
//          final int i = descriptionEncoded.indexOf(':');
          final String[] split = descriptionEncoded.split("\\~!_~");
          final String language = split[0];
          final String description = split[2];
          final String type = split[1];
          out.add(LongDescriptionEntity.builder()
              .longDescription(description)
              .language(language)
              .type(type)
              .build());
        }
      }
    }
    return out;
  }


  private MaterialEntity readMaterial(final ResultSet rs) throws SQLException {
    return MaterialEntity.builder()
        .id(rs.getLong("id"))
        .client(rs.getString("client"))
        .materialCode(rs.getString("material_code"))
        .deletionFlag(rs.getBoolean("deletion_flag"))
        .materialType(rs.getString("material_type"))
        .industrySector(rs.getString("industry_sector"))
        .materialGroup(rs.getString("material_group"))
        .oldMaterialNumber(rs.getString("old_material_number"))
        .baseUnitOfMeasurement(rs.getString("base_unit_of_measurement"))
        .productDivision(rs.getString("product_division"))
        .authorizationGroup(rs.getString("authorization_group"))
        .crossPlantMaterialStatus(rs.getString("cross_plant_material_status"))
        .materialStatusValidFromDate(rs.getLong("material_status_valid_from_date"))
        .manufacturerPartNumber(rs.getString("manufacturer_part_number"))
        .manufacturerCode(rs.getString("manufacturer_code"))
        .documentNumber(rs.getString("document_number"))
        .basicMaterial(rs.getString("basic_material"))
        .genericItemGroup(rs.getString("generic_item_group"))
        .revisionNumber(rs.getString("revision_number"))
        .purchasingMeasurementUnits(rs.getString("purchasing_measurement_units"))
        .materialCreatedOn(rs.getString("material_created_on"))
        .externalMaterialGroup(rs.getString("external_material_group"))
        .weightUnit(rs.getString("weight_unit"))
        .netWeight(rs.getBigDecimal("net_weight"))
        .grossWeight(rs.getBigDecimal("gross_weight"))
        .sizeDimension(rs.getString("size_dimension"))
        .hazardousMaterialNumber(rs.getString("hazardous_material_number"))
        .ignore(rs.getBoolean("ignore"))
        .createdOn(rs.getTimestamp("created_on"))
        .lastModifiedOn(rs.getTimestamp("last_modified_on"))
        .synchronizedOn(rs.getTimestamp("synchronized_on"))
        .synchronizationConfirmedOn(rs.getTimestamp("synchronization_confirmed_on"))
        .synchronizationState(rs.getString("synchronization_state"))
        .semanticallyAnalyzed(rs.getBoolean("semantically_analyzed"))
        .mdDomain(rs.getString("md_domain"))

        ////FIXME: Custom fields required by A2A T4-1756
        .famiglia(rs.getString("famiglia"))
        .sottoFamiglia(rs.getString("sotto_famiglia"))
        .specificaTecnica(rs.getString("specifica_tecnica"))
        .edizione(rs.getString("edizione"))
        .revisione(rs.getString("revisione"))
        .dataCustom(rs.getLong("data_custom"))

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(rs.getString("product_hierarchy"))

        .volume(BigDecimal.valueOf(rs.getLong("volume")))
        .volumeUnit(rs.getString("volume_unit"))
        .internationalArticleNumberEanUpc(rs.getString("internation_article_number"))
        .serviceValuationClass(rs.getString("service_valuation_class"))
        .build();
  }


}
