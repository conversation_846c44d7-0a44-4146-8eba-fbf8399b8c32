package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitofmeasure;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.UnitOfMeasureDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class SendUnitOfMeasureDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForUniOfMeasureDefinition calculateAggregateForUniOfMeasureDefinition;
  private final ConvertAggregateToUnitOfMeasureDefinitionRequestItemProcessor convertAggregateToUnitOfMeasureDefinitionRequestItemProcessor;
  private final UnitOfMeasureDefinitionWriter unitOfMeasureDefinitionWriter;
  private final UnitOfMeasureKeysItemReader unitOfMeasureKeysItemReader;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-unit-of-measure-definition-add-or-update")
        .<UnitOfMeasureKeyEntity, UnitOfMeasureDefinitionUpsertRequestMessage>chunk(1000)
        .reader(unitOfMeasureKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<UnitOfMeasureKeyEntity, UnitOfMeasureDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForUniOfMeasureDefinition,
                convertAggregateToUnitOfMeasureDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<UnitOfMeasureDefinitionUpsertRequestMessage>().delegates(List.of(unitOfMeasureDefinitionWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<UnitOfMeasureDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "unit_of_measure_definitions_staging", "unit_of_measure",
        (unitOfMeasureDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, unitOfMeasureDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, unitOfMeasureDefinitionUpsertRequestMessage.getUnitOfMeasure());
        });
  }
}
