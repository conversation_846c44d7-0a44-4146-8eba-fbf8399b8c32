package com.creactives.tam4.dataloader.entrypoints.job.steps.out.industrysectordefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.IndustrySectorDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Log4j2
@Component
public class SendIndustrySectorDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForIndustrySectorDefinition calculateAggregateForIndustrySectorDefinition;
  private final ConvertAggregateToIndustrySectorDefinitionRequestItemProcessor convertAggregateToIndustrySectorDefinitionRequestItemProcessor;
  private final IndustrySectorDefinitionWriter industrySectorDefinitionWriter;
  private final IndustrySectorDefinitionKeysItemReader industrySectorDefinitionKeysItemReader;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-industry-sector-definition-add-or-update")
        .<IndustrySectorEntity, IndustrySectorDefinitionUpsertRequestMessage>chunk(1000)
        .reader(industrySectorDefinitionKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<IndustrySectorEntity, IndustrySectorDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForIndustrySectorDefinition,
                convertAggregateToIndustrySectorDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<IndustrySectorDefinitionUpsertRequestMessage>().delegates(List.of(industrySectorDefinitionWriter,
                updateStagingTable()
            )).build()
        )
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<IndustrySectorDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "industry_sector_definitions_staging", "industry_sector",
        (industrySectorDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, industrySectorDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, industrySectorDefinitionUpsertRequestMessage.getIndustrySector());
        });
  }
}
