package com.creactives.tam4.dataloader.dataproviders.database.supplier;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Created on 6/11/2019 4:14 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Repository
public interface SupplierStagingRepository extends PagingAndSortingRepository<SupplierEntity, Long> {

  Optional<SupplierEntity> findByClientAndCode(String client, String vatRegistrationNumber);

  List<SupplierEntity> findByClientAndCodeIn(String client, List<String> codes);

  Optional<SupplierEntity> findByClientAndCodeAndSynchronizationState(String client, String vatRegistrationNumber, String status);

  Page<SupplierEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<SupplierEntity> findAllByClient(String client, Pageable pageable);

  Page<SupplierEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
