package com.creactives.tam4.dataloader.dataproviders.database.descriptions.longdescription;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface LongDescriptionStagingRepository extends CrudRepository<LongDescriptionEntity, Long> {

  List<LongDescriptionEntity> findByClientAndLanguageAndMaterialIn(String client, String language, List<String> materials);

  List<LongDescriptionEntity> findByClientAndMaterialAndType(String client, String material, String type);

  @Query(value = "UPDATE LongDescriptionEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE client = :client AND synchronizationState = :stateOld AND material in (:codes)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("client") String client,
                   @Param("stateOld") String stateOld,
                   @Param("codes") List<String> codes);

}
