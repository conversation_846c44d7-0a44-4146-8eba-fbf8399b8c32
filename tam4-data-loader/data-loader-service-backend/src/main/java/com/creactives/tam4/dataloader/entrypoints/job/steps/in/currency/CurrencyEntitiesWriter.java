package com.creactives.tam4.dataloader.entrypoints.job.steps.in.currency;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.currency.CurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.currency.CurrencyStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class CurrencyEntitiesWriter implements ItemWriter<CurrencyEntity> {

  private final CurrencyStagingRepository currencyStagingRepository;

  @Override
  public void write(final List<? extends CurrencyEntity> list) throws Exception {
    for (final CurrencyEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    currencyStagingRepository.saveAll(list);
  }
}
