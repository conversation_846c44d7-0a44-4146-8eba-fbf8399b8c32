package com.creactives.tam4.dataloader.entrypoints.job.steps.in.marginkey;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginEntity;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginKeyStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class SchedulingMarginKeyEntitiesWriter implements ItemWriter<SchedulingMarginEntity> {

  private final SchedulingMarginKeyStagingRepository stagingRepository;

  @Override
  public void write(final List<? extends SchedulingMarginEntity> list) throws Exception {
    for (final SchedulingMarginEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    stagingRepository.saveAll(list);
  }
}
