package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * Created on 6/12/2019 7:19 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class CalculateAggregateForMaterialCreated implements ItemProcessor<MaterialEntity, AggregateDataForMaterialCreated> {

  private final ShortDescriptionStagingRepository shortDescriptionStagingRepository;

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForMaterialCreated process(final MaterialEntity materialEntity) {
    final List<ShortDescriptionEntity> shortDescriptionEntities = shortDescriptionStagingRepository.findByClientAndMaterial(materialEntity.getClient(), materialEntity.getMaterialCode());
    //le short sono già scritte nel OptimizedMaterialReader.map ma di qui no passiamo
    return AggregateDataForMaterialCreated.builder()
        .materialEntity(materialEntity)
        .shortDescriptionEntities(shortDescriptionEntities)
        .poDescriptionEntities(Collections.emptyList())
        .longDescriptionEntities(Collections.emptyList())
        .internalNoteDescriptionEntities(Collections.emptyList())
        .inspectionDescriptionEntities(Collections.emptyList())
        .alternativeUnitsOfMeasureEntities(Collections.emptyList())
        .build();
  }

}
