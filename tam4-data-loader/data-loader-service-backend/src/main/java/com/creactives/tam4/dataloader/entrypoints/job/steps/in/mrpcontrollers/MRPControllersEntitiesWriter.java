package com.creactives.tam4.dataloader.entrypoints.job.steps.in.mrpcontrollers;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers.MRPControllerStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers.MRPControllersEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class MRPControllersEntitiesWriter implements ItemWriter<MRPControllersEntity> {

  private final MRPControllerStagingRepository mrpControllerStagingRepository;

  @Override
  public void write(final List<? extends MRPControllersEntity> list) throws Exception {
    for (final MRPControllersEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    mrpControllerStagingRepository.saveAll(list);
  }
}
