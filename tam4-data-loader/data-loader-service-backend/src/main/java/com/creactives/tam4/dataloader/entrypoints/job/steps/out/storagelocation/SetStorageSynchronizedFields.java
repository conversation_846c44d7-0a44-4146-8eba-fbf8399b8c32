package com.creactives.tam4.dataloader.entrypoints.job.steps.out.storagelocation;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
@RequiredArgsConstructor
@Service
public class SetStorageSynchronizedFields implements ItemProcessor<StorageLocationEntity, StorageLocationEntity> {

  private final StorageLocationStagingRepository repository;

  @Override
  @Transactional
  public StorageLocationEntity process(final StorageLocationEntity storageLocationEntity) throws Exception {

    final List<StorageLocationEntity> storageLocation = repository.findAllByClientAndStorageLocationAndSynchronizationState(storageLocationEntity.getClient(),
        storageLocationEntity.getStorageLocation(),
        SyncStatus.PENDING.getCode());
    storageLocation.parallelStream().filter(Objects::nonNull).forEach(entity -> {
      entity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
      entity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
    });
    return storageLocationEntity;
  }
}
