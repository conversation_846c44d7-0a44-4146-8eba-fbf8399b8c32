package com.creactives.tam4.dataloader.entrypoints.job.steps.in.purchasingdocumentitem;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentitem.PurchasingDocumentItemEntity;
import com.creactives.tam4.dataloader.dataproviders.database.purchasingdocumentitem.PurchasingDocumentItemStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class PurchasingDocumentItemEntitiesWriter implements ItemWriter<PurchasingDocumentItemEntity> {

  private final PurchasingDocumentItemStagingRepository purchasingDocumentItemStagingRepository;

  @Override
  public void write(final List<? extends PurchasingDocumentItemEntity> list) throws Exception {
    for (final PurchasingDocumentItemEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    purchasingDocumentItemStagingRepository.saveAll(list);
  }
}
