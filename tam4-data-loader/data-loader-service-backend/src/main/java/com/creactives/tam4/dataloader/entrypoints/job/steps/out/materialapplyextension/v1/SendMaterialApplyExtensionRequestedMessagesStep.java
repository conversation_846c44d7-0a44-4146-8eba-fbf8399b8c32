package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1;

import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

/**
 * Created on 6/12/2019 5:52 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class SendMaterialApplyExtensionRequestedMessagesStep implements StepConfigurer {

  private static final String SEND_MATERIAL_EXTENDED = "send-material-extended";
  private final StepBuilderFactory stepBuilderFactory;
  private final MaterialPlantItemProcessor itemProcessor;
  private final MaterialApplyExtensionRequestProvider itemWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final OptimizedMaterialExtensionReader optimizedMaterialExtensionReader;

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get(SEND_MATERIAL_EXTENDED)
        .<AggregateDataForMaterialExtension, MaterialApplyExtensionLoadMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(optimizedMaterialExtensionReader)
        .processor(itemProcessor)
        .writer(itemWriter)
        .listener(new StatisticsListener())
        .build();
  }
//FIXME: GBRRRT - Add table update!
}
