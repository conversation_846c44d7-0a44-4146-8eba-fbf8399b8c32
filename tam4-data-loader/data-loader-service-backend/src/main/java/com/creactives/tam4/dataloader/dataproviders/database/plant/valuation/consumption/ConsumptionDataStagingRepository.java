package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface ConsumptionDataStagingRepository extends CrudRepository<ConsumptionDataEntity, Long> {

  List<ConsumptionDataEntity> findAllByClientAndMaterialCode(String client, String materialCode);

  List<ConsumptionDataEntity> findAllByClientAndMaterialCodeIn(String client, List<String> materialCode);

  @Query(value = "UPDATE ConsumptionDataEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE synchronizationState = :stateOld AND id in (:ids)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("stateOld") String stateOld,
                   @Param("ids") List<Long> ids);
}
