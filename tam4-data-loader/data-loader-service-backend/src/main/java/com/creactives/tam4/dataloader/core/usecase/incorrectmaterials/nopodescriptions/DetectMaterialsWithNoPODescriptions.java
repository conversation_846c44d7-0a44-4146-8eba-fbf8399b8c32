package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.nopodescriptions;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialsWithNoPODescriptions {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialsWithNoPODescriptions(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoPOText(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-purchase-order-descriptions")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoPODescriptions(client))
        .build();
  }
}
