package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialstoragelocations;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created on 6/19/2019 11:24 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialStorageLocationEntitiesWriter implements ItemWriter<MaterialStorageLocationEntity> {

  private final MaterialStorageLocationsStagingRepository repository;

  @Override
  public void write(final List<? extends MaterialStorageLocationEntity> items) {
    for (final MaterialStorageLocationEntity entity :
        items) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    repository.saveAll(items);
  }
}
