package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus;

import com.google.common.collect.ArrayListMultimap;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class Material {

  private final String client;
  private final String materialCode;
  private final String materialGroupId;
  private final String materialGroupDescription;
  private final Map<String, String> shortDescription;
  private final Map<String, String> longDescriptionPO;
  private final Map<String, String> longDescription;
  private final Map<String, String> inspectionDescription;
  private final Map<String, String> internalNoteDescription;
  private final boolean deletionFlag;
  private final String manufacturer;
  private final String manufacturerPartNumber;
  private final String vendorName;
  private final Double totalStockQuantity;
  private final Double totalConsumptionQuantity;
  private final Double totalPOQuantity;
  private final Map<String, Double> totalStockAmount;
  private final Map<String, Double> totalConsumptionAmount;
  private final Map<String, Double> totalPOAmount;
  private final MaterialPrices prices;
  private final String oldMaterialNumber;
  private final Map<String, String> technicalCharacteristics;
  private final List<String> plants;
  private final List<String> warehouses;
  private final boolean semanticallyAnalyzed;
  private final String mdDomain;
  private final ArrayListMultimap<String, String> characteristics;

  // added to comply with A2A requirements
  private final String materialType;
  private final String industrySector;
  private final String materialGroup;
  private final String baseUnitOfMeasurement;
  private final String productDivision;
  private final String authorizationGroup;
  private final String crossPlantMaterialStatus;
  private final Long materialStatusValidFromDate;
  private final String manufacturerCode;
  private final String genericItemGroup;
  private final String revisionNumber;
  private final String purchasingMeasurementUnits;
  private final String materialCreatedOn;
  private final String externalMaterialGroup;
  private final String weightUnit;
  private final BigDecimal netWeight;
  private final BigDecimal grossWeight;
  private final String sizeDimension;
  private final String hazardousMaterialNumber;
  private final String basicMaterial;
  private final String documentNumber;
  private final boolean batchManagementRequirementIndicator;
  private final String laboratory;

  //FIXME: Custom fields required by A2A T4-1756
  private final String famiglia;
  private final String sottoFamiglia;
  private final String specificaTecnica;
  private final String edizione;
  private final String revisione;
  private final Long dataCustom;

  // FIXME: Custom fields required by Perenco T4-1538
  private final String productHierarchy;

  public static MaterialBuilder from(final Material material) {
    return Material.builder()
        .client(material.client)
        .materialCode(material.materialCode)
        .materialGroupId(material.materialGroupId)
        .materialGroupDescription(material.materialGroupDescription)
        .shortDescription(material.shortDescription)
        .longDescriptionPO(material.longDescriptionPO)
        .longDescription(material.longDescription)
        .deletionFlag(material.deletionFlag)
        .manufacturer(material.manufacturer)
        .manufacturerPartNumber(material.manufacturerPartNumber)
        .vendorName(material.vendorName)
        .totalStockQuantity(material.totalStockQuantity)
        .totalConsumptionQuantity(material.totalConsumptionQuantity)
        .totalPOQuantity(material.totalPOQuantity)
        .totalStockAmount(material.totalStockAmount)
        .totalConsumptionAmount(material.totalConsumptionAmount)
        .totalPOAmount(material.totalPOAmount)
        .oldMaterialNumber(material.oldMaterialNumber)
        .technicalCharacteristics(material.technicalCharacteristics)
        .characteristics(material.characteristics)
        .prices(material.prices)
        .plants(material.plants)
        .warehouses(material.warehouses)
        .mdDomain(material.mdDomain)
        .semanticallyAnalyzed(material.semanticallyAnalyzed)

        // added to comply to A2A requirements
        .materialType(material.materialType)
        .industrySector(material.industrySector)
        .materialGroup(material.materialGroup)
        .baseUnitOfMeasurement(material.baseUnitOfMeasurement)
        .productDivision(material.productDivision)
        .authorizationGroup(material.authorizationGroup)
        .crossPlantMaterialStatus(material.crossPlantMaterialStatus)
        .materialStatusValidFromDate(material.materialStatusValidFromDate)
        .manufacturerCode(material.manufacturerCode)
        .genericItemGroup(material.genericItemGroup)
        .revisionNumber(material.revisionNumber)
        .purchasingMeasurementUnits(material.purchasingMeasurementUnits)
        .materialCreatedOn(material.materialCreatedOn)
        .externalMaterialGroup(material.externalMaterialGroup)
        .weightUnit(material.weightUnit)
        .netWeight(material.netWeight)
        .grossWeight(material.grossWeight)
        .sizeDimension(material.sizeDimension)
        .hazardousMaterialNumber(material.hazardousMaterialNumber)
        .basicMaterial(material.basicMaterial)
        .documentNumber(material.documentNumber)
        .batchManagementRequirementIndicator(material.batchManagementRequirementIndicator)
        .laboratory(material.laboratory)

        //FIXME: Custom fields required by A2A T4-1756
        .famiglia(material.famiglia)
        .sottoFamiglia(material.sottoFamiglia)
        .specificaTecnica(material.specificaTecnica)
        .edizione(material.edizione)
        .revisione(material.revisione)
        .dataCustom(material.dataCustom)

        // FIXME: Custom fields required by Perenco T4-1538
        .productHierarchy(material.productHierarchy)
        ;
  }
}
