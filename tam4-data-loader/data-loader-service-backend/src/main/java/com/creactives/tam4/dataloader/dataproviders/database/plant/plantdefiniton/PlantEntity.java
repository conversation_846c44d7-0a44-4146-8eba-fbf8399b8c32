package com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Table(name = "plant_definitions_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PlantEntity implements ClientEnrichableEntity, Serializable {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "plant_definitions_staging_generator")
  @SequenceGenerator(name = "plant_definitions_staging_generator", sequenceName = "plant_definitions_staging_seq", allocationSize = 1000)
  private long id;

  @Column
  private String client;

  @Column(name = "plant")
  private String plant;

  @Column(name = "description")
  private String description;

  @Column(name = "valuation_area")
  private String valuationArea;

  @Column(name = "vendor_number")
  private String vendorNumber;

  @Column(name = "second_description")
  private String secondDescription;

  @Column(name = "city")
  private String city;

  @Column(name = "country_key")
  private String countryKey;

  @Column(name = "region")
  private String region;

  @Column(name = "language")
  private String language;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private boolean enabled;

  @Column(name = "erp_sequence_num")
  private Long erpSequenceNum;

  //questo è proprio il valore SAP che c'è nell'excel, in TAM lo ignoriamo, usiamo il valore già convertito
  @Deprecated
  @Column(name = "language_code")
  private String languageCode;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;

}
