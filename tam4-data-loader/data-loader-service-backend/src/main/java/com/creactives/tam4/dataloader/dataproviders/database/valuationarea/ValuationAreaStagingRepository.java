package com.creactives.tam4.dataloader.dataproviders.database.valuationarea;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ValuationAreaStagingRepository extends PagingAndSortingRepository<ValuationAreaEntity, Long> {

  ValuationAreaEntity findByClientAndValuationArea(String client, String valuationaArea);

  List<ValuationAreaEntity> findByClientAndValuationAreaIn(String client, List<String> valuationaArea);
}
