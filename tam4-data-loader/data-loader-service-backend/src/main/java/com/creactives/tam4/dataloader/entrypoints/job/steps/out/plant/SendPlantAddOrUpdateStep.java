package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.PlantDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.JpaCursorItemReader;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@Log4j2
@RequiredArgsConstructor
public class SendPlantAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForPlantDefinition calculateAggregateForPlantDefinition;
  private final ConvertAggregateToPlantDefinitionRequestItemProcessor convertAggregateToPlantDefinitionRequestItemProcessor;
  private final JpaCursorItemReader<PlantEntity> plantItemReader;
  private final PlantDefinitionWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-plant-definition-add-or-update")
        .<PlantEntity, PlantDefinitionUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(plantItemReader)
        .processor(new CompositeItemProcessorBuilder<PlantEntity, PlantDefinitionUpsertRequestMessage>()
            .delegates(List.of(calculateAggregateForPlantDefinition,
                convertAggregateToPlantDefinitionRequestItemProcessor
            ))
            .build()
        )
        .writer(new CompositeItemWriterBuilder<PlantDefinitionUpsertRequestMessage>()
                .delegates(List.of(writer,
                    updateStagingTable("plant_currency", "plant"),
//                                         updateStagingTable("plants_additional_languages_staging", "plant"),
                    updateStagingTable("plant_definitions_staging", "plant")
                )).build()
        )
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<PlantDefinitionUpsertRequestMessage> updateStagingTable(final String tableName, final String matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getPlant());
    });
  }
}
