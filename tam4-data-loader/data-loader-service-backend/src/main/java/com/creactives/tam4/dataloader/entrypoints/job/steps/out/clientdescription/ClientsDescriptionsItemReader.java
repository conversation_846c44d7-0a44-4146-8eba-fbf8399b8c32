package com.creactives.tam4.dataloader.entrypoints.job.steps.out.clientdescription;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.dataloader.configuration.ClientsConfiguration;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.support.ListItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@StepScope
public class ClientsDescriptionsItemReader extends ListItemReader<Client> {

  public ClientsDescriptionsItemReader(final ClientsConfiguration clientOverrideConfiguration,
                                       @Value("#{jobParameters['clientCode']}") final String clientCode) {
    super(clientOverrideConfiguration.getClients().stream().filter(client -> {
      return clientCode == null || clientCode.equals(client.getCode());
    }).collect(Collectors.toList()));
  }

}
