package com.creactives.tam4.dataloader.entrypoints.rest;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.dataloader.configuration.ClientsConfiguration;
import com.creactives.tam4.dataloader.configuration.SpringBatchConfiguration;
import com.creactives.tam4.dataloader.core.usecase.loadclient.ClientPathResolverUseCase;
import com.creactives.tam4.dataloader.entrypoints.job.MassiveFileUploadJob;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.configuration.ListableJobLocator;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.NoSuchJobException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/data-loader/api/job/massive")
@Log4j2
public class MassiveFileUploadLauncherController {

  private final MassiveFileUploadJob massiveFileUploadJob;
  private final JobLauncher jobLauncher;
  private final JobBuilderFactory factory;
  private final ClientPathResolverUseCase clientPathResolverUseCase;
  private final ClientsConfiguration clientOverrideConfiguration;
  //  private final TaskExecutor taskExecutor;
//  private final JobRepository jobRepository;
  private final ListableJobLocator listableJobLocator;


  public MassiveFileUploadLauncherController(final MassiveFileUploadJob massiveFileUploadJob,
                                             @Qualifier(SpringBatchConfiguration.TAM_JOB_LAUNCHER) final JobLauncher jobLauncher,
                                             final JobBuilderFactory factory,
                                             final ClientPathResolverUseCase clientPathResolverUseCase,
                                             final ClientsConfiguration clientOverrideConfiguration, final ListableJobLocator listableJobLocator) {
    this.massiveFileUploadJob = massiveFileUploadJob;
    this.jobLauncher = jobLauncher;
    this.factory = factory;
    this.clientPathResolverUseCase = clientPathResolverUseCase;
    this.clientOverrideConfiguration = clientOverrideConfiguration;
//    this.taskExecutor = taskExecutor;
//    this.jobRepository = jobRepository;
    this.listableJobLocator = listableJobLocator;
  }


  @GetMapping("massive-file-upload/{client}")
  @SneakyThrows
  @Async
  public BatchStatus uploadBulkFilesForClient(@PathVariable final String client) {
    final Client configuredClient = clientOverrideConfiguration.forCode(client);
    return runNow(configuredClient);
  }

  @GetMapping("massive-file-upload-all-clients")
  @SneakyThrows
  public void uploadBulkFilesForAllClients() {
    final List<Client> clients = clientOverrideConfiguration.getClients();
    for (final Client client : clients) {
      runNow(client);
    }
  }


  private BatchStatus runNow(final Client configuredClient) throws JobRestartException, JobInstanceAlreadyCompleteException, JobParametersInvalidException, JobExecutionAlreadyRunningException, NoSuchJobException {

    log.info("Running loadDataJob");
    final HashMap<String, JobParameter> jobParameterMap = new HashMap<>();
    jobParameterMap.put("time", new JobParameter(System.currentTimeMillis()));

    final File clientFolder = clientPathResolverUseCase.getAttachmentsFolderForClient(configuredClient);
    jobParameterMap.put("inputFolder", new JobParameter(clientFolder.getAbsolutePath()));
    final Job job = massiveFileUploadJob.configureJob(factory);
    final JobParameters jobParameters = new JobParameters(jobParameterMap);
//    final JobExecution run = jobLauncher.run(job, jobParameters);

    final JobExecution run = jobLauncher.run(listableJobLocator.getJob("massive-file-upload"), jobParameters);

    while (run.isRunning()) {
      log.info("Job running ...");
    }

    return run.getStatus();
  }

}

