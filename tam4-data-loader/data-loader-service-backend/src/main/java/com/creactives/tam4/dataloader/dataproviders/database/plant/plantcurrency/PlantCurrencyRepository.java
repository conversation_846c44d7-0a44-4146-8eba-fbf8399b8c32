package com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface PlantCurrencyRepository extends PagingAndSortingRepository<PlantCurrencyEntity, Long> {

  PlantCurrencyEntity findByClientAndPlant(String client, String plant);

  @Query(value = "UPDATE PlantCurrencyEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE client= :client and synchronizationState = :stateOld AND plant in (:plants)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("client") String client,
                   @Param("stateOld") String stateOld,
                   @Param("plants") List<String> plants);
}
