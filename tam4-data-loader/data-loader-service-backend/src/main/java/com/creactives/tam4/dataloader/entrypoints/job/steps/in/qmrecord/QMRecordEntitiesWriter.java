package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmrecord;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.qmrecord.QMRecordStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.qmrecord.QMRecordsEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class QMRecordEntitiesWriter implements ItemWriter<QMRecordsEntity> {

  private final QMRecordStagingRepository qmRecordStagingRepository;

  @Override
  public void write(final List<? extends QMRecordsEntity> list) throws Exception {
    for (final QMRecordsEntity entity : list
    ) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    qmRecordStagingRepository.saveAll(list);
  }
}
