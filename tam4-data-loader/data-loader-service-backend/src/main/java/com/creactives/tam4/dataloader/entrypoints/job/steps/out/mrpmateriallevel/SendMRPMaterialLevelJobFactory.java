package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendMRPMaterialLevelJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMRPMaterialLevelAddOrUpdateStep step;

  public SendMRPMaterialLevelJobFactory(final JobBuilderFactory jobBuilderFactory,
                                        final StepBuilderFactory stepBuilderFactory,
                                        final SendMRPMaterialLevelAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendMRPMaterialLevelFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T438M")
        .build("send-mrp-material-level");
  }
}
