package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.currency;

import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.currency.CurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.currency.CurrencyStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationarea.ValuationAreaEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationarea.ValuationAreaStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * Created on 9/1/2019 5:26 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
public class CalculatePlantCurrencyProcessor implements ItemProcessor<PlantEntity, PlantCurrencyEntity> {

  private final CurrencyStagingRepository currencyStagingRepository;
  private final CompanyStagingRepository companyStagingRepository;
  private final ValuationAreaStagingRepository valuationAreaStagingRepository;
  private final CurrencyConverter converter;

  @Override
  public PlantCurrencyEntity process(final PlantEntity plantEntity) {
    final ValuationAreaEntity valuationAreaEntity = valuationAreaStagingRepository.findByClientAndValuationArea(plantEntity.getClient(), plantEntity.getValuationArea());
    if (valuationAreaEntity != null) {
      final CompanyEntity companyEntity = companyStagingRepository.findByClientAndCompanyCodeAndCurrencyKeyNotNull(valuationAreaEntity.getClient(), valuationAreaEntity.getCompanyCode());
      if (companyEntity != null) {
        final CurrencyEntity currencyEntity = currencyStagingRepository.findByClientAndCurrencyKey(companyEntity.getClient(), companyEntity.getCurrencyKey());
        if (currencyEntity != null) {
          return PlantCurrencyEntity.builder()
              .plant(plantEntity.getPlant())
              .client(plantEntity.getClient())
              .currency(currencyEntity.getIsoCurrencyCode())
              .createdOn(new Timestamp(System.currentTimeMillis()))
              .build();
        } else {
          log.error("Could not find currency based on client {} and currency {} provided",
              companyEntity.getClient(), companyEntity.getCurrencyKey());
        }
      } else {
        log.error("Could not calculate currency for plant due to missing company for plant {}, valuation area {}, company {}",
            plantEntity.getPlant(), valuationAreaEntity.getValuationArea(),
            valuationAreaEntity.getCompanyCode());
      }
    } else {
      log.error("Could not calculate currency for plant due to missing valuation area for plant {}", plantEntity.getPlant());
    }
    final String plant = plantEntity.getPlant();
    return PlantCurrencyEntity.builder()
        .plant(plant)
        .client(plantEntity.getClient())
        .currency(getCurrencyWhenMissing(plant))
        .createdOn(new Timestamp(System.currentTimeMillis()))
        .build();
  }

  private String getCurrencyWhenMissing(final String plant) {
    if (converter.isAssumeDefaultCurrencyWhenMissing()) {
      final String defaultCurrency = converter.getDefaultCurrency();
      log.warn("Saving currency {} for plant {}", defaultCurrency, plant);
      return defaultCurrency;
    }
    return null;
  }
}
