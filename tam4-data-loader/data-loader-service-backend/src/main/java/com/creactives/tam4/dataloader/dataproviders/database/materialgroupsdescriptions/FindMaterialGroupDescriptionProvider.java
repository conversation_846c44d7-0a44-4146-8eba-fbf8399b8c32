package com.creactives.tam4.dataloader.dataproviders.database.materialgroupsdescriptions;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.FindMaterialGroupDescription;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
@CacheConfig(cacheNames = "materialGroupDescriptions")
public class FindMaterialGroupDescriptionProvider implements FindMaterialGroupDescription {
  private final MaterialGroupDefinitionsStagingRepository materialGroupDefinitionsStagingRepository;

  public FindMaterialGroupDescriptionProvider(final MaterialGroupDefinitionsStagingRepository materialGroupDefinitionsStagingRepository) {
    this.materialGroupDefinitionsStagingRepository = materialGroupDefinitionsStagingRepository;
  }

  @Cacheable
  @Override
  public String findMaterialGroupDescription(final String client, final String materialGroupId) {
    return materialGroupDefinitionsStagingRepository
        .findByClientAndMaterialGroupAndLanguage(client, materialGroupId, "E")
        .map(MaterialGroupDefinitionEntity::getDescription)
        .orElse(null);
  }
}
