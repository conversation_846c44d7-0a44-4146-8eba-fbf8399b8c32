package com.creactives.tam4.dataloader.dataproviders.database.mmvmaterialStatus;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "mmv_material_status_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T141", description = "Material Status from Materials Management/PPC View")
public class MMVMaterialStatusEntity implements ClientEnrichableEntity {


  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "mmv_material_status_staging_generator")
  @SequenceGenerator(name = "mmv_material_status_staging_generator", sequenceName = "mmv_material_status_staging_seq", allocationSize = 1000)
  private long id;


  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_status")
  @FieldDoc(required = true, sapField = "MMSTA", description = "Material status from MM/PP view")
  private String materialStatus;

  @Column(name = "purchasing_message")
  @FieldDoc(sapField = "DEINK", description = "Message if material is used in Purchasing")
  private String purchasingMessage;

  @Column(name = "bom_header_message")
  @FieldDoc(sapField = "DSTLK", description = "Message if material is used in BOM header")
  private String bomHeaderMessage;

  @Column(name = "routing_message")
  @FieldDoc(sapField = "DAPLA", description = "Message if material is used in routing/master recipe")
  private String routingMessage;

  @Column(name = "requirement_message")
  @FieldDoc(sapField = "DPBED", description = "Message if independent requirement is created for material")
  private String requirementMessage;

  @Column(name = "mrp_message")
  @FieldDoc(sapField = "DDISP", description = "Message if material is used in MRP")
  private String mrpMessage;

  @Column(name = "production_item_message")
  @FieldDoc(sapField = "DFAPO", description = "Message if Material Used in Production Order or Network Item")
  private String productionItemMessage;

  @Column(name = "production_header_message")
  @FieldDoc(sapField = "DFAKO", description = "Message if material is used in production order header")
  private String productionHeaderMessage;

  @Column(name = "plant_maintenance_message")
  @FieldDoc(sapField = "DINST", description = "Message if material is used in Plant Maintenance")
  private String plantMaintenanceMessage;

  @Column(name = "inventory_message")
  @FieldDoc(sapField = "DBEST", description = "Message if material is used in Inventory Management")
  private String inventoryMessage;

  @Column(name = "forecasting_message")
  @FieldDoc(sapField = "DPROG", description = "Message if material is used in Forecasting")
  private String forecastingMessage;

  @Column(name = "prt_order_message")
  @FieldDoc(sapField = "DFHMI", description = "Message if PRT are assigned to routing or order")
  private String prtOrderMessage;

  @Column(name = "qm_message")
  @FieldDoc(sapField = "DQMPF", description = "Message if material is used in QM inspection procedures")
  private String qmMessage;

  @Column(name = "wm_change_message")
  @FieldDoc(sapField = "DTBED", description = "Message if material used in WM transfer reqmt/posting change")
  private String wmChangeMessage;

  @Column(name = "wm_order_message")
  @FieldDoc(sapField = "DTAUF", description = "Message if material is used in WM transfer order")
  private String wmOrderMessage;

  @Column(name = "mc_estimate_procedure")
  @FieldDoc(sapField = "DERZK", description = "Procedure When Creating Material Cost Estimate")
  private String mcEstimateProcedure;

  @Column(name = "long_term_planning_message")
  @FieldDoc(sapField = "DLFPL", description = "Message if material used in long-term planning")
  private String longTermPlanningMessage;

  @Column(name = "distribution_lock_indicator")
  @FieldDoc(sapField = "DLOCK", description = "Indicator: distribution lock")
  private String distributionLockIndicator;

  @Column(name = "ale_profile_name")
  @FieldDoc(sapField = "AUPRF", description = "Profile Name for ALE Change Authorization")
  private String aleProfileName;

  @Column(name = "locked_for_purchase")
  @FieldDoc(sapField = "JSPPP", description = "Locked for Purchase Quantity Planning")
  private String lockedForPurchase;

  @Column(name = "blocked_for_po")
  @FieldDoc(sapField = "JSPPO", description = "Blocked for PO Generation in Purchase Quantity Planning")
  private String blockedForPO;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
