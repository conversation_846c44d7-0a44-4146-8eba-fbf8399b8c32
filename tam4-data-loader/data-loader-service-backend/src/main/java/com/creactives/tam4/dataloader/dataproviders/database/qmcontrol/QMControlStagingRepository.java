package com.creactives.tam4.dataloader.dataproviders.database.qmcontrol;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QMControlStagingRepository extends PagingAndSortingRepository<QMControlEntity, Long> {

  List<QMControlEntity> findByClientAndQmControlKey(String client, String controlKey);

  @Modifying
  @Query("update QMControlEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update QMControlEntity set enabled= :enabled WHERE qmControlKey=:qmControlKey AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("qmControlKey") String qmControlKey, @Param("client") String client);

  List<QMControlEntity> findByClientAndQmControlKeyAndSynchronizationState(String client, String qmControlKey, String status);

  Page<QMControlEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<QMControlEntity> findAllByClient(String client, Pageable pageable);

  Page<QMControlEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
