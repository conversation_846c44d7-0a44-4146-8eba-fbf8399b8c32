package com.creactives.tam4.dataloader.configuration;

import com.creactives.tam4.utils.BigDecimalJacksonDeserializer;
import com.creactives.tam4.utils.BigDecimalJacksonSerializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.vladmihalcea.hibernate.type.util.ObjectMapperWrapper;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * Created on 1/8/2019 4:52 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Configuration
public class MessagingConfiguration {

  public static final String DATA_LOADER_EXCHANGE_NAME = "data-loader-exchange";
  public static final String WARNINGS_INBOUND_EXCHANGE_NAME = "warnings-inbound-exchange";
  public static final String WORKFLOW_INBOUND_EXCHANGE_NAME = "workflow-inbound-exchange";
  //FIXME non possono avere lo stesso nome altrimenti ci prendiamo solo uno dei 2 listener (il secondo, quello delle API).
  public static final String DATA_LOADER_INBOUND_QUEUE_NAME = "data-loader-service-queue";
  public static final String STANDARD_API_INBOUND_QUEUE_NAME = "data-loader-service-queue";

  public static final String ROUTING_KEY = "";

  @Value("${data-loader.api.events.json-mapper.use-custom-mapper:false}")
  private boolean customJsonMapper = false;

  @Value("${data-loader.api.events.json-mapper.remove-null-fields:false}")
  private boolean removeNullValues = false;

  @Bean
  public ObjectMapper objectMapper() {
    final ObjectMapper objectMapper = new ObjectMapper();

    if (customJsonMapper) {
      setObjectMapperCustomizations(objectMapper);
      setObjectMapperCustomizations(ObjectMapperWrapper.INSTANCE.getObjectMapper());

    }
    return objectMapper;
  }

  private void setObjectMapperCustomizations(final ObjectMapper objectMapper) {
    objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    objectMapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
    objectMapper.configure(SerializationFeature.FAIL_ON_UNWRAPPED_TYPE_IDENTIFIERS, false);
    objectMapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);


    // Deserialization
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objectMapper.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);

    // ----- Other cfgs -----
    // GBRRRT - this configuration removes null values.
    // We added a parameter so the usage of this is can be set in a customer specific configuration
    if (removeNullValues) {
      objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    final SimpleModule simpleFormattingModule = new SimpleModule();
    simpleFormattingModule.addSerializer(BigDecimal.class, new BigDecimalJacksonSerializer());
    simpleFormattingModule.addDeserializer(BigDecimal.class, new BigDecimalJacksonDeserializer());

    objectMapper.registerModule(simpleFormattingModule);
  }

  @Bean
  public FanoutExchange exchange() {
    return new FanoutExchange(DATA_LOADER_EXCHANGE_NAME);
  }

  @Bean
  public FanoutExchange warningsExchange() {
    return new FanoutExchange(WARNINGS_INBOUND_EXCHANGE_NAME);
  }

  @Bean
  public RabbitTemplate rabbitTemplate(final ConnectionFactory connectionFactory, final ObjectMapper objectMapper) {
    final RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
    rabbitTemplate.setMessageConverter(producerJackson2MessageConverter(objectMapper));
    return rabbitTemplate;
  }

  @Bean
  public Jackson2JsonMessageConverter producerJackson2MessageConverter(final ObjectMapper objectMapper) {

    return new Jackson2JsonMessageConverter(objectMapper);
  }

}
