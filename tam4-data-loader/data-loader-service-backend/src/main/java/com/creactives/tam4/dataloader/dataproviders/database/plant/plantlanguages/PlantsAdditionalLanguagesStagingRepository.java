package com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages;

import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Set;

public interface PlantsAdditionalLanguagesStagingRepository extends PagingAndSortingRepository<PlantsAdditionalLanguagesStagingEntity, Long> {


  Set<PlantsAdditionalLanguagesStagingEntity> findByClientAndPlant(String client, String plant);
}
