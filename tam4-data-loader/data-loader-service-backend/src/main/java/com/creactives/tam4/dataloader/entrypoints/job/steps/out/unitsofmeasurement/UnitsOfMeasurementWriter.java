package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.UnitsOfMeasurementUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Service
@Log4j2
@Deprecated
public class UnitsOfMeasurementWriter implements ItemWriter<UnitsOfMeasurementUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends UnitsOfMeasurementUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
