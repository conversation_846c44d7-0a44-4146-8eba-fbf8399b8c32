package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialWarehousesProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class CalculateAggregateForMaterialExtension implements ItemProcessor<MaterialEntity, AggregateDataForMaterialExtension> {

  private final MaterialPlantStagingRepository materialPlantStagingRepository;
  private final MaterialStorageLocationsStagingRepository materialStorageLocationsStagingRepository;
  private final PlantStagingRepository plantStagingRepository;

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForMaterialExtension process(final MaterialEntity materialEntity) {
    final List<MaterialPlantEntity> materialPlants = materialPlantStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    final List<MaterialStorageLocationEntity> allStorageLocations = materialStorageLocationsStagingRepository.findByClientAndMaterialCode(materialEntity.getClient(), materialEntity.getMaterialCode());
    final List<MaterialStorageLocationEntity> notDeletedStorages = allStorageLocations.stream().filter(it -> !AddMaterialWarehousesProcessor.SAP_CHECKED.equals(it.getFlagMaterialForDeletionAtStorageLocationLevel())).collect(Collectors.toList());
    return AggregateDataForMaterialExtension.builder()
        .materialEntity(materialEntity)
        .materialPlantEntities(materialPlants)
        .materialStorageLocationEntities(notDeletedStorages)
        .plants(materialPlants.stream().map(materialPlantEntity -> plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(materialPlantEntity.getClient(), materialPlantEntity.getPlantId())
        ).collect(Collectors.toMap(PlantEntity::getPlant, Function.identity())))
        .build();
  }
}
