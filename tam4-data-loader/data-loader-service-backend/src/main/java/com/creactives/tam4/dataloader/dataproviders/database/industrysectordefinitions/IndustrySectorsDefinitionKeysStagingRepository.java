package com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface IndustrySectorsDefinitionKeysStagingRepository extends PagingAndSortingRepository<IndustrySectorEntity, Long> {

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<IndustrySectorEntity> findByClientAndIndustrySector(String client, String industrySector);

  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  List<IndustrySectorEntity> findByClientAndIndustrySectorIn(String client, List<String> industrySector);

  @Override
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  <S extends IndustrySectorEntity> @NotNull S save(@NotNull S entity);

  @Modifying
  @Query("update IndustrySectorEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update IndustrySectorEntity set enabled= :enabled WHERE industrySector=:industrySector AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("industrySector") String industrySector, @Param("client") String client);

  Page<IndustrySectorEntity> findAllByClient(String client, Pageable pageable);
}
