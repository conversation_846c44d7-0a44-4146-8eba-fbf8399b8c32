package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialtypes;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.MaterialTypeDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SendMaterialTypesAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToMaterialTypeRequestItemProcessor convertEntityToMaterialTypeRequestItemProcessor;
  private final MaterialTypeITemReader materialTypeITemReader;
  private final MaterialTypeWriter materialTypeWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-material-type-add-or-update")
        .<MaterialTypesEntity, MaterialTypeDefinitionUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(materialTypeITemReader)
        .processor(convertEntityToMaterialTypeRequestItemProcessor)
        .writer(new CompositeItemWriterBuilder<MaterialTypeDefinitionUpsertRequestMessage>().delegates(List.of(materialTypeWriter,
            updateStagingTable("material_type_staging", "material_type"),
            updateStagingTable("material_type_definitions_staging", "material_type")
        )).build())
        .listener(new StatisticsListener())
        .build();
  }


  private JdbcBatchItemWriter<MaterialTypeDefinitionUpsertRequestMessage> updateStagingTable(final String tableName, final String matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getMaterialType());
    });
  }
}
