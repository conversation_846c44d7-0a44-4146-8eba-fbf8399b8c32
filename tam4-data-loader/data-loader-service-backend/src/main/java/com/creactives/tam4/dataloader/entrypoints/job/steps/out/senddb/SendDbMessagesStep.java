package com.creactives.tam4.dataloader.entrypoints.job.steps.out.senddb;

import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.stereotype.Component;

@Component
public class SendDbMessagesStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final DLJsonToMessageProcessor itemProcessor;
  private final SendDdbMessageItemReader itemReader;
  private final SendMessagesWriter writer;
  private final ChunkWriterListener chunkWriterListener;

  public SendDbMessagesStep(final StepBuilderFactory stepBuilderFactory,
                            final DLJsonToMessageProcessor itemProcessor,
                            final SendDdbMessageItemReader itemReader,
                            final SendMessagesWriter writer,
                            final ChunkWriterListener chunkWriterListener) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.itemProcessor = itemProcessor;
    this.itemReader = itemReader;
    this.writer = writer;
    this.chunkWriterListener = chunkWriterListener;
  }

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-db-message-step")
        .<DbMessage, ObjectMessage>chunk(10000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(writer)
        .listener(new StatisticsListener())
        .build();
  }

}
