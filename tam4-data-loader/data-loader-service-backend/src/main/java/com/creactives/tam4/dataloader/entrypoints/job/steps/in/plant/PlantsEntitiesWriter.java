package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class PlantsEntitiesWriter implements ItemWriter<PlantEntity> {

  private final PlantStagingRepository plantStagingRepository;

  @Override
  public void write(final List<? extends PlantEntity> list) throws Exception {
    for (final PlantEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    plantStagingRepository.saveAll(list);
  }
}
