package com.creactives.tam4.dataloader.excel.sap.core.usecase.validation;

import com.creactives.tam4.dataloader.configuration.SpringBatchConfiguration;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobExecutionNotRunningException;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.batch.core.launch.NoSuchJobExecutionException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Date: 2/1/2021 Time: 6:06 PM
 */
@Service
@Log4j2
public class SimpleJobManager {

  private final JobExplorer jobExplorer;
  private final JobLauncher jobLauncher;
  private final JobOperator jobOperator;

  public SimpleJobManager(final JobExplorer jobExplorer,
                          final JobOperator jobOperator,
                          @Qualifier(SpringBatchConfiguration.TAM_JOB_LAUNCHER) final JobLauncher jobLauncher) {
    this.jobExplorer = jobExplorer;
    this.jobOperator = jobOperator;
    this.jobLauncher = jobLauncher;
  }

  @SneakyThrows
  public long start(final Job job, final Map<String, JobParameter> params) {
    final HashMap<String, JobParameter> arguments = new HashMap<>(params);
    arguments.put("time", new JobParameter(System.currentTimeMillis()));
    final JobExecution execution = jobLauncher.run(job, new JobParameters(arguments));
    return execution.getId();
  }

  @SneakyThrows
  public JobExecution findExecution(final long executionId) {
    return jobExplorer.getJobExecution(executionId);
  }

  public boolean stop(final long executionId) {
    try {
      jobOperator.stop(executionId);
      return true;
    } catch (final NoSuchJobExecutionException | JobExecutionNotRunningException e) {
      log.catching(e);
      return false;
    }
  }

  public Set<JobExecution> findExecutionsByName(final String jobName) {
    return jobExplorer.findRunningJobExecutions(jobName);
  }

  public Set<JobExecution> findExecutionsByNameAndParameter(final String jobName, final String lastFolderNameParameter) {
    final Set<JobExecution> jobExecutions = new HashSet<JobExecution>();
    if (lastFolderNameParameter == null) return jobExecutions;
    jobExplorer.getJobInstances(jobName, 0, Integer.MAX_VALUE).stream().forEach(jobInstance -> {
      jobExecutions.addAll(jobExplorer.getJobExecutions(jobInstance).stream().filter(jobExecution -> jobExecution.getJobParameters() != null
              && jobExecution.getJobParameters().getString("input.file.path") != null
              && jobExecution.getJobParameters().getString("input.file.path").contains(lastFolderNameParameter))
          .collect(Collectors.toSet()));
    });
    return jobExecutions;
  }
}
