package com.creactives.tam4.dataloader.entrypoints.job.steps.in.valuationarea;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.valuationarea.ValuationAreaEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationarea.ValuationAreaStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class ValuationAreaEntitiesWriter implements ItemWriter<ValuationAreaEntity> {

  private final ValuationAreaStagingRepository valuationAreaStagingRepository;

  @Override
  public void write(final List<? extends ValuationAreaEntity> list) throws Exception {
    for (final ValuationAreaEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    valuationAreaStagingRepository.saveAll(list);
  }
}
