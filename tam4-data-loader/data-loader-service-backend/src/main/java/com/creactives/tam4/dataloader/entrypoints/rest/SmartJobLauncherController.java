package com.creactives.tam4.dataloader.entrypoints.rest;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.dataloader.configuration.ClientsConfiguration;
import com.creactives.tam4.dataloader.configuration.SpringBatchConfiguration;
import com.creactives.tam4.dataloader.core.usecase.loadclient.ClientPathResolverUseCase;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.configuration.ListableJobLocator;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/data-loader/api/job/")
@Log4j2
//@RequiredArgsConstructor
public class SmartJobLauncherController {

  private final JobLauncher jobLauncher;
  private final ListableJobLocator listableJobLocator;
  private final ClientsConfiguration clientOverrideConfiguration;
  private final ClientPathResolverUseCase clientPathResolverUseCase;

  public SmartJobLauncherController(@Qualifier(SpringBatchConfiguration.TAM_JOB_LAUNCHER) final JobLauncher jobLauncher,
                                    final ListableJobLocator listableJobLocator,
                                    final ClientsConfiguration clientOverrideConfiguration,
                                    final ClientPathResolverUseCase clientPathResolverUseCase) {
    this.jobLauncher = jobLauncher;
    this.listableJobLocator = listableJobLocator;
    this.clientOverrideConfiguration = clientOverrideConfiguration;
    this.clientPathResolverUseCase = clientPathResolverUseCase;
  }

  private static void loadParameterFromRequest(final HttpServletRequest httpRequest, final Map<String, JobParameter> jobParameterMap) {
    httpRequest.getParameterMap().forEach((k, v) -> jobParameterMap.putIfAbsent(k, new JobParameter(v[0])));
  }

  @GetMapping("{entity}")
  @SneakyThrows
  @Async
  public void launchJob(@PathVariable final String entity,
                        @RequestParam(value = "reload", defaultValue = "false") final String reload,
                        @RequestParam(value = "start_from", required = false) final String start_from,
                        @RequestParam(value = "client", required = false) final String client,
                        final HttpServletRequest httpRequest) {
    final Map<String, JobParameter> jobParameterMap = new HashMap<>();
    jobParameterMap.put("reload", new JobParameter(reload));
    jobParameterMap.put("time", new JobParameter(System.currentTimeMillis()));
    if (null != start_from) {
      jobParameterMap.put("start_from", new JobParameter(start_from));
    }

    if(null != client) {
      jobParameterMap.put("clientCode", new JobParameter(client));
    }

    loadParameterFromRequest(httpRequest, jobParameterMap);

    final JobParameters jobParameters = new JobParameters(jobParameterMap);
    final JobExecution run = jobLauncher.run(listableJobLocator.getJob(entity), jobParameters);
  }

  @GetMapping("{client}/{entity}")
  @SneakyThrows
  @Async
  public void launchJobDataLoad(@PathVariable final String client,
                        @PathVariable final String entity,
                        @RequestParam(value = "reload", defaultValue = "false") final String reload,
                        @RequestParam(value = "start_from", required = false) final String start_from,
                        final HttpServletRequest httpRequest) {
    final Map<String, JobParameter> jobParameterMap = new HashMap<>();
    jobParameterMap.put("reload", new JobParameter(reload));
    jobParameterMap.put("time", new JobParameter(System.currentTimeMillis()));
    if (start_from != null) {
      jobParameterMap.put("start_from", new JobParameter(start_from));
    }

    clientOverrideConfiguration.setCurrentClientCode(client);
    final Client configuredClient = clientOverrideConfiguration.forCode(client);
    final File clientFolder = clientPathResolverUseCase.getValidatedClientFolder(configuredClient);
    jobParameterMap.put("inputFolder", new JobParameter(clientFolder.getAbsolutePath()));
    jobParameterMap.put("clientCode", new JobParameter(configuredClient.getCode()));

    loadParameterFromRequest(httpRequest, jobParameterMap);
    final JobParameters jobParameters = new JobParameters(jobParameterMap);
    jobLauncher.run(listableJobLocator.getJob(entity), jobParameters);
  }

}
