package com.creactives.tam4.dataloader.entrypoints.job.steps.in.storagelocation;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.storagelocation.StorageLocationStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
@StepScope
public class StorageLocationsEntitiesWriter implements ItemWriter<StorageLocationEntity> {

  private final StorageLocationStagingRepository storageLocationStagingRepository;

  @Override
  public void write(final List<? extends StorageLocationEntity> list) throws Exception {
    for (final StorageLocationEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    storageLocationStagingRepository.saveAll(list);
  }
}
