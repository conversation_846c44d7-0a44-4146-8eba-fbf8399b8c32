package com.creactives.tam4.dataloader.entrypoints.job.steps.out.clientdescription;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.SendClientsDescriptionsMessage;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.stereotype.Component;

@Component
public class SendClientsDescriptionsAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertAggregateToClientsDescriptionsItemProcessor convertAggregateToClientsDescriptionsItemProcessor;
  private final ClientsDescriptionsItemReader clientsDescriptionsItemReader;
  private final ClientsDescriptionsWriter clientsDescriptionsWriter;

  public SendClientsDescriptionsAddOrUpdateStep(final StepBuilderFactory stepBuilderFactory,
                                                final ConvertAggregateToClientsDescriptionsItemProcessor convertAggregateToClientsDescriptionsItemProcessor,
                                                final ClientsDescriptionsItemReader clientsDescriptionsItemReader,
                                                final ClientsDescriptionsWriter clientsDescriptionsWriter) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.convertAggregateToClientsDescriptionsItemProcessor = convertAggregateToClientsDescriptionsItemProcessor;
    this.clientsDescriptionsItemReader = clientsDescriptionsItemReader;
    this.clientsDescriptionsWriter = clientsDescriptionsWriter;
  }

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get("send-clients-descriptions")
        .<Client, SendClientsDescriptionsMessage>chunk(1000)
        .reader(clientsDescriptionsItemReader)
        .processor(convertAggregateToClientsDescriptionsItemProcessor)
        .writer(clientsDescriptionsWriter)
        .listener(new StatisticsListener())
        .build();
  }
}
