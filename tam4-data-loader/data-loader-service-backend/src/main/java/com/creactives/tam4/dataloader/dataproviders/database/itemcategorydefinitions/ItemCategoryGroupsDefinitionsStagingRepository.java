package com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemCategoryGroupsDefinitionsStagingRepository extends PagingAndSortingRepository<ItemCategoryGroupDefinitionsEntity, Long> {

  List<ItemCategoryGroupDefinitionsEntity> findByClientAndItemCategoryGroup(String client, String itemCategoryGroup);

  List<ItemCategoryGroupDefinitionsEntity> findByClientAndLanguageAndItemCategoryGroupIn(String client, String language, List<String> itemCategoryGroups);

  List<ItemCategoryGroupDefinitionsEntity> findByClientAndItemCategoryGroupAndSynchronizationState(String client, String itemCategoryGroup, String synchronizationState);
}
