package com.creactives.tam4.dataloader.dataproviders.database.approvedmanufacturerparts;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApprovedManufacturerPartsStagingRepository extends PagingAndSortingRepository<ApprovedManufacturedPartsEntity, Long> {

  List<ApprovedManufacturedPartsEntity> findByClientAndMaterialNumberAndRecordNumberIn(String client, String materialNumber, List<String> recordNumbers);

}
