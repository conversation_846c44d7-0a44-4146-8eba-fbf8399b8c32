package com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "global_valuation_category_keys_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GlobalValuationCategoryDefinitionsKeyEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "global_valuation_category_keys_staging_generator")
  @SequenceGenerator(name = "global_valuation_category_keys_staging_generator", sequenceName = "global_valuation_category_keys_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "global_valuation_category")
  private String globalValuationCategory;

  @Column(name = "enabled")
  private Boolean enabled;

}
