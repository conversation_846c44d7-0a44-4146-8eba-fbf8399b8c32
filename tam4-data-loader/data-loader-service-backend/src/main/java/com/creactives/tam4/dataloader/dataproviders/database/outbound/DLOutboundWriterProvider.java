package com.creactives.tam4.dataloader.dataproviders.database.outbound;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Date: 6/30/2020 Time: 10:17 PM
 */
@Component
@RequiredArgsConstructor
public class DLOutboundWriterProvider {
  private final JdbcTemplate jdbcTemplate;
  @Autowired
  private ObjectMapper objectMapper;

  @Transactional
  public <T> void writeBatchMessages(final String exchange, final List<T> messages) {
    final SimpleJdbcInsert insert = new SimpleJdbcInsert(jdbcTemplate);
    insert.withTableName("outbound_queue")
        .usingGeneratedKeyColumns("id")
        .usingColumns("message_type", "exchange", "content")
        .executeBatch(messages.stream()
            .map(it -> Map.of("message_type", it.getClass().getCanonicalName(),
                "exchange", exchange,
                "content", objectToString(it)))
            .toArray(Map[]::new)
        );
  }

  @SneakyThrows
  private String objectToString(final Object it) {
    return objectMapper.writeValueAsString(it);
  }
}
