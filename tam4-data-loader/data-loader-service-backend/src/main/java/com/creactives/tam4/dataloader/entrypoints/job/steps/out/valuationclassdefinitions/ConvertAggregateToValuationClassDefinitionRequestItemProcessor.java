package com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions;


import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsEntity;
import com.creactives.tam4.messaging.materials.commands.ValuationClassDefinitionUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Log4j2
public class ConvertAggregateToValuationClassDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForValuationClassDefinition, ValuationClassDefinitionUpsertRequestMessage> {

  @Override
  public ValuationClassDefinitionUpsertRequestMessage process(final AggregateDataForValuationClassDefinition aggregateDataForValuationClassDefinition) throws Exception {
    return ValuationClassDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForValuationClassDefinition.getValuationClassDefinitionsKeyEntity().getClient())
        .valuationClass(aggregateDataForValuationClassDefinition.getValuationClassDefinitionsKeyEntity().getValuationClass())
        .descriptions(aggregateDataForValuationClassDefinition.getValuationClassEntities().stream()
            .collect(Collectors.toMap(ValuationClassDefinitionsEntity::getLanguage,
                ValuationClassDefinitionsEntity::getDescription
            ))
        )
        .enabled(aggregateDataForValuationClassDefinition.getValuationClassDefinitionsKeyEntity().getEnabled())
        .build();
  }
}
