package com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MRPControllerStagingRepository extends PagingAndSortingRepository<MRPControllersEntity, Long> {

 List<MRPControllersEntity> findByClientAndPlantAndMrpControllerIn(String client, String plant, List<String> mrpControllers);

  Page<MRPControllersEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<MRPControllersEntity> findAllByClient(String client, Pageable pageable);

  Page<MRPControllersEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
