package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialgroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
@Log4j2
public class SendMaterialGroupAddOrUpdateStep implements StepConfigurer {

  private final DataSource dataSource;
  private final StepBuilderFactory stepBuilderFactory;
  private final MaterialGroupWriter writer;
  private final MaterialGroupItemReader materialGroupItemReader;
  private final ConvertEntityToMaterialGroupRequestItemProcessor convertEntityToMaterialGroupRequestItemProcessor;
  private final ChunkWriterListener chunkWriterListener;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-material-group-add-or-update")
        .<MaterialGroupEntity, MaterialAndMessageCouple>chunk(1000)
        .listener(chunkWriterListener)
        .reader(materialGroupItemReader)
        .processor(convertEntityToMaterialGroupRequestItemProcessor)
        .writer(new CompositeItemWriterBuilder<MaterialAndMessageCouple>().delegates(List.of(writer,
            updateStagingTable("material_group_staging", "material_group"),
            updateStagingTable("material_group_definitions_staging", "material_group")
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<MaterialAndMessageCouple> updateStagingTable(final String tableName, final String matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getMaterialGroupEntity().getClient());
      ps.setString(4, mcrm.getMaterialGroupEntity().getMaterialGroup());
    });
  }
}
