package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcertificatecategory;


import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions.QMCertificateCategoryDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions.QMCertificateCategoryDefinitonStagingRepository;
import com.creactives.tam4.messaging.materials.commands.QMCertificateCategoryUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class ConvertEntityToQMCertificateCategoryRequestItemProcessor implements ItemProcessor<QMCertificateCategoryEntity, QMCertificateCategoryUpsertRequestMessage> {

  private final QMCertificateCategoryDefinitonStagingRepository qmCertificateCategoryDefinitonStagingRepository;

  @Override
  public QMCertificateCategoryUpsertRequestMessage process(final QMCertificateCategoryEntity qmCertificateCategoryEntity) throws Exception {
    final List<QMCertificateCategoryDefinitionEntity> qmCertificateCategoryDefinitionEntities = qmCertificateCategoryDefinitonStagingRepository
        .findByClientAndCertificateType(qmCertificateCategoryEntity.getClient(), qmCertificateCategoryEntity.getCertificateType());

    return QMCertificateCategoryUpsertRequestMessage.builder()
        .client(qmCertificateCategoryEntity.getClient())
        .certificateType(qmCertificateCategoryEntity.getCertificateType())
        .shortText(qmCertificateCategoryDefinitionEntities.stream()
            .filter(qmCertificateCategoryDefinitionEntity -> qmCertificateCategoryDefinitionEntity.getLanguage() != null)
            .collect(Collectors.toMap(QMCertificateCategoryDefinitionEntity::getLanguage,
                QMCertificateCategoryDefinitionEntity::getShortText
            )))
        .enabled(qmCertificateCategoryEntity.getEnabled())
        .build();
  }
}
