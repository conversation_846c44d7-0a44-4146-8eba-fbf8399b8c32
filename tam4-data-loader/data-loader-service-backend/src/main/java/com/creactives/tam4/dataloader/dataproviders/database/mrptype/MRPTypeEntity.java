package com.creactives.tam4.dataloader.dataproviders.database.mrptype;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "mrp_type_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T438A", description = "Industry Descriptions")
public class MRPTypeEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "mrp_type_staging_generator")
  @SequenceGenerator(name = "mrp_type_staging_generator", sequenceName = "mrp_type_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "mrp_type")
  @FieldDoc(required = true, sapField = "DISMM", referenceTable = "T438A", description = "MRP Type")
  private String mrpType;

  @Column(name = "mrp_procedure")
  @FieldDoc(sapField = "DISVF", description = "MRP procedure")
  private String mrpProcedure;

  @Column(name = "forecast_indicator")
  @FieldDoc(sapField = "PROKZ", description = "Forecast indicator")
  private String forecastIndicator;

  @Column(name = "consumption_forecast")
  @FieldDoc(sapField = "PROVB", description = "Consumption indicator of forecast")
  private String consumptionForecast;

  @Column(name = "mrp_forecast")
  @FieldDoc(sapField = "PROOS", description = "MRP indicator of forecast")
  private String mrpForecast;

  @Column(name = "safety_stock")
  @FieldDoc(sapField = "KZSIB", description = "Indicator: calculate safety stock")
  private String safetyStock;

  @Column(name = "reorder_point")
  @FieldDoc(sapField = "KZMEB", description = "Indicator: calculate reorder level")
  private String reorderPoint;

  @Column(name = "reduce_forecast")
  @FieldDoc(sapField = "PRRED", description = "Reduce forecast requirements")
  private String reduceForecast;

  @Column(name = "firming_type")
  @FieldDoc(sapField = "FXART", description = "Firming Type of the Planning Result")
  private String firmingType;

  @Column(name = "roll_forward")
  @FieldDoc(sapField = "RESPL", description = "Indicator: delete firm planned orders")
  private String rollForward;

  @Column(name = "plan_regularly")
  @FieldDoc(sapField = "KZREG", description = "Indicator: plan material regularly")
  private String planRegularly;

  @Column(name = "reorder_point_with_requirements")
  @FieldDoc(sapField = "ICBED", description = "Reorder point planning with external requirements")
  private String reorderPointWithRequirements;

  @Column(name = "time_phased_planning")
  @FieldDoc(sapField = "RYBED", description = "Time-phased planning with requirements (MRP)")
  private String timePhasedPlanning;

  @Column(name = "screen_sequence")
  @FieldDoc(sapField = "DYFOL", description = "Screen sequence for the header details")
  private String screenSequence;

  @Column(name = "planning_method")
  @FieldDoc(sapField = "CBPPT", description = "Method by which a material is planned")
  private String planningMethod;

  @Column(name = "material_staging_requirements")
  @FieldDoc(sapField = "BBBED", description = "Material staging reqmts (subcontr. reqmts) MRP element BB")
  private String materiaStagingRequirements;

  @Column(name = "order_delay_scheduled")
  @FieldDoc(sapField = "U1BED", description = "Release for stock transfer order")
  private String orderDelayScheduled;

  @Column(name = "delivery_schedule_for_stock_transfer_requisition")
  @FieldDoc(sapField = "U2BED", description = "Delivery schedule for a stock transfer requisition")
  private String deliveryScheduleForStockTransferRequisition;

  @Column(name = "delivery_schedule_for_stock_transport_agreement")
  @FieldDoc(sapField = "U4BED", description = "Delivery schedule for stock transport scheduling agreement")
  private String deliveryScheduleForStockTransportAgreement;

  @Column(name = "order_reservation")
  @FieldDoc(sapField = "A1BED", description = "Dependent reservation - standard orders")
  private String orderReservation;

  @Column(name = "network_reservation")
  @FieldDoc(sapField = "A2BED", description = "Dependent reservation - plant maintenance orders and network")
  private String networkReservation;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
