package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.MaterialPlantValuationEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory.POHistoryEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AggregateDataForMaterialPlantValuation {

  private MaterialEntity materialEntity;
  private List<MaterialPlantValuationEntity> materialPlantValuationEntities;
  private Map<String, List<POHistoryEntity>> orderedValuesByPlant;
  private Map<String, List<ConsumptionDataEntity>> consumptionValuesByPlant;
  private Map<String, PlantEntity> plants;
  private Map<String, PlantCurrencyEntity> plantCurrencies;

}
