package com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginEntity;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginKeyStagingRepository;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@StepScope
public class SchedulingMarginKeyItemReader extends RepositoryItemReader<SchedulingMarginEntity> {

  public SchedulingMarginKeyItemReader(final SchedulingMarginKeyStagingRepository stagingRepository,
                                       @Value("#{jobParameters['clientCode']}") final String clientCode,
                                       @Value("#{jobParameters['ignore_status']}") final String ignoreStatus
  ) {
    this.setRepository(stagingRepository);
    this.setPageSize(100);
    if (clientCode != null) {
      if (Boolean.parseBoolean(ignoreStatus)) {
        this.setMethodName("findAllByClient");
        this.setArguments(List.of(clientCode));
      } else {
        this.setMethodName("findAllBySynchronizationStateAndClient");
        this.setArguments(List.of(SyncStatus.PENDING.getCode(), clientCode));
      }
    } else {
      this.setMethodName("findAllBySynchronizationState");
      this.setArguments(List.of(SyncStatus.PENDING.getCode()));
    }

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC);
    this.setSort(sorts);
  }
}
