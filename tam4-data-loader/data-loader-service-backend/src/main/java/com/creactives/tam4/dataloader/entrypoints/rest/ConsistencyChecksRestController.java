package com.creactives.tam4.dataloader.entrypoints.rest;

import com.creactives.tam4.dataloader.configuration.Client;
import com.creactives.tam4.dataloader.configuration.ClientsConfiguration;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noconsumptionsmaterials.DetectMaterialsWithNoConsumptionsUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noorderedmaterials.DetectMaterialsWithNoOrderedUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noplantsmaterials.DetectMaterialWithNoPlantsUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.nopodescriptions.DetectMaterialsWithNoPODescriptions;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noshorttextmaterials.DetectMaterialsWithNoShortTextUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.novaluationsmaterials.DetectMaterialsWithNoValuationsUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.GetTotalAnalysedMaterialsUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.GetTotalIgnoredMaterialsUseCase;
import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.GetTotalMaterialsUseCase;
import com.creactives.tam4.dataloader.core.usecase.loadclient.GetTotalClientsInMaterialsUseCase;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@RestController
@RequestMapping("/data-loader/api")
@Log4j2
@RequiredArgsConstructor
public class ConsistencyChecksRestController {

  private final DetectMaterialWithNoPlantsUseCase detectMaterialWithNoPlantsUseCase;
  private final DetectMaterialsWithNoOrderedUseCase detectMaterialsWithNoOrderedUseCase;
  private final DetectMaterialsWithNoConsumptionsUseCase detectMaterialsWithNoConsumptionsUseCase;
  private final DetectMaterialsWithNoValuationsUseCase detectMaterialsWithNoValuationsUseCase;
  private final GetTotalMaterialsUseCase getTotalMaterialsUseCase;
  private final DetectMaterialsWithNoShortTextUseCase detectMaterialsWithNoShortTextUseCase;
  private final DetectMaterialsWithNoPODescriptions detectMaterialsWithNoPODescriptions;
  private final GetTotalIgnoredMaterialsUseCase getTotalIgnoredMaterialsUseCase;
  private final GetTotalAnalysedMaterialsUseCase getTotalAnalysedMaterialsUseCase;
  private final GetTotalClientsInMaterialsUseCase getTotalClientsInMaterialsUseCase;

  private final ClientsConfiguration clientsConfiguration;

  public static BigDecimal calculatePercentage(final long totalMaterials, final long value) {
    if (totalMaterials == 0L) {
      return BigDecimal.ZERO;
    }
    return BigDecimal.valueOf(value)
        .multiply(BigDecimal.valueOf(100L))
        .divide(BigDecimal.valueOf(totalMaterials), 2, RoundingMode.HALF_EVEN);
  }

  @GetMapping("/get-all-clients")
  @ResponseBody
  public List<String> getAllClientsCode() {
    return getTotalClientsInMaterialsUseCase.totalClients();
  }

  @GetMapping("/plants-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noPlantMaterials(@PathVariable final String client) {
    return detectMaterialWithNoPlantsUseCase.materialsWithNoPlants(client);
  }

  @GetMapping("/ordered-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noOrderedMaterials(@PathVariable final String client) {
    return detectMaterialsWithNoOrderedUseCase.materialsWithNoOrdered(client);
  }

  @GetMapping("/valuations-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noValuationsMaterials(@PathVariable final String client) {
    return detectMaterialsWithNoValuationsUseCase.materialsWithNoValuations(client);
  }

  @GetMapping("/consumption-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noConsumptionsMaterials(@PathVariable final String client) {
    return detectMaterialsWithNoConsumptionsUseCase.materialsWithNoConsumptions(client);
  }

  @GetMapping("/total-materials/{client}")
  @ResponseBody
  public DataAnalyseResponse totalMaterials(@PathVariable final String client) {
    return getTotalMaterialsUseCase.totalMaterials(client);
  }

  @GetMapping("/total-ignored-materials/{client}")
  @ResponseBody
  public DataAnalyseResponse totalIgnoredMaterials(@PathVariable final String client) {
    return getTotalIgnoredMaterialsUseCase.totalIgnoredMaterials(client);
  }

  @GetMapping("/total-analysed-materials/{client}")
  @ResponseBody
  public DataAnalyseResponse totalAnalysedMaterials(@PathVariable final String client) {
    return getTotalAnalysedMaterialsUseCase.totalAnalysedMaterials(client);
  }

  @GetMapping("/short-text-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noShortTextMaterials(@PathVariable final String client) {
    return detectMaterialsWithNoShortTextUseCase.materialsWithNoShortText(client);
  }

  @GetMapping("/po-descriptions-report/{client}")
  @ResponseBody
  public DataAnalyseResponse noPurchaseOrderDescriptionsMaterials(@PathVariable final String client) {
    return detectMaterialsWithNoPODescriptions.materialsWithNoPOText(client);
  }

  @GetMapping("/print-report/{client}")
  public void printToLog(@PathVariable final String client) {
    final Long totalMaterials = getTotalMaterialsUseCase.totalMaterials(client).getRowCount();
    log.info("Materials number: {}", totalMaterials);
    log.info("No. of ignored materials: {}", getTotalIgnoredMaterialsUseCase.totalIgnoredMaterials(client).getRowCount());
    log.info("No. of analyzed materials: {}", getTotalAnalysedMaterialsUseCase.totalAnalysedMaterials(client).getRowCount());
    final Long materialsWithoutShortTexts = detectMaterialsWithNoShortTextUseCase.materialsWithNoShortText(client).getRowCount();
    log.info("Materials without short texts: {}, ({}%)", materialsWithoutShortTexts, calculatePercentage(totalMaterials, materialsWithoutShortTexts));
    final Long materialsWithoutPoTexts = detectMaterialsWithNoPODescriptions.materialsWithNoPOText(client).getRowCount();
    log.info("Materials without PO texts: {}, ({}%)", materialsWithoutPoTexts, calculatePercentage(totalMaterials, materialsWithoutPoTexts));
    final Long materialsWithoutPlants = detectMaterialWithNoPlantsUseCase.materialsWithNoPlants(client).getRowCount();
    log.info("Materials without Plant data (MARC): {}, ({}%)", materialsWithoutPlants, calculatePercentage(totalMaterials, materialsWithoutPlants));
    final Long materialsWithoutValuations = detectMaterialsWithNoValuationsUseCase.materialsWithNoValuations(client).getRowCount();
    log.info("Materials without Valuations/Stocks data (MBEW): {}, ({}%)", materialsWithoutValuations, calculatePercentage(totalMaterials, materialsWithoutValuations));
    final Long materialsWithoutPOValues = detectMaterialsWithNoOrderedUseCase.materialsWithNoOrdered(client).getRowCount();
    log.info("Materials without PO values: {}, ({}%)", materialsWithoutPOValues, calculatePercentage(totalMaterials, materialsWithoutPOValues));
    final Long materialsWithoutConsumptionValues = detectMaterialsWithNoConsumptionsUseCase.materialsWithNoConsumptions(client).getRowCount();
    log.info("Materials without Consumption values: {}, ({}%)", materialsWithoutConsumptionValues, calculatePercentage(totalMaterials, materialsWithoutConsumptionValues));
  }

  @GetMapping("/print-report-all")
  public void printToForAllClients() {
    final List<Client> clients = clientsConfiguration.getClients();
    for (final Client client : clients) {
      final String code = client.getCode();
      log.info("-----------------------------Client {}-----------------------------------", code);
      printToLog(code);
    }
  }

}
