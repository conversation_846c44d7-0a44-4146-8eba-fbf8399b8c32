package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 4:41 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Table(name = "plants_valuation_data_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "MBEW", description = "Material Valuation")
public class MaterialPlantValuationEntity implements ClientEnrichableEntity, Serializable {

  @Id

  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "plants_valuation_data_staging_generator")
  @SequenceGenerator(name = "plants_valuation_data_staging_generator", sequenceName = "plants_valuation_data_staging_seq", allocationSize = 100000)

  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_code")
  @FieldDoc(required = true, sapField = "MATNR", referenceTable = "MARA", description = "Material Number")
  private String materialCode;

  @Column(name = "plant_id")
  @FieldDoc(required = true, sapField = "BWKEY", referenceTable = "T001W", description = "Valuation Area")
  private String plantId;

  @Column(name = "valuation_type")
  @FieldDoc(required = true, sapField = "BWTAR", description = "Valuation Type")
  private String valuationType;

  @Column(name = "deletion_flag_material")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private boolean deletionFlagMaterial;

  @Column(name = "valuation_class")
  @FieldDoc(required = true, sapField = "BKLAS", referenceTable = "T025", description = "Valuation Class")
  private String valuationClass;

  @Column(name = "valuation_category")
  @FieldDoc(sapField = "BWTTY", description = "Valuation Category")
  private String valuationCategory;

  @Column(name = "inventory_amount")
  @FieldDoc(required = true, sapField = "LBKUM", description = "Inventory Amount")
  private BigDecimal inventoryAmount;

  @Column(name = "price_control_indicator")
  @FieldDoc(sapField = "VPRSV", description = "Price control indicator")
  private String priceControlIndicator;

  @Column(name = "moving_average_price")
  @FieldDoc(required = true, sapField = "VERPR", description = "Moving Average Price/Periodic Unit Price")
  private BigDecimal movingAveragePrice;

  @Column(name = "standard_price")
  @FieldDoc(required = true, sapField = "STPRS", description = "Standard price")
  private BigDecimal standardPrice;

  @Column(name = "price_unit")
  @FieldDoc(sapField = "PEINH", description = "Price Unit")
  private Integer priceUnit;

  @Column(name = "total_value")
  @FieldDoc(required = true, sapField = "SALK3", description = "Total Valuated Stock")
  private BigDecimal totalValue;

  @Column(name = "usage_material")
  private String usageMaterial;

  @Column(name = "origin_material")
  private String originMaterial;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
