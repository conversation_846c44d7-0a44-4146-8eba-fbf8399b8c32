package com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.pohistory;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface POHistoryStagingRepository extends CrudRepository<POHistoryEntity, Long> {

  List<POHistoryEntity> findByClientAndMaterialCode(String client, String materialCode);

  List<POHistoryEntity> findByClientAndMaterialCodeAndPlantCodeIn(String client, String materialCode, List<String> plantCodes);

  @Query(value = "UPDATE POHistoryEntity SET synchronizationState = :state, synchronizedOn = :time " +
      "WHERE synchronizationState = :stateOld AND id in (:ids)")
  @Modifying
  @Transactional
  int updateStatus(@Param("state") String state,
                   @Param("time") Timestamp time,
                   @Param("stateOld") String stateOld,
                   @Param("ids") List<Long> ids);
}
