package com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IndustrySectorsDefinitionStagingRepository extends PagingAndSortingRepository<IndustrySectorDefinitionEntity, Long> {

  List<IndustrySectorDefinitionEntity> findByClientAndIndustrySector(String client, String industrySector);

  List<IndustrySectorDefinitionEntity> findByClientAndLanguageAndIndustrySectorIn(String client, String language, List<String> industrySectors);

  List<IndustrySectorDefinitionEntity> findByClientAndIndustrySectorAndSynchronizationState(String client, String industrySector, String synchronizationState);
}
