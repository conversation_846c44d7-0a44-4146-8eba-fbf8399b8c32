package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.SalesDivisionDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
public class SendProductDivisionDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ProductDivisionDefinitionWriter productDivisionDefinitionWriter;
  private final ProductDivisionKeysItemReader productDivisionKeysItemReader;
  private final CalculateAggregateForProductDivisionDefinition calculateAggregateForProductDivisionDefinition;
  private final ConvertAggregateToProductDivisionDefinitionRequestItemProcessor convertAggregateToProductDivisionDefinitionRequestItemProcessor;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-product-definition-definition-add-or-update")
        .<ProductDivisionKeyEntity, SalesDivisionDefinitionUpsertRequestMessage>chunk(1000)
        .reader(productDivisionKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<ProductDivisionKeyEntity, SalesDivisionDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForProductDivisionDefinition,
                convertAggregateToProductDivisionDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<SalesDivisionDefinitionUpsertRequestMessage>().delegates(List.of(productDivisionDefinitionWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<SalesDivisionDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "product_division_definitions_staging", "division",
        (salesDivisionDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, salesDivisionDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, salesDivisionDefinitionUpsertRequestMessage.getSalesDivision());
        });
  }
}
