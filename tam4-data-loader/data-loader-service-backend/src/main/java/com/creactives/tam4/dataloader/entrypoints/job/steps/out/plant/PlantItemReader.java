package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JpaCursorItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

@Component("send-plant-definition-add-or-update-reader")
@StepScope
public class PlantItemReader extends JpaCursorItemReader<PlantEntity> {


  public PlantItemReader(final EntityManagerFactory entityManagerFactory,
                         @Value("#{jobParameters['clientCode']}") final String clientCode,
                         @Value("#{jobParameters['ignore_status']}") final String ignoreStatus
  ) {
    setSaveState(false);
    setEntityManagerFactory(entityManagerFactory);

    final StringBuilder query = new StringBuilder();
    final Map<String, Object> params = new HashMap<>();

    query.append(" SELECT p FROM PlantEntity p ");
    query.append(" where 1=1 ");

    if (clientCode != null) {
      query.append(" and p.client = :clientCode ");
      params.put("clientCode", clientCode);
    }

    DataLoaderUtils.appendStatusJpa(ignoreStatus, query, params, "p");

    query.append(" order by id asc ");

    setParameterValues(params);
    setQueryString(query.toString());
  }


}
