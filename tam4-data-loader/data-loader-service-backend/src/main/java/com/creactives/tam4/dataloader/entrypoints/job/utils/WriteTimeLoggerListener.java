package com.creactives.tam4.dataloader.entrypoints.job.utils;

import com.creactives.tam4.common.utils.StopWatchUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.ChunkListener;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.ChunkContext;

import java.util.List;

@Log4j2
public class WriteTimeLoggerListener<T> implements ItemWriteListener<T>, ChunkListener {

  private final String loggingKey;
  private final String logLevel;

  public WriteTimeLoggerListener(final StepExecution stepExecution, final String logLevel) {
    this.loggingKey = stepExecution.getStepName() + "::" + stepExecution.getId() + "::write";
    this.logLevel = logLevel;
  }

  @Override
  public void beforeWrite(final List<? extends T> list) {
    StopWatchUtils.startStopwatch(loggingKey);
  }

  @Override
  public void afterWrite(final List<? extends T> list) {
    StopWatchUtils.stopAndPrint(loggingKey, logLevel);
  }

  @Override
  public void onWriteError(final Exception e, final List<? extends T> list) {

  }

  @Override
  public void beforeChunk(final ChunkContext chunkContext) {
    StopWatchUtils.startStopwatch(loggingKey + "::chunk");
  }

  @Override
  public void afterChunk(final ChunkContext chunkContext) {
    StopWatchUtils.stopAndPrint(loggingKey + "::chunk", "INFO");
  }

  @Override
  public void afterChunkError(final ChunkContext chunkContext) {

  }
}
