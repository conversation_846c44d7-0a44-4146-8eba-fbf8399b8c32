package com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
public interface GlobalValuationCategoriesKeysStagingRepository extends PagingAndSortingRepository<GlobalValuationCategoryDefinitionsKeyEntity, Long> {
  @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
  Optional<GlobalValuationCategoryDefinitionsKeyEntity> findByClientAndGlobalValuationCategory(String client, String globalValuationCategory);

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  <S extends GlobalValuationCategoryDefinitionsKeyEntity> @NotNull S save(@NotNull S entity);

  @Modifying
  @Query("update GlobalValuationCategoryDefinitionsKeyEntity set enabled= false")
  void setEnabledFalse();

  @Modifying
  @Query("update GlobalValuationCategoryDefinitionsKeyEntity set enabled= :enabled WHERE globalValuationCategory=:globalValuationCategory AND client=:client")
  void setEnabled(@Param("enabled") Boolean enabled, @Param("globalValuationCategory") String globalValuationCategory, @Param("client") String client);

  Page<GlobalValuationCategoryDefinitionsKeyEntity> findAllByClient(String client, Pageable pageable);

}
