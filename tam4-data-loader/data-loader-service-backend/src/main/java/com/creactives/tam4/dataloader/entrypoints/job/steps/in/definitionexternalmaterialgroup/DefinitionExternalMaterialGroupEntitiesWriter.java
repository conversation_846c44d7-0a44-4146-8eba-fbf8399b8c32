package com.creactives.tam4.dataloader.entrypoints.job.steps.in.definitionexternalmaterialgroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup.DefinitionExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.definitionexternalmaterialgroup.DefinitionExternalMaterialGroupStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@RequiredArgsConstructor

@Component
@StepScope
public class DefinitionExternalMaterialGroupEntitiesWriter implements ItemWriter<DefinitionExternalMaterialGroupEntity> {

  private final DefinitionExternalMaterialGroupStagingRepository descriptionExternalMaterialGroupStagingRepository;
  private final ExternalMaterialGroupStagingRepository externalMaterialGroupStagingRepository;

  @Override
  public void write(final List<? extends DefinitionExternalMaterialGroupEntity> list) throws Exception {
    for (final DefinitionExternalMaterialGroupEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    descriptionExternalMaterialGroupStagingRepository.saveAll(list);

    for (final DefinitionExternalMaterialGroupEntity entity : list) {
      if (externalMaterialGroupStagingRepository.findByClientAndExternalMaterialGroup(entity.getClient(), entity.getExternalMaterialGroup()) == null) {
        externalMaterialGroupStagingRepository.save(ExternalMaterialGroupEntity.builder()
            .createdOn(new Timestamp(System.currentTimeMillis()))
            .externalMaterialGroup(entity.getExternalMaterialGroup())
            .client(entity.getClient())
            .build());
      }
    }
  }
}
