package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class SendMaterialApplyExtensionRequestedV4JobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;

  @Bean(SendMaterialApplyExtensionRequestedV4Constants.JOB_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME)
  public JobFactory sendMaterialApplyExtensionRequestedV4Job(@Qualifier(SendMaterialApplyExtensionRequestedV4Constants.STEP_SEND_MATERIAL_APPLY_EXTENSION_REQUESTED_NAME) final Step step) {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: MARC, MARD, T001W, ")
        .build("send-plants-v4");
  }

}
