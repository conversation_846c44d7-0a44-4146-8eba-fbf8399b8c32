package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.creactives.tam4.common.batch.SimpleJdbcPartitioner;
import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.dataloader.entrypoints.job.utils.WriteTimeLoggerListener;
import com.creactives.tam4.messaging.materials.load.MaterialPlantValuationUpsertLoadMessage;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ChunkListener;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.database.builder.JdbcCursorItemReaderBuilder;
import org.springframework.batch.item.support.CompositeItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class SendMaterialPlantValuationV2Step {

  private final StepBuilderFactory stepBuilderFactory;
  private final ChunkWriterListener chunkWriterListener;
  private final TaskExecutor taskExecutor;
  private final DataSource dataSource;

  private final SendMaterialPlantValuationV2Writer writer;

  @Bean(name = SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION)
  public Step getSendMaterialApplyExtensionRequestedStep(@Value("${creactives.tam4.batch.grid-size:8}") final int gridSize,
                                                         @Value("${data-loader.send-job.materials-plantvaluation-chunk-size:1000}") final int chunkSize,
                                                         @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_PARTITIONER) final Partitioner partitioner,
                                                         @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_READER) final JdbcCursorItemReader<AggregateDataForMaterialPlantValuation> itemReader,
                                                         @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_PROCESSOR) final ItemProcessor<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage> itemProcessor,
                                                         @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_WRITER) final CompositeItemWriter<MaterialPlantValuationUpsertLoadMessage> itemWriter,
                                                         @Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_WRITELISTENER) final ChunkListener loggerListener) {
    final AbstractTaskletStepBuilder<SimpleStepBuilder<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage>> builder = stepBuilderFactory.get(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION)
        .<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage>chunk(chunkSize)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(itemWriter)
        .listener(chunkWriterListener)
        .listener(loggerListener);

    return stepBuilderFactory.get(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_PARTITIONER)
        .partitioner(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION, partitioner)
        .step(builder.build())
        .gridSize(gridSize)
        .taskExecutor(taskExecutor)
        .build();
  }

  @Bean(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_PARTITIONER)
  @JobScope
  public Partitioner partitioner(@Value("${data-loader.send-job.materials-plantvaluation-partition-size:10000}") final int partitionSize,
                                 @Value("#{jobExecution}") final JobExecution jobExecution,
                                 @Value("#{jobParameters['clientCode']}") final String clientCode,
                                 @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {

    final Map<String, Object> params = new HashMap<>();
    params.put("jobExecutionId", jobExecution.getJobId());
    params.put("clientCode", clientCode);
    params.put("ignoreStatus", ignoreStatus);

    final String _ignoreStatus = DataLoaderUtils.getIgnoreStatus(ignoreStatus);
    if (StringUtils.isNotBlank(_ignoreStatus)) {
      params.put("synchronizationState", _ignoreStatus);
    }

    return SimpleJdbcPartitioner.builder()
        .jdbcTemplate(new NamedParameterJdbcTemplate(dataSource))
        .qryForKeys(SendMaterialPlantValuationV2Constants.getPartitionerQuery(clientCode, ignoreStatus, true))
        .params(params)
        .partitionSize(partitionSize)
        .stringKey(true)
        .build();
  }

  @Bean(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_READER)
  @StepScope
  public JdbcCursorItemReader<AggregateDataForMaterialPlantValuation> reader(@Value("${data-loader.send-job.materials-chunk-size:1000}") final int fetchSize,
                                                                             @Value("#{stepExecution}") final StepExecution stepExecution,
                                                                             @Value("#{jobParameters['clientCode']}") final String clientCode,
                                                                             @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    return new JdbcCursorItemReaderBuilder<AggregateDataForMaterialPlantValuation>()
        .name(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_READER)
        .dataSource(dataSource)
        .preparedStatementSetter(DataLoaderUtils.initPrepareStatementSetter(clientCode, ignoreStatus, stepExecution, true))
        .rowMapper(new AggregateDataForMaterialPlantValuationRowMapper())
        .fetchSize(fetchSize)
        .sql(SendMaterialPlantValuationV2Constants.getChunkQuery(clientCode, ignoreStatus))
        .build();
  }


  @Bean(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_WRITER)
  @StepScope
  public CompositeItemWriter<MaterialPlantValuationUpsertLoadMessage> buildAsyncWriter() {
    return new CompositeItemWriterBuilder<MaterialPlantValuationUpsertLoadMessage>().delegates(List.of(writer,
        updateStagingTable("plants_valuation_data_staging", "material_code"),
        updateStagingTable("history_order_staging", "material_code"),
        updateStagingTable("consumption_order_staging", "material_code")
    )).build();
  }


  private JdbcBatchItemWriter<MaterialPlantValuationUpsertLoadMessage> updateStagingTable(final String tableName, final String matCodeColumn) {
    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getMaterialCode());
    });
  }

  @Bean(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION_WRITELISTENER)
  @StepScope
  public ChunkListener itemWriteListener(@Value("#{stepExecution}") final StepExecution stepExecution) {
    return new WriteTimeLoggerListener<MaterialPlantValuationUpsertLoadMessage>(stepExecution, "info");
  }
}
