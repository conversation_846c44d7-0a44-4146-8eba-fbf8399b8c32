package com.creactives.tam4.dataloader.entrypoints.job.steps.in.shortdescriptions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created on 6/5/2019 4:43 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class ShortDescriptionEntitiesWriter implements ItemWriter<ShortDescriptionEntity> {

  private final ShortDescriptionStagingRepository repository;

  @Override
  public void write(final List<? extends ShortDescriptionEntity> items) {
    for (final ShortDescriptionEntity entity :
        items) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    repository.saveAll(items);
  }
}
