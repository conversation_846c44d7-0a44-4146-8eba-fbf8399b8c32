package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcontrol;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.QMControlUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QMControlWriter implements ItemWriter<QMControlUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  private final QMControlStagingRepository qmControlStagingRepository;

  public QMControlWriter(final WriteMessageService sendMessages, final QMControlStagingRepository qmControlStagingRepository) {
    this.sendMessages = sendMessages;

    this.qmControlStagingRepository = qmControlStagingRepository;
  }

  @Override
  public void write(final List<? extends QMControlUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
