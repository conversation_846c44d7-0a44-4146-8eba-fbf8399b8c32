package com.creactives.tam4.dataloader.entrypoints.job.steps.out.clientdescription;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.SendClientsDescriptionsMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClientsDescriptionsWriter implements ItemWriter<SendClientsDescriptionsMessage> {

  private final WriteMessageService sendMessages;

  public ClientsDescriptionsWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }

  private void send(final SendClientsDescriptionsMessage sendClientsDescriptionsMessage) {
  }


  @Override
  public void write(final List<? extends SendClientsDescriptionsMessage> messages) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, messages);
  }
}
