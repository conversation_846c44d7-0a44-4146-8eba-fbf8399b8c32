package com.creactives.tam4.dataloader.entrypoints.job.steps.out.valuationclassdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclassdefinitions.ValuationClassesDefinitionsStagingRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Log4j2
@Component
@StepScope
public class CalculateAggregateForValuationClassDefinition implements ItemProcessor<ValuationClassDefinitionsKeyEntity, AggregateDataForValuationClassDefinition> {

  private final ValuationClassesDefinitionsStagingRepository valuationClassesDefinitionsStagingRepository;

  private final boolean ignoreStatus;


  public CalculateAggregateForValuationClassDefinition(final ValuationClassesDefinitionsStagingRepository valuationClassesDefinitionsStagingRepository,
                                                       @Value("#{jobParameters['ignore_status']}") final String ignoreStatus) {
    this.valuationClassesDefinitionsStagingRepository = valuationClassesDefinitionsStagingRepository;
    this.ignoreStatus = Boolean.parseBoolean(ignoreStatus);
  }

  @Override
  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  public AggregateDataForValuationClassDefinition process(final ValuationClassDefinitionsKeyEntity valuationClassDefinitionsKeyEntity) throws Exception {
    final List<ValuationClassDefinitionsEntity> entities = ignoreStatus ?
        valuationClassesDefinitionsStagingRepository.findByClientAndValuationClass(valuationClassDefinitionsKeyEntity.getClient(),
            valuationClassDefinitionsKeyEntity.getValuationClass())
        : valuationClassesDefinitionsStagingRepository.findByClientAndValuationClassAndSynchronizationState(valuationClassDefinitionsKeyEntity.getClient(),
        valuationClassDefinitionsKeyEntity.getValuationClass(),
        SyncStatus.PENDING.getCode()
    );
    if (CollectionUtils.isEmpty(entities)) {
      return null;
    }
    return AggregateDataForValuationClassDefinition.builder()
        .valuationClassDefinitionsKeyEntity(valuationClassDefinitionsKeyEntity)
        .valuationClassEntities(entities)
        .build();
  }

}
