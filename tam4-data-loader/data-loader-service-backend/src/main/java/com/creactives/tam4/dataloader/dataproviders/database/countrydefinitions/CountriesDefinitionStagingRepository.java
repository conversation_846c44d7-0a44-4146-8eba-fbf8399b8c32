package com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CountriesDefinitionStagingRepository extends PagingAndSortingRepository<CountryDefinitionsEntity, Long> {

  List<CountryDefinitionsEntity> findByClientAndCountry(String client, String country);

  List<CountryDefinitionsEntity> findByClientAndCountryIn(String client, List<String> country);

  List<CountryDefinitionsEntity> findByClientAndCountryAndSynchronizationState(String client, String country, String synchronizationState);
}
