package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialstatusdefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsKeyStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class MaterialStatusesDefinitionsEnabledEntitiesWriter implements ItemWriter<MaterialStatusEntity> {

  private final MaterialStatusDefinitionsKeyStagingRepository materialStatusDefinitionsKeyStagingRepository;

  @Override
  public void write(final List<? extends MaterialStatusEntity> itemsList) throws Exception {
    for (final MaterialStatusEntity entity : itemsList) {
      if (entity.getEnabled()) {
        materialStatusDefinitionsKeyStagingRepository.setEnabled(true, entity.getMaterialStatus(), entity.getClient());
      }
    }
  }
}
