package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialgroup;

import com.creactives.tam4.dataloader.dataproviders.database.materialgroup.MaterialGroupEntity;
import com.creactives.tam4.messaging.materials.commands.MaterialGroupDefinitionUpsertRequestMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialAndMessageCouple {

  private MaterialGroupEntity materialGroupEntity;
  private MaterialGroupDefinitionUpsertRequestMessage materialGroupDefinitionUpsertRequestMessage;

}
