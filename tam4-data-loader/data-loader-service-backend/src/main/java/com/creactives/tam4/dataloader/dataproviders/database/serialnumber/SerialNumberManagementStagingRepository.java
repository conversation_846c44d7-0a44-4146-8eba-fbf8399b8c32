package com.creactives.tam4.dataloader.dataproviders.database.serialnumber;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SerialNumberManagementStagingRepository extends PagingAndSortingRepository<SerialNumberManagementEntity, Long> {

  List<SerialNumberManagementEntity> findByClientAndSerialNumberProfileIn(String client, List<String> serialNumberProfiles);
  Page<SerialNumberManagementEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<SerialNumberManagementEntity> findAllByClient(String client, Pageable pageable);

  Page<SerialNumberManagementEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
