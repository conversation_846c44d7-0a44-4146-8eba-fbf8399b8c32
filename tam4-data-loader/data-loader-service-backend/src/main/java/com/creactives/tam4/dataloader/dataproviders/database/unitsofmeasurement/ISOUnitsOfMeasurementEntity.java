package com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "iso_units_of_measurement_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T006I", description = "ISO codes for units of measurement")
public class ISOUnitsOfMeasurementEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "iso_units_of_measurement_staging_generator")
  @SequenceGenerator(name = "iso_units_of_measurement_staging_generator", sequenceName = "iso_units_of_measurement_staging_seq", allocationSize = 100000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "CLIENT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "iso_units_of_measurement")
  @FieldDoc(required = true, sapField = "ISOCODE", description = "ISO code for unit of measurement")
  private String isoUnitsOfMeasurement;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
