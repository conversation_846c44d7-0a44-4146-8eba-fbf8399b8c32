package com.creactives.tam4.dataloader.entrypoints.job.steps.out.itemcategorydefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions.ItemCategoryGroupDefinitionsKeyEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForItemCategoryGroupDefinition {

  private ItemCategoryGroupDefinitionsKeyEntity itemCategoryGroupDefinitionsKeyEntity;
  private List<ItemCategoryGroupDefinitionsEntity> itemCategoryGroupEntities;
}
