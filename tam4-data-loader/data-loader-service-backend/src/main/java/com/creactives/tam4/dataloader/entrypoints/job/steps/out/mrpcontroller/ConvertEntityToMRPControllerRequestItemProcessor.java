package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpcontroller;


import com.creactives.tam4.dataloader.dataproviders.database.mrpcontrollers.MRPControllersEntity;
import com.creactives.tam4.messaging.materials.commands.MRPControllerUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToMRPControllerRequestItemProcessor implements ItemProcessor<MRPControllersEntity, MRPControllerUpsertRequestMessage> {


  @Override
  public MRPControllerUpsertRequestMessage process(final MRPControllersEntity mrpControllersEntity) throws Exception {
    return MRPControllerUpsertRequestMessage.builder()
        .client(mrpControllersEntity.getClient())
        .mrpController(mrpControllersEntity.getMrpController())
        .mrpControllerName(mrpControllersEntity.getMrpControllerName())
        .plant(mrpControllersEntity.getPlant())
        .purchasingGroup(mrpControllersEntity.getPurchasingGroup())
        .build();
  }
}
