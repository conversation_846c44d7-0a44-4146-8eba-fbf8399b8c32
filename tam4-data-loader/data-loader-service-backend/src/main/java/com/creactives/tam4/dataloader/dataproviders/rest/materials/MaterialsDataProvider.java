package com.creactives.tam4.dataloader.dataproviders.rest.materials;

import com.creactives.tam4.base_arch.rest_template.IMaskedErrors;
import com.creactives.tam4.base_arch.rest_template.ResponseErrorHandler;
import com.creactives.tam4.base_arch.rest_template.Tam4RestTemplateBuilder;
import com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload.AttachmentDataProvider;
import com.creactives.tam4.dataloader.entrypoints.rest.materialdetails.MaterialDetailsProvider;
import com.creactives.tam4.messaging.materialdetails.MaterialDetails;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.Objects;
import java.util.UUID;

@Service
@Log4j2
public class MaterialsDataProvider implements AttachmentDataProvider, MaterialDetailsProvider {

  public static final long KILO = 1024L;
  private final RestTemplate restTemplate;
  private final String materialsEndpoint;

  public MaterialsDataProvider(@Value("${data-loader.materials.endpoint}") final String materialsEndpoint, final Tam4RestTemplateBuilder restTemplateBuilder, @Nullable final IMaskedErrors maskedErrors) {
    this.materialsEndpoint = materialsEndpoint;
    this.restTemplate = restTemplateBuilder.createRestTemplate(new ResponseErrorHandler("materials", maskedErrors));
  }

  @Override
  public UUID addAttachment(final int userId, final File file, final String categoryDescription) throws Exception {
    final String fileName = file.getName();
    log.debug("Adding an attachments with filename: {} by user with id {}", fileName, userId);
    final String url = materialsEndpoint + "/materials/api/attachments/upload";
    final HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);

    final MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("userId", userId);
    requestBody.add("file", new FileSystemResource(file));
    requestBody.add("fileName", fileName);
    requestBody.add("categoryDescription", categoryDescription);
    final long fileSizeToKb = file.length() / KILO;
    requestBody.add("size", fileSizeToKb);

    final HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
    final ResponseEntity<UUID> response = restTemplate.postForEntity(url, requestEntity, UUID.class);
    if (response.getStatusCode().is2xxSuccessful()) {
      return Objects.requireNonNull(response.getBody());
    } else {
      throw new RuntimeException();
    }
  }

  @Override
  public MaterialDetails getMaterialDetails(final UUID materialId) {
    final HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    final HttpEntity<Void> request = new HttpEntity<>(null, headers);

    final ResponseEntity<MaterialDetails> response = restTemplate.postForEntity(materialsEndpoint + "/materials/api/details?materialId=" + materialId,
        request,
        MaterialDetails.class
    );
    if (response.getStatusCode().is2xxSuccessful()) {
      return response.getBody();
    } else {
      throw new RuntimeException();
    }
  }

}

