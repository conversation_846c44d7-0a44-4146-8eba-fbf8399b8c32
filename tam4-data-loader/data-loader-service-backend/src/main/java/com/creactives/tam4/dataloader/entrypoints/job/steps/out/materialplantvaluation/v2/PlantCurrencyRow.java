package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PlantCurrencyRow implements Serializable {

  @JsonAlias("id")
  private long id;

  @JsonAlias("client")
  private String client;

  @JsonAlias("plant")
  private String plant;

  @JsonAlias("currency")
  private String currency;

  @JsonAlias("created_on")
  private Timestamp createdOn;

  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;

  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;

  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @JsonAlias("synchronization_state")
  private String synchronizationState;
}
