package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created on 1/8/2019 4:53 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Log4j2
@Service
@StepScope
public class MaterialApplyExtensionRequestProviderV3 implements ItemWriter<MaterialApplyExtensionLoadMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends MaterialApplyExtensionLoadMessage> list) {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME,
        list.stream()
            .filter(message -> CollectionUtils.isNotEmpty(message.getMaterialPlantDetails()))
            .collect(Collectors.toList()));
  }
}
