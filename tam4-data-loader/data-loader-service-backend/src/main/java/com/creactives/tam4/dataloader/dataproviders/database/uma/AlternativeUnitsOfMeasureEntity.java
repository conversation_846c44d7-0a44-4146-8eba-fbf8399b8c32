package com.creactives.tam4.dataloader.dataproviders.database.uma;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 4:28 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Table(name = "units_of_measure_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "MARM", description = "Units of Measure for Material")
public class AlternativeUnitsOfMeasureEntity implements ClientEnrichableEntity {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "units_of_measure_staging_generator")
  @SequenceGenerator(name = "units_of_measure_staging_generator", sequenceName = "units_of_measure_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(sapField = "MANDT",
      description = "Client",
      referenceTable = "T000",
      required = true
  )
  private String client;

  @Column(name = "material_code")
  @FieldDoc(sapField = "MATNR",
      description = "Material Number",
      referenceTable = "MARA",
      required = true
  )
  private String materialCode;

  @Column(name = "alternative_unit_of_measurement")
  @FieldDoc(sapField = "MEINH",
      description = "Alternative Unit of Measure for Stockkeeping Unit",
      referenceTable = "T006",
      required = true
  )
  private String alternativeUnitOfMeasurement;

  @Column(name = "numerator")
  @FieldDoc(sapField = "UMREZ",
      description = "Numerator for Conversion to Base Units of Measure",
      required = true
  )
  private BigDecimal numerator;

  @Column(name = "denominator")
  @FieldDoc(sapField = "UMREN",
      description = "Denominator for conversion to base units of measure",
      required = true
  )
  private BigDecimal denominator;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
