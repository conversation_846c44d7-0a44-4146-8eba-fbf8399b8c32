package com.creactives.tam4.dataloader.dataproviders.database.dimension;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "dimension_text_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T006T", description = "Dimension Texts")
public class DimensionTextEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "dimension_text_staging_generator")
  @SequenceGenerator(name = "dimension_text_staging_generator", sequenceName = "dimension_text_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(sapField = "MANDT", required = true, referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "language_key")
  @FieldDoc(sapField = "SPRAS", required = true, description = "Language key")
  private String languageKey;

  @Column(name = "dimension_key")
  @FieldDoc(sapField = "DIMID", required = true, description = "Dimension key")
  private String dimensionKey;

  @Column(name = "dimension_text")
  @FieldDoc(sapField = "TXDIM", required = true, description = "Dimension text")
  private String dimenstionText;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
