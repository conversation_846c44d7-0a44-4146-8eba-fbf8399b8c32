package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v4;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 21/03/2024
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MaterialPlantRow implements Serializable {

  @JsonAlias("id")
  private long id;
  @JsonAlias("client")
  private String client;
  @JsonAlias("material_code")
  private String materialCode;
  @JsonAlias("plant_id")
  private String plantId;
  @JsonAlias("country_key")
  private String countryKey;
  @JsonAlias("deletion_flag")
  private boolean deletionFlag;
  @JsonAlias("status")
  private String status;
  @JsonAlias("seriable")
  private String seriable;
  @JsonAlias("mrp_type")
  private String mrpType;
  @JsonAlias("lead_time_in_days")
  private Integer leadTimeInDays;
  @JsonAlias("lotSize")
  private String lotSize;
  @JsonAlias("reorder_point")
  private BigDecimal reorderPoint;
  @JsonAlias("safety_stock")
  private BigDecimal safetyStock;
  @JsonAlias("maximum_stock_level")
  private BigDecimal maximumStockLevel;
  @JsonAlias("mrp_group")
  private String mrpGroup;
  @JsonAlias("follow_up_material")
  private String followUpMaterial;
  @JsonAlias("logistics_handling_group")
  private String logisticsHandlingGroup;
  @JsonAlias("min_lot_size")
  private BigDecimal minLotSize;
  @JsonAlias("minimum_safety_stock")
  private BigDecimal minimumSafetyStock;
  @JsonAlias("valid_from_date")
  private Long validFromDate;
  @JsonAlias("purchasing_group")
  private String purchasingGroup;
  @JsonAlias("mrp_controller")
  private String mrpController;
  @JsonAlias("in_house_production_time")
  private Integer inHouseProductionTime;
  @JsonAlias("individual_coll")
  private String individualColl;
  @JsonAlias("goods_receipt_processing_time_in_days")
  private Integer goodsReceiptProcessingTimeInDays;
  @JsonAlias("control_key_for_quality_management")
  private String controlKeyForQualityManagement;
  @JsonAlias("certificate_type")
  private String certificateType;
  @JsonAlias("batch_management_requirement")
  private boolean batchManagementRequirement;
  @JsonAlias("scheduling_margin_key_for_floats")
  private String schedulingMarginKeyForFloats;
  @JsonAlias("profit_center")
  private String profitCenter;
  @JsonAlias("plant_old_material_number")
  private String plantOldMaterialNumber;
  @JsonAlias("lot_size_for_product_costing")
  private BigDecimal lotSizeForProductCosting;
  @JsonAlias("intrastat_code")
  private String intrastatCode;
  @JsonAlias("control_code_consumption_taxes_foreign_trade")
  private String controlCodeConsumptionTaxesForeignTrade;
  @JsonAlias("material_cfopcategory")
  private String materialCFOPCategory;
  @JsonAlias("period_indicator")
  private String periodIndicator;
  @JsonAlias("special_procurement_type")
  private String specialProcurementType;
  @JsonAlias("checking_group_availability_check")
  private String checkingGroupAvailabilityCheck;
  @JsonAlias("avato_log_system")
  private String avatoLogSystem;
  @JsonAlias("avato_version")
  private String avatoVersion;
  @JsonAlias("avato_last_sync")
  private String avatoLastSync;
  @JsonAlias("avato_sequence")
  private String avatoSequence;
  @JsonAlias("created_on")
  private Timestamp createdOn;
  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;
  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;
  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;
  @JsonAlias("synchronization_state")
  private String synchronizationState;
  @JsonAlias("fixed_lot_size")
  private BigDecimal fixedLotSize;
  @JsonAlias("maximum_lot_size")
  private BigDecimal maximumLotSize;
  @JsonAlias("ordering_costs")
  private BigDecimal orderingCosts;
  @JsonAlias("storage_costs_indicator")
  private String storageCostsIndicator;
  @JsonAlias("rounding_value_for_purchase_order_quantity")
  private BigDecimal roundingValueForPurchaseOrderQuantity;
  @JsonAlias("unit_of_issue")
  private String unitOfIssue;
  @JsonAlias("procurement_type")
  private String procurementType;
  @JsonAlias("strategy_group")
  private String strategyGroup;
  @JsonAlias("critical_part")
  private String criticalPart;
  @JsonAlias("effective_out_date")
  private Long effectiveOutDate;
  @JsonAlias("country_of_origin")
  private String countryOfOrigin;
  @JsonAlias("loading_group")
  private String loadingGroup;
  @JsonAlias("planning_time_fence")
  private String planningTimeFence;
  @JsonAlias("consumption_mode")
  private String consumptionMode;
  @JsonAlias("consumption_period_backward")
  private String consumptionPeriodBackward;
  @JsonAlias("consumption_period_forward")
  private String consumptionPeriodForward;

  public String getPlantKey() {
    return client + "/" + plantId;
  }
}
