package com.creactives.tam4.dataloader.entrypoints.job.steps.in.supplierdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierEntity;
import com.creactives.tam4.dataloader.dataproviders.database.supplier.SupplierStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created on 6/11/2019 4:17 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class SupplierEntitiesWriter implements ItemWriter<SupplierEntity> {

  private final SupplierStagingRepository repository;

  @Override
  public void write(final List<? extends SupplierEntity> items) throws Exception {
    for (final SupplierEntity entity :
        items) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    repository.saveAll(items);
  }
}
