package com.creactives.tam4.dataloader.entrypoints.job.steps.in.valuationclass;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclass.ValuationClassStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.valuationclass.ValuationClassesEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class ValuationClassEntitiesWriter implements ItemWriter<ValuationClassesEntity> {

  private final ValuationClassStagingRepository valuationClassStagingRepository;

  @Override
  public void write(final List<? extends ValuationClassesEntity> list) throws Exception {
    for (final ValuationClassesEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    valuationClassStagingRepository.saveAll(list);

  }
}
