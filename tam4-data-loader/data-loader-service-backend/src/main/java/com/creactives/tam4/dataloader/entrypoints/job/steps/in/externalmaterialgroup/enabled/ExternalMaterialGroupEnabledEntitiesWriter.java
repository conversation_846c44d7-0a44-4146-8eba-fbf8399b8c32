package com.creactives.tam4.dataloader.entrypoints.job.steps.in.externalmaterialgroup.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupEntity;
import com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup.ExternalMaterialGroupStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ExternalMaterialGroupEnabledEntitiesWriter implements ItemWriter<ExternalMaterialGroupEntity> {

  private final ExternalMaterialGroupStagingRepository externalMaterialGroupStagingRepository;

  @Override
  public void write(final List<? extends ExternalMaterialGroupEntity> list) throws Exception {
    for (final ExternalMaterialGroupEntity entity : list) {
      if (entity.getEnabled()) {
        externalMaterialGroupStagingRepository.setEnabled(entity.getEnabled(), entity.getExternalMaterialGroup(), entity.getClient());
      }
    }
  }
}
