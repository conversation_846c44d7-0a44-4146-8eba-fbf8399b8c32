package com.creactives.tam4.dataloader.entrypoints.job.steps.in.productdivision;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionKeyStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class ProductDivisionEntitiesWriter implements ItemWriter<ProductDivisionEntity> {

  private final ProductDivisionStagingRepository productDivisionStagingRepository;
  private final ProductDivisionKeyStagingRepository productDivisionKeyStagingRepository;

  @Override
  public void write(final List<? extends ProductDivisionEntity> list) throws Exception {
    for (final ProductDivisionEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    productDivisionStagingRepository.saveAll(list);
    for (final ProductDivisionEntity productDivisionEntity : list) {
      if (productDivisionKeyStagingRepository.findByClientAndDivision(productDivisionEntity.getClient(), productDivisionEntity.getDivision()).isEmpty()) {
        productDivisionKeyStagingRepository.save(ProductDivisionKeyEntity.builder()
            .client(productDivisionEntity.getClient())
            .division(productDivisionEntity.getDivision())
            .build());
      }
    }
  }
}
