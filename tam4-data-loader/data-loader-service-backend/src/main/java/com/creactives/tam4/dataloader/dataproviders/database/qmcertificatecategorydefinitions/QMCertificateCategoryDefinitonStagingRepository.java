package com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategorydefinitions;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QMCertificateCategoryDefinitonStagingRepository extends PagingAndSortingRepository<QMCertificateCategoryDefinitionEntity, Long> {

  List<QMCertificateCategoryDefinitionEntity> findByClientAndCertificateType(String client, String certType);

  List<QMCertificateCategoryDefinitionEntity> findByClientInAndCertificateTypeIn(List<String> client, List<String> certType);

  List<QMCertificateCategoryDefinitionEntity> findByClientAndLanguageAndCertificateTypeIn(String client, String language, List<String> certTypes);
}
