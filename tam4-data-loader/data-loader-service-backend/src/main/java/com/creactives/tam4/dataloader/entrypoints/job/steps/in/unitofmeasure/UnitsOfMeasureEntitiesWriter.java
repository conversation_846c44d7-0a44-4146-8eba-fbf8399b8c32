package com.creactives.tam4.dataloader.entrypoints.job.steps.in.unitofmeasure;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitOfMeasureKeyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitsOfMeasureKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure.UnitsOfMeasureStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class UnitsOfMeasureEntitiesWriter implements ItemWriter<UnitOfMeasureEntity> {

  private final UnitsOfMeasureKeysStagingRepository unitsOfMeasureKeysStagingRepository;
  private final UnitsOfMeasureStagingRepository unitsOfMeasureStagingRepository;

  @Override
  public void write(final List<? extends UnitOfMeasureEntity> list) throws Exception {
    for (final UnitOfMeasureEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    unitsOfMeasureStagingRepository.saveAll(list);
    for (final UnitOfMeasureEntity item : list) {
      if (unitsOfMeasureKeysStagingRepository.findByClientAndUnitOfMeasure(item.getClient(), item.getUnitOfMeasure()).isEmpty()) {
        unitsOfMeasureKeysStagingRepository.save(UnitOfMeasureKeyEntity.builder()
            .client(item.getClient())
            .unitOfMeasure(item.getUnitOfMeasure())
            .enabled(true)
            .build());
      }
    }
  }
}
