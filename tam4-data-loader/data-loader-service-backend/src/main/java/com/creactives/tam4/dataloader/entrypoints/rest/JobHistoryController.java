package com.creactives.tam4.dataloader.entrypoints.rest;

import com.creactives.tam4.dataloader.entrypoints.response.ExecutionHistory;
import com.creactives.tam4.dataloader.entrypoints.response.JobExecutionsHistoryResponse;
import com.creactives.tam4.dataloader.entrypoints.response.JobStepHistoryResponse;
import com.creactives.tam4.dataloader.entrypoints.response.StepHistory;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Entity;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/data-loader/api/job-history")
@RequiredArgsConstructor
public class JobHistoryController {

  private static final int MAX_EXECUTIONS = 10_000;
  private final JobExplorer jobExplorer;

  @GetMapping("/executions")
  @ResponseBody
  public JobExecutionsHistoryResponse jobHistory() {
    final List<String> jobNames = jobExplorer.getJobNames();

    final List<ExecutionHistory> executionHistories = jobNames.stream()
        .map(this::getJobWithLatestVersion)
        .filter(Optional::isPresent)
        .map(instanceOptional -> jobExplorer.getJobExecutions(instanceOptional.get()))
        .flatMap(Collection::stream)
        .map(jobExecution -> ExecutionHistory.builder()
            .executionId(jobExecution.getId())
            .jobName(jobExecution.getJobInstance().getJobName())
            .startTime(jobExecution.getStartTime())
            .endTime(jobExecution.getEndTime())
            .exitStatus(jobExecution.getExitStatus().getExitCode())
            .build()
        )
        .collect(Collectors.toList());

    return new JobExecutionsHistoryResponse(executionHistories);
  }

  @GetMapping("/execution-steps")
  @ResponseBody
  public JobStepHistoryResponse jobSteps(final Long jobExecutionId) {
    final List<StepHistory> stepHistories = jobExplorer.getJobExecution(jobExecutionId).getStepExecutions().stream()
        .map(stepExecution -> StepHistory.builder()
            .stepName(stepExecution.getStepName())
            .readCount(stepExecution.getReadCount())
            .writeCount(stepExecution.getWriteCount())
            .startTime(stepExecution.getStartTime())
            .endTime(stepExecution.getEndTime())
            .exitStatus(stepExecution.getExitStatus().getExitCode())
            .build()
        )
        .collect(Collectors.toList());

    return new JobStepHistoryResponse(stepHistories);
  }

  private Optional<JobInstance> getJobWithLatestVersion(final String jobName) {
    return jobExplorer.getJobInstances(jobName, 0, MAX_EXECUTIONS).stream().max(Comparator.comparing(Entity::getVersion));
  }


}
