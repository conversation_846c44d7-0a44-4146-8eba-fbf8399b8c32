package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcontroldefinition;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontrol.QMControlStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions.QMControlDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcontroldefinitions.QMControlDefinitionsStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class QMControlDefinitionEntitiesWriter implements ItemWriter<QMControlDefinitionsEntity> {

  private final QMControlDefinitionsStagingRepository qmControlDefinitionsStagingRepository;
  private final QMControlStagingRepository qmControlStagingRepository;

  @Override
  public void write(final List<? extends QMControlDefinitionsEntity> list) throws Exception {
    for (final QMControlDefinitionsEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    qmControlDefinitionsStagingRepository.saveAll(list);

    for (final QMControlDefinitionsEntity entity : list) {
      if (qmControlStagingRepository.findByClientAndQmControlKey(entity.getClient(), entity.getQmControlKey()).isEmpty()) {
        qmControlStagingRepository.save(QMControlEntity.builder()
            .createdOn(new Timestamp(System.currentTimeMillis()))
            .client(entity.getClient())
            .qmControlKey(entity.getQmControlKey())
            .build()
        );
      }
    }
  }
}
