package com.creactives.tam4.dataloader;

import com.creactives.rabbitmq.EnableQuickListener;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.creactives")
@EnableScheduling
@EnableCaching
@EnableQuickListener
@EnableJpaRepositories(basePackages = "com.creactives")
@EntityScan(basePackages = "com.creactives")
//@EnableBatchProcessing
@EnableTransactionManagement
//@EnableConfigurationProperties
public class DataLoaderApplication {

  public static void main(final String[] args) {
    SpringApplication.run(DataLoaderApplication.class, args);
  }

}




