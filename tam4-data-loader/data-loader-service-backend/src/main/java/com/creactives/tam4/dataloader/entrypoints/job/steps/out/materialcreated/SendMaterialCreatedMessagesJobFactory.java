package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 7:03 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
public class SendMaterialCreatedMessagesJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final Step step;

  public SendMaterialCreatedMessagesJobFactory(final JobBuilderFactory jobBuilderFactory,
                                               final StepBuilderFactory stepBuilderFactory,
                                               @Qualifier(MaterialCreatedConstants.STEP_SEND_MATERIAL_CREATION_MESSAGES) final Step step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }


  @Bean
  public JobFactory sendMaterialCreatedFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: MARA, MAKT, Long_Text, MARM, AUSP")
        .build("send-material-created");
  }

}
