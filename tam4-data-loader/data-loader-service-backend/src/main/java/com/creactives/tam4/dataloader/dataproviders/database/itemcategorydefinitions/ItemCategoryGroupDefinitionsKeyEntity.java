package com.creactives.tam4.dataloader.dataproviders.database.itemcategorydefinitions;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Table(name = "item_category_group_keys_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TPTM", description = "Materials: Item Category Groups in Material Master")
public class ItemCategoryGroupDefinitionsKeyEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "item_category_group_keys_staging_generator")
  @SequenceGenerator(name = "item_category_group_keys_staging_generator", sequenceName = "item_category_group_keys_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "item_category_group")
  @FieldDoc(required = true, sapField = "MTPOS", description = "Item category group from material master")
  private String itemCategoryGroup;

  @Column(name = "enabled")
  private Boolean enabled;

}
