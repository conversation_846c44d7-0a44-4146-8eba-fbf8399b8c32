package com.creactives.tam4.dataloader.entrypoints.job.utils;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import lombok.experimental.UtilityClass;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.database.ItemPreparedStatementSetter;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.builder.JdbcBatchItemWriterBuilder;
import org.springframework.jdbc.core.PreparedStatementSetter;

import javax.sql.DataSource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@UtilityClass
@Log4j2
public class DataLoaderUtils {

  public static void appendStatusJpa(final String ignoreStatus, final StringBuilder query, final Map<String, Object> params, final String tableSuffix) {
    if (StringUtils.isBlank(ignoreStatus) || isSpecificStatus(ignoreStatus) || !Boolean.parseBoolean(ignoreStatus)) {
      query.append(" and ").append(tableSuffix).append(".synchronizationState = :synchronizationState ");
      params.put("synchronizationState", getIgnoreStatus(ignoreStatus));

    }
  }

  public static void appendStatusNamedParameters(final String ignoreStatus, final StringBuilder query, final String tableSuffix) {
    if (StringUtils.isBlank(ignoreStatus) || isSpecificStatus(ignoreStatus) || !Boolean.parseBoolean(ignoreStatus)) {
      query.append(" and ").append(tableSuffix).append(".synchronization_state = :synchronizationState ");
    }
  }

  public static void appendStatusParams(final String ignoreStatus, final StringBuilder query, final String tableSuffix) {
    if (StringUtils.isBlank(ignoreStatus) || isSpecificStatus(ignoreStatus) || !Boolean.parseBoolean(ignoreStatus)) {
      query.append(" and ").append(tableSuffix).append(".synchronization_state = ? ");
    }
  }

  public static String getIgnoreStatus(final String ignoreStatus) {
    try {
      if (isSpecificStatus(ignoreStatus)) {
        return SyncStatus.findByCode(ignoreStatus).getCode();
      } else if (StringUtils.isBlank(ignoreStatus) || !Boolean.parseBoolean(ignoreStatus)) {
        return SyncStatus.PENDING.getCode();
      }
    } catch (final IllegalArgumentException e) {
      log.throwing(e);
    }
    return null;
  }

  public static boolean isSpecificStatus(final String ignoreStatus) {
    try {
      SyncStatus.findByCode(ignoreStatus);
      return true;
    } catch (final IllegalArgumentException e) {
      return false;
    }
  }

  public static <T> JdbcBatchItemWriter<T> updateStagingTable(final DataSource dataSource,
                                                              final String tableName,
                                                              final String keyColumn,
                                                              final ItemPreparedStatementSetter<T> ps) {

    return updateStagingTable(dataSource, tableName, List.of(keyColumn), ps);
  }

  public static <T> JdbcBatchItemWriter<T> updateStagingTable(final DataSource dataSource,
                                                              @NotNull final String tableName,
                                                              @NotEmpty final List<String> keyColumns,
                                                              final ItemPreparedStatementSetter<T> ps) {


    final StringBuilder query = new StringBuilder();
    query.append("update ").append(tableName);
    query.append(" set synchronization_state = ? " +
        ", synchronized_on = ?");
    query.append(" where 1=1 ");
    query.append(" and client = ? ");
    keyColumns.forEach(k -> query.append(" and ").append(k).append(" = ? "));
//    query.append(" and synchronization_state = 'pending' ");

    final JdbcBatchItemWriterBuilder<T> builder = new JdbcBatchItemWriterBuilder<>();
    builder.assertUpdates(false);
    builder.dataSource(dataSource);
    builder.sql(query.toString());
    builder.itemPreparedStatementSetter(ps);
    return builder.build();
  }

  public static PreparedStatementSetter initPrepareStatementSetter(final String clientCode, final String ignoreStatus, final StepExecution stepExecution,
                                                                   final boolean stringKey) {
    return ps -> {
      int psCounter = 1;
      if (StringUtils.isNotBlank(clientCode)) {
        ps.setString(psCounter, clientCode);
        psCounter++;
      }

      final String _ignoreStatus = DataLoaderUtils.getIgnoreStatus(ignoreStatus);
      if (StringUtils.isNotBlank(_ignoreStatus)) {
        ps.setString(psCounter, _ignoreStatus);
        psCounter++;
      }

      if (stringKey) {
        final String _firstIndex = stepExecution.getExecutionContext().getString("firstIndex");
        final String _lastIndex = stepExecution.getExecutionContext().getString("lastIndex");
        ps.setString(psCounter, _firstIndex);
        psCounter++;
        ps.setString(psCounter, _lastIndex);
      } else {
        final long _firstIndex = stepExecution.getExecutionContext().getLong("firstIndex");
        final long _lastIndex = stepExecution.getExecutionContext().getLong("lastIndex");
        ps.setLong(psCounter, _firstIndex);
        psCounter++;
        ps.setLong(psCounter, _lastIndex);
      }
    };
  }

  public static String buildConditionPartitioner(final String clientCode, final String ignoreStatus, final String tableSuffix, final boolean namedParameterQuery) {
    final StringBuilder condition = new StringBuilder();
    if (clientCode != null) {
      condition.append(" and ").append(tableSuffix).append(".client = ").append(namedParameterQuery ? ":clientCode" : '?');
    }

    if (namedParameterQuery) {
      DataLoaderUtils.appendStatusNamedParameters(ignoreStatus, condition, tableSuffix);
    } else {
      DataLoaderUtils.appendStatusParams(ignoreStatus, condition, tableSuffix);
    }

    return condition.toString();
  }

  public static String buildConditionChunk(final String clientCode, final String ignoreStatus, final String tableSuffix, final boolean namedParameterQuery) {
    final StringBuilder condition = new StringBuilder(DataLoaderUtils.buildConditionPartitioner(clientCode, ignoreStatus, tableSuffix, namedParameterQuery));
    if (namedParameterQuery) {
      condition.append(" and ").append(tableSuffix).append(".partitionKey >= :firstIndex ");
      condition.append(" and ").append(tableSuffix).append(".partitionKey <= :lastIndex ");
    } else {
      condition.append(" and ").append(tableSuffix).append(".partitionKey >= ? ");
      condition.append(" and ").append(tableSuffix).append(".partitionKey <= ? ");
    }
    return condition.toString();
  }
}
