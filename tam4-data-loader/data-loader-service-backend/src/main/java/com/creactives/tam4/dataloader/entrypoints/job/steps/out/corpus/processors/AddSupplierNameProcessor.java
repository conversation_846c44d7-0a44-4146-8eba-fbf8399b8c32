package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.FindSupplierName;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Component
public class AddSupplierNameProcessor implements ItemProcessor<Material, Material> {
  private final FindSupplierName findSupplierName;

  @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
  @Override
  public Material process(final Material item) throws Exception {
    final String manufacturer = item.getManufacturer();
    if (StringUtils.isNotBlank(manufacturer)) {
      return Material.from(item)
          .vendorName(findSupplierName.findSupplierName(item.getClient(), item.getManufacturer()))
          .build();

    }
    return item;
  }
}
