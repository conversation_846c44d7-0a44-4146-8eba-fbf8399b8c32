package com.creactives.tam4.dataloader.entrypoints.job.steps.out.addcharateristics;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 7:03 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
public class SendMaterialAddCharacteristicsMessagesJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMaterialAddCharacteristicsMessagesStep step;


  @Bean
  public JobFactory sendMaterialAddCharacteristicsFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: AUSP, MARA")
        .build("send-material-add-characteristics");
  }

}
