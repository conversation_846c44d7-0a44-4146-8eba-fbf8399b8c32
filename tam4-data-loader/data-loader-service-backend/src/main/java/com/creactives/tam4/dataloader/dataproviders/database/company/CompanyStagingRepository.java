package com.creactives.tam4.dataloader.dataproviders.database.company;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyStagingRepository extends PagingAndSortingRepository<CompanyEntity, Long> {

  @Deprecated
  CompanyEntity findByClientAndCompanyCode(String client, String companyCode);

  CompanyEntity findByClientAndCompanyCodeAndCurrencyKeyNotNull(String client, String companyCode);

  List<CompanyEntity> findByClientAndCompanyCodeIn(String client, List<String> companyCode);

}
