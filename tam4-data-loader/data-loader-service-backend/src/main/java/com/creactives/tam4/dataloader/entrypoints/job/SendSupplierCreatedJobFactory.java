package com.creactives.tam4.dataloader.entrypoints.job;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.supplierdefinition.SendSupplierDefinitionAddOrUpdateStep;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
@RequiredArgsConstructor
@Component
public class SendSupplierCreatedJobFactory {
  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendSupplierDefinitionAddOrUpdateStep step;


  @Bean
  public JobFactory sendSupplierCreatedFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: LFA1")
        .build("send-suppliers");
  }

}
