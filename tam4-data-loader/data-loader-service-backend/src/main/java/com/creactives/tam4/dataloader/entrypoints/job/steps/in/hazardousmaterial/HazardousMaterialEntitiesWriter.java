package com.creactives.tam4.dataloader.entrypoints.job.steps.in.hazardousmaterial;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial.HazardousMaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial.HazardousMaterialStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class HazardousMaterialEntitiesWriter implements ItemWriter<HazardousMaterialEntity> {

  private final HazardousMaterialStagingRepository hazardousMaterialStagingRepository;

  @Override
  public void write(final List<? extends HazardousMaterialEntity> list) throws Exception {
    for (final HazardousMaterialEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    hazardousMaterialStagingRepository.saveAll(list);
  }
}
