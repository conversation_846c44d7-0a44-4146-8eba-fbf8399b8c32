package com.creactives.tam4.dataloader.entrypoints.job;

import com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload.MassiveFileUploadImportStep;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class MassiveFileUploadJob {
  public static final String LOAD_MASSIVE_FILE_UPLOAD = "load-massive-file-upload";
  private final MassiveFileUploadImportStep materialDataImportStep;


  public MassiveFileUploadJob(final MassiveFileUploadImportStep materialDataImportStep) {
    this.materialDataImportStep = materialDataImportStep;
  }

  @Bean(name = LOAD_MASSIVE_FILE_UPLOAD)
  @StepScope
  public Job configureJob(final JobBuilderFactory factory) {
    log.info("Configuring job");
    return factory.get(LOAD_MASSIVE_FILE_UPLOAD)
        .incrementer(new RunIdIncrementer())
        .start(materialDataImportStep.configureStep())
        .build();
  }
}
