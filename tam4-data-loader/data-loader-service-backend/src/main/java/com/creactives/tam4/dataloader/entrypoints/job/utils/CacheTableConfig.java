package com.creactives.tam4.dataloader.entrypoints.job.utils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CacheTableConfig implements Serializable {
  private String tableName;
  private String query;
  private List<String> keyFields;
  private List<String> inputParamNames;
}
