package com.creactives.tam4.dataloader.entrypoints.job.steps.in.bulkfileupload;

import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.CopyJobParametersListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.materials.commands.MasterdataChangesMessage;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.stereotype.Component;

import java.io.File;

@RequiredArgsConstructor
@Log4j2
@Component
public class MassiveFileUploadImportStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final MaterialSnapshotProvider materialSnapshotProvider;
  private final MassiveFileUploadItemProcessor itemProcessor;
  private final ChunkWriterListener chunkWriterListener;
  private final CopyJobParametersListener copyJobParametersListener;

  @Override
  public Step configureStep() {
    return stepBuilderFactory.get("massive-file-upload-staging")
        .listener(copyJobParametersListener)
        .<File, MasterdataChangesMessage>chunk(10000)
        .listener(chunkWriterListener)
        .reader(reader())
        .processor(itemProcessor)
        .writer(materialSnapshotProvider)
        .listener(new StatisticsListener())
        .build();
  }

  @SneakyThrows
  public AttachmentsItemReader<File> reader() {
    return new AttachmentsItemReader<>();
  }
}

