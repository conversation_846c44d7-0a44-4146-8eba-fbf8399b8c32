package com.creactives.tam4.dataloader.core.exceptions;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class UnsupportedLanguageException extends RuntimeException {

  public UnsupportedLanguageException(final String languageCode) {
    super("dataloader.errors.unsupported-language-code" + languageCode);
    log.warn("Skipped information for not supported language - language code : '{}'", languageCode);
  }

}
