package com.creactives.tam4.dataloader.dataproviders.database.material;

import com.google.common.base.Splitter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created on 6/19/2019 7:38 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialKey {

  private String client;
  private String materialCode;

  public MaterialKey(final CharSequence businessKey) {
    final List<String> businessKeyComponents = Splitter.on("/").splitToList(businessKey);
    this.client = businessKeyComponents.get(0);
    this.materialCode = businessKeyComponents.get(1);
  }

  public String composite() {
    return client + '/' + materialCode;
  }

}
