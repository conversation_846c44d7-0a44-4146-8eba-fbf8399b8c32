package com.creactives.tam4.dataloader.entrypoints.job.steps.in.mrptype;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class MRPTypeEntitiesWriter implements ItemWriter<MRPTypeEntity> {

  private final MRPTypeStagingRepository mrpTypeStagingRepository;

  @Override
  public void write(final List<? extends MRPTypeEntity> list) throws Exception {
    for (final MRPTypeEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    mrpTypeStagingRepository.saveAll(list);
  }
}
