package com.creactives.tam4.dataloader.entrypoints.job.steps.out.globalvaluationcategorydefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.globalvaluationcategoriesdefinitions.GlobalValuationCategoryDefinitionsKeyEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.GlobalValuationCategoryDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class SendGlobalValuationCategoryDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final CalculateAggregateForGlobalValuationCategoryDefinition calculateAggregateForGlobalValuationCategoryDefinition;
  private final ConvertAggregateToGlobalValuationCategoryDefinitionRequestItemProcessor convertAggregateToGlobalValuationCategoryDefinitionRequestItemProcessor;
  private final GlobalValuationCategoryDefinitionWriter categoryDefinitionWriter;
  private final GlobalValuationCategoryDefinitionsKeysItemReader categoryKeysItemReader;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-global-valuation-category-definition-add-or-update")
        .<GlobalValuationCategoryDefinitionsKeyEntity, GlobalValuationCategoryDefinitionUpsertRequestMessage>chunk(1000)
        .reader(categoryKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<GlobalValuationCategoryDefinitionsKeyEntity, GlobalValuationCategoryDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForGlobalValuationCategoryDefinition,
                convertAggregateToGlobalValuationCategoryDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<GlobalValuationCategoryDefinitionUpsertRequestMessage>().delegates(List.of(categoryDefinitionWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<GlobalValuationCategoryDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "global_valuation_category_definitions_staging", "global_valuation_category",
        (globalValuationCategoryDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, globalValuationCategoryDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, globalValuationCategoryDefinitionUpsertRequestMessage.getGlobalValuationCategory());
        });
  }

}
