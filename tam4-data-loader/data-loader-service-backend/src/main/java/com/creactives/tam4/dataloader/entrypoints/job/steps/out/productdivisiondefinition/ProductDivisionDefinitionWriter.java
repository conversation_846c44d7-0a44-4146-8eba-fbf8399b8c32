package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.SalesDivisionDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
@RequiredArgsConstructor
@Component
public class ProductDivisionDefinitionWriter implements ItemWriter<SalesDivisionDefinitionUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  @Override
  public void write(final List<? extends SalesDivisionDefinitionUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
