package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementStagingRepository;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Service
@Deprecated
public class SetUnitsOfMeasurementSyncronizedFields implements ItemProcessor<UnitsOfMeasurementEntity, UnitsOfMeasurementEntity> {

  private final UnitsOfMeasurementStagingRepository repository;

  public SetUnitsOfMeasurementSyncronizedFields(final UnitsOfMeasurementStagingRepository repository) {
    this.repository = repository;
  }

  @Override
  @Transactional
  public UnitsOfMeasurementEntity process(final UnitsOfMeasurementEntity unitsOfMeasurementEntity) throws Exception {

    final List<UnitsOfMeasurementEntity> unitsOfMeasurement = repository.findByClientAndUnitsOfMeasurement(unitsOfMeasurementEntity.getClient(), unitsOfMeasurementEntity.getUnitsOfMeasurement());
    for (final UnitsOfMeasurementEntity entity :
        unitsOfMeasurement) {

      if (entity != null) {
        entity.setSynchronizationState(SyncStatus.MESSAGE_SENT.getCode());
        entity.setSynchronizedOn(new Timestamp(System.currentTimeMillis()));
      }
    }
    return unitsOfMeasurementEntity;
  }
}
