package com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ISOUnitsOfMeasureStagingRepository extends PagingAndSortingRepository<ISOUnitOfMeasureEntity, Long> {

  List<ISOUnitOfMeasureEntity> findByClientAndLanguageKeyAndIsoUnitOfMeasureIn(String client, String languageKey, List<String> keys);
}
