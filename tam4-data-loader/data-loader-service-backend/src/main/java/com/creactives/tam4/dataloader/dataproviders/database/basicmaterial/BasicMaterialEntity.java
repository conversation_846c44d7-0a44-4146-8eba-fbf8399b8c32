package com.creactives.tam4.dataloader.dataproviders.database.basicmaterial;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "basic_material_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TWSPR", description = "Check Table for Basic Material Field")
public class BasicMaterialEntity implements ClientEnrichableEntity {

  @Id

  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "basic_material_staging_generator")
  @SequenceGenerator(name = "basic_material_staging_generator", sequenceName = "basic_material_staging_seq", allocationSize = 100000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", description = "Client", referenceTable = "T000")
  private String client;

  @Column(name = "basic_material")
  @FieldDoc(required = true, sapField = "WRKST", description = "Basic Material")
  private String basicMaterial;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
