package com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.SchedulingMarginKeyUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SchedulingMarginKeyWriter implements ItemWriter<SchedulingMarginKeyUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  public SchedulingMarginKeyWriter(final WriteMessageService sendMessages) {
    this.sendMessages = sendMessages;
  }


  @Override
  public void write(final List<? extends SchedulingMarginKeyUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
