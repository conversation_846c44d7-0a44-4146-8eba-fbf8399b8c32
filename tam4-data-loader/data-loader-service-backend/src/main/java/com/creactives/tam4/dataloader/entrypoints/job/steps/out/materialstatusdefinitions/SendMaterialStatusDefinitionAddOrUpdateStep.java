package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.MaterialStatusDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class SendMaterialStatusDefinitionAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertAggregateToMaterialStatusDefinitionRequestItemProcessor convertAggregateToMaterialStatusDefinitionRequestItemProcessor;
  private final CalculateAggregateForMaterialStatusDefinition calculateAggregateForMaterialStatusDefinition;
  private final MaterialStatusDefinitionWriter materialStatusDefinitionWriter;
  private final MaterialStatusDefinitionsKeysItemReader materialStatusDefinitionsKeysItemReader;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-material-status-definition-add-or-update")
        .<MaterialStatusEntity, MaterialStatusDefinitionUpsertRequestMessage>chunk(1000)
        .reader(materialStatusDefinitionsKeysItemReader)
        .processor(new CompositeItemProcessorBuilder<MaterialStatusEntity, MaterialStatusDefinitionUpsertRequestMessage>().delegates(List.of(calculateAggregateForMaterialStatusDefinition,
                convertAggregateToMaterialStatusDefinitionRequestItemProcessor
            )).build()
        )
        .writer(new CompositeItemWriterBuilder<MaterialStatusDefinitionUpsertRequestMessage>().delegates(List.of(materialStatusDefinitionWriter,
                updateStagingTable()
            )).build()
        )
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<MaterialStatusDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "material_status_definitions_staging", "material_status",
        (materialStatusDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, materialStatusDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, materialStatusDefinitionUpsertRequestMessage.getMaterialStatus());
        });

  }
}
