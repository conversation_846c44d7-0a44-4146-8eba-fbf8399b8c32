package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials;

import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GetTotalIgnoredMaterialsUseCase {

  private final GetTotalMaterialsInDatabase getTotalMaterialsInDatabase;

  public DataAnalyseResponse totalIgnoredMaterials(final String client) {
    return DataAnalyseResponse.builder()
        .code("total-ignored-materials")
        .rowCount(getTotalMaterialsInDatabase.totalIgnoredMaterials(client, true))
        .build();
  }
}
