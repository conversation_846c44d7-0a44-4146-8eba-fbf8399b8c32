package com.creactives.tam4.dataloader.entrypoints.job.steps.in.countrydefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountriesDefinitionKeysStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.countrydefinitions.CountryDefinitionsKeyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class CountryDefinitionsEnabledEntitiesWriter implements ItemWriter<CountryDefinitionsKeyEntity> {

  private final CountriesDefinitionKeysStagingRepository countriesDefinitionKeysStagingRepository;

  @Override
  public void write(final List<? extends CountryDefinitionsKeyEntity> list) throws Exception {
    for (final CountryDefinitionsKeyEntity item : list) {
//      System.out.println("________" + item);
//      if (item.getEnabled()) {
      countriesDefinitionKeysStagingRepository.setEnabled(item.getEnabled(), item.getCountry(), item.getClient());
//      System.out.println(countriesDefinitionKeysStagingRepository.findByClientAndCountry(item.getClient(), item.getCountry()));
//        System.out.println("HERE");
//      }
    }
  }
}
