package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions;

import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusDefinitionsEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialstatusdefinitions.MaterialStatusEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AggregateDataForMaterialStatusDefinition {

  private MaterialStatusEntity materialStatusEntity;
  private List<MaterialStatusDefinitionsEntity> materialStatusEntities;
}
