package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@Deprecated
public class SendUnitsOfMeasurementJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendUnitsOfMeasurementAddOrUpdateStep step;

  public SendUnitsOfMeasurementJobFactory(final JobBuilderFactory jobBuilderFactory,
                                          final StepBuilderFactory stepBuilderFactory,
                                          final SendUnitsOfMeasurementAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendUnitsOfMeasurementFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T006")
        .build("send-units-of-measurement");
  }
}
