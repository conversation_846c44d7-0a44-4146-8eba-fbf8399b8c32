package com.creactives.tam4.dataloader.core.entity;

public enum SyncStatus {

  PENDING("pending"),
  MESSAGE_SENT("message-sent"),
  SUCCESS("success"),
  ERROR("error"),
  UNMATCHED("unmatched");

  private final String code;

  SyncStatus(final String code) {
    this.code = code;
  }

  public static SyncStatus findByCode(final String code) {
    for (final SyncStatus value : SyncStatus.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }
    throw new IllegalArgumentException("Enum value not found for code: " + code);
  }

  public String getCode() {
    return code;
  }
}
