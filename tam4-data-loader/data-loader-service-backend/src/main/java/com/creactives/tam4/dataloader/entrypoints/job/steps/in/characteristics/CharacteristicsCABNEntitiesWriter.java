package com.creactives.tam4.dataloader.entrypoints.job.steps.in.characteristics;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn.CharacteristicsCABNEntity;
import com.creactives.tam4.dataloader.dataproviders.database.characteristicscabn.CharacteristicsCABNStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@StepScope
public class CharacteristicsCABNEntitiesWriter implements ItemWriter<CharacteristicsCABNEntity> {

  private final CharacteristicsCABNStagingRepository characteristicsCABNStagingRepository;

  @Override
  public void write(final List<? extends CharacteristicsCABNEntity> list) throws Exception {
    for (final CharacteristicsCABNEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    characteristicsCABNStagingRepository.saveAll(list);
  }
}
