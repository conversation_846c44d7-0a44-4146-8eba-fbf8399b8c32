package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;


import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors.AddMaterialWarehousesProcessor;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import com.creactives.tam4.messaging.PlantKey;
import com.creactives.tam4.messaging.materials.extension.MaterialPlantDetails;
import com.creactives.tam4.messaging.materials.extension.MaterialStorageLocationDetails;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Created on 6/14/2019 10:39 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@StepScope
public class MaterialPlantItemProcessorV3 implements ItemProcessor<AggregateDataForMaterialExtension, MaterialApplyExtensionLoadMessage> {
  private final MaterialCodeNormalizer materialCodeNormalizer;

  private static String getCountryCode(final Map<String, PlantEntity> plants, final String plantId) {
    if (MapUtils.isEmpty(plants)) {
      return null;
    }

    if (plants.get(plantId) == null) {
      return null;
    }

    return plants.get(plantId).getCountryKey();
  }

  private static List<MaterialStorageLocationDetails> createStorageLocations(final Collection<MaterialStorageLocationEntity> materialStorageLocationEntities) {
    if (materialStorageLocationEntities == null) {
      return Collections.emptyList();
    }
    return materialStorageLocationEntities.stream()
        .map(materialStorageLocationEntity -> MaterialStorageLocationDetails.builder()
            .id(materialStorageLocationEntity.getId())
            .storageLocation(materialStorageLocationEntity.getStorageLocation())
            .valuatedUnrestrictedUseStock(materialStorageLocationEntity.getValuatedUnrestrictedUseStock())
            .stockInTransfer(materialStorageLocationEntity.getStockInTransfer())
            .stockInQualityInspection(materialStorageLocationEntity.getStockInQualityInspection())
            .blockedStock(materialStorageLocationEntity.getBlockedStock())
            .storageBin(materialStorageLocationEntity.getStorageBin())
            .deletionFlag(AddMaterialWarehousesProcessor.SAP_CHECKED.equals(materialStorageLocationEntity.getFlagMaterialForDeletionAtStorageLocationLevel()))
            .build()
        )
        .collect(Collectors.toList());
  }

  @Override
  public MaterialApplyExtensionLoadMessage process(final AggregateDataForMaterialExtension aggregate) {
    final List<MaterialPlantEntity> materialPlantEntities = aggregate.getMaterialPlantEntities();
    final List<MaterialStorageLocationEntity> materialStorageLocationEntities = aggregate.getMaterialStorageLocationEntities();

    final Map<String, List<MaterialStorageLocationEntity>> groups = materialStorageLocationEntities.stream()
        .collect(Collectors.groupingBy(MaterialStorageLocationEntity::getPlantCode));

    final List<MaterialPlantDetails> plantDetails = materialPlantEntities.stream()
        .map(materialPlantEntity ->
            MaterialPlantDetails.builder()
                .id(materialPlantEntity.getId())
                .plantKey(new PlantKey(materialPlantEntity.getPlantId(), materialPlantEntity.getClient()))
                .countryCode(getCountryCode(aggregate.getPlants(), materialPlantEntity.getPlantId()))
                .status(materialPlantEntity.getStatus())
                .leadTimeInDays(materialPlantEntity.getLeadTimeInDays())
                .validFromDate(materialPlantEntity.getValidFromDate())
                .deletionFlag(materialPlantEntity.isDeletionFlag())
                .lotSize(materialPlantEntity.getLotSize())
                .minLotSize(materialPlantEntity.getMinLotSize())
                .seriable(materialPlantEntity.getSeriable())
                .reorderPoint(materialPlantEntity.getReorderPoint())
                .safetyStock(materialPlantEntity.getSafetyStock())
                .minimumSafetyStock(materialPlantEntity.getMinimumSafetyStock())
                .maximumStockLevel(materialPlantEntity.getMaximumStockLevel())
                .mrpType(materialPlantEntity.getMrpType())
                .mrpGroup(materialPlantEntity.getMrpGroup())
                .purchasingGroup(materialPlantEntity.getPurchasingGroup())
                .followUpMaterialCode(materialPlantEntity.getFollowUpMaterial())
                .logisticsHandlingGroup(materialPlantEntity.getLogisticsHandlingGroup())
                .mrpController(materialPlantEntity.getMrpController())
                .inHouseProductionTime(materialPlantEntity.getInHouseProductionTime())
                .individualColl(materialPlantEntity.getIndividualColl())
                .goodReceiptProcessingTimeInDays(materialPlantEntity.getGoodsReceiptProcessingTimeInDays())
                .controlKeyForQM(materialPlantEntity.getControlKeyForQualityManagement())
                .certificateType(materialPlantEntity.getCertificateType())
                .batchManagementFlag(materialPlantEntity.isBatchManagementRequirement())
                .schedulingMarginKeyForFloats(materialPlantEntity.getSchedulingMarginKeyForFloats())
                .storageLocations(createStorageLocations(groups.get(materialPlantEntity.getPlantId())))
                .intrastatCode(materialPlantEntity.getIntrastatCode())
                .controlCodeConsumptionTaxesForeignTrade(materialPlantEntity.getControlCodeConsumptionTaxesForeignTrade())
                .materialCFOPCategory(materialPlantEntity.getMaterialCFOPCategory())
                .periodIndicator(materialPlantEntity.getPeriodIndicator())
                .specialProcurementType(materialPlantEntity.getSpecialProcurementType())
                .checkingGroupAvailabilityCheck(materialPlantEntity.getCheckingGroupAvailabilityCheck())
                .fixedLotSize(materialPlantEntity.getFixedLotSize())
                .maximumLotSize(materialPlantEntity.getMaximumLotSize())
                .orderingCosts(materialPlantEntity.getOrderingCosts())
                .storageCostsIndicator(materialPlantEntity.getStorageCostsIndicator())
                .roundingValueForPurchaseOrderQuantity(materialPlantEntity.getRoundingValueForPurchaseOrderQuantity())
                .unitOfIssue(materialPlantEntity.getUnitOfIssue())
                .procurementType(materialPlantEntity.getProcurementType())
                .strategyGroup(materialPlantEntity.getStrategyGroup())
                .criticalPart(materialPlantEntity.getCriticalPart())
                .effectiveOutDate(materialPlantEntity.getEffectiveOutDate())
                .countryOfOrigin(materialPlantEntity.getCountryOfOrigin())
                .loadingGroup(materialPlantEntity.getLoadingGroup())
                .planningTimeFence(materialPlantEntity.getPlanningTimeFence())
                .consumptionMode(materialPlantEntity.getConsumptionMode())
                .consumptionPeriodBackward(materialPlantEntity.getConsumptionPeriodBackward())
                .consumptionPeriodForward(materialPlantEntity.getConsumptionPeriodForward())
                .build()
        )
        .collect(Collectors.toList());

    final MaterialApplyExtensionLoadMessage e = MaterialApplyExtensionLoadMessage.builder()
        .client(aggregate.getMaterialEntity().getClient())
        .materialCode(materialCodeNormalizer.removeZeroes(aggregate.getMaterialEntity().getMaterialCode()))
        .materialPlantDetails(plantDetails)
        .build();
    return e;
  }
}
