package com.creactives.tam4.dataloader.entrypoints.job.steps.out.laboratory;


import com.creactives.tam4.dataloader.dataproviders.database.laboratory.LaboratoryEntity;
import com.creactives.tam4.messaging.materials.commands.LaboratoryUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class ConvertEntityToLaboratoryRequestItemProcessor implements ItemProcessor<LaboratoryEntity, LaboratoryUpsertRequestMessage> {


  @Override
  public LaboratoryUpsertRequestMessage process(final LaboratoryEntity laboratoryEntity) throws Exception {
    return LaboratoryUpsertRequestMessage.builder()
        .client(laboratoryEntity.getClient())
        .laboratory(laboratoryEntity.getLaboratory())
        .build();
  }
}
