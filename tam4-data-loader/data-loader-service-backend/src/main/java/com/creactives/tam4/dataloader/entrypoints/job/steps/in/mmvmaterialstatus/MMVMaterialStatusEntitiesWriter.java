package com.creactives.tam4.dataloader.entrypoints.job.steps.in.mmvmaterialstatus;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mmvmaterialStatus.MMVMaterialStatusEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mmvmaterialStatus.MMVMaterialStatusStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class MMVMaterialStatusEntitiesWriter implements ItemWriter<MMVMaterialStatusEntity> {

  private final MMVMaterialStatusStagingRepository mmvMaterialStatusStagingRepository;

  @Override
  public void write(final List<? extends MMVMaterialStatusEntity> itemsList) throws Exception {
    for (final MMVMaterialStatusEntity entity :
        itemsList) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    mmvMaterialStatusStagingRepository.saveAll(itemsList);

  }
}
