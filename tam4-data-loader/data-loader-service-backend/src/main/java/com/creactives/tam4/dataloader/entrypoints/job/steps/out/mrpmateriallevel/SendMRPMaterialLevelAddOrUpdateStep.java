package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.MRPMaterialLevelUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SendMRPMaterialLevelAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToMRPMaterialLevelRequestItemProcessor itemProcessor;
  private final MRPMateriaLevelItemReader itemReader;
  private final MRPMaterialLevelWriter writer;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-mrp-material-level-add-or-update")
        .<MRPMaterialLevelEntity, MRPMaterialLevelUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(itemReader)
        .processor(itemProcessor)
        .writer(new CompositeItemWriterBuilder<MRPMaterialLevelUpsertRequestMessage>()
            .delegates(List.of(writer,
                updateStagingTable("mrp_material_level_staging", List.of("plant", "mrp_group"))
            )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<MRPMaterialLevelUpsertRequestMessage> updateStagingTable(final String tableName, final List<String> matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getPlant());
      ps.setString(5, mcrm.getMrpGroup());
    });
  }
}
