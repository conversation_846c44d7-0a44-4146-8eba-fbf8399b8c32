package com.creactives.tam4.dataloader.dataproviders.database.company;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "company_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T001", description = "Company Codes")
public class CompanyEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "company_staging_generator")
  @SequenceGenerator(name = "company_staging_generator", sequenceName = "company_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", description = "Client", referenceTable = "T000")
  private String client;

  @Column(name = "company_code")
  @FieldDoc(required = true, sapField = "BUKRS", description = "Company Code")
  private String companyCode;

  @Column(name = "company_name")
  @FieldDoc(required = true, sapField = "BUTXT", description = "Name of Company Code or Company")
  private String companyName;

  @Column(name = "city")
  @FieldDoc(sapField = "ORT01", description = "City")
  private String city;

  @Column(name = "country_key")
  @FieldDoc(required = true, sapField = "LAND1", description = "Country Key")
  private String countryKey;

  @Column(name = "currency_key")
  @FieldDoc(required = true, sapField = "WAERS", description = "Currency Key")
  private String currencyKey;

  @Column(name = "language")
  @FieldDoc(sapField = "SPRAS", description = "Language Key")
  private String language;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
