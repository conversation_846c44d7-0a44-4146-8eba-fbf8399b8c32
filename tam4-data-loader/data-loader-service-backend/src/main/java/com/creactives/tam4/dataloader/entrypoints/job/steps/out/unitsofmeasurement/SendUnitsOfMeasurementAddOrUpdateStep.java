package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;

import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.materials.commands.UnitsOfMeasurementUpsertRequestMessage;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.stereotype.Component;

@Component
@Deprecated //message should not be sent
public class SendUnitsOfMeasurementAddOrUpdateStep implements StepConfigurer {


  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToUnitsOfMeasurementRequestItemProcessor convertEntityToUnitsOfMeasurementRequestItemProcessor;
  private final UnitsOfMeasurementItemReader unitsOfMeasurementItemReader;
  private final UnitsOfMeasurementWriter unitsOfMeasurementWriter;
  private final ChunkWriterListener chunkWriterListener;

  public SendUnitsOfMeasurementAddOrUpdateStep(final StepBuilderFactory stepBuilderFactory,
                                               final ConvertEntityToUnitsOfMeasurementRequestItemProcessor convertEntityToUnitsOfMeasurementRequestItemProcessor,
                                               final UnitsOfMeasurementItemReader unitsOfMeasurementItemReader,
                                               final UnitsOfMeasurementWriter unitsOfMeasurementWriter,
                                               final ChunkWriterListener chunkWriterListener) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.convertEntityToUnitsOfMeasurementRequestItemProcessor = convertEntityToUnitsOfMeasurementRequestItemProcessor;
    this.unitsOfMeasurementItemReader = unitsOfMeasurementItemReader;
    this.unitsOfMeasurementWriter = unitsOfMeasurementWriter;
    this.chunkWriterListener = chunkWriterListener;
  }

  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-units-of-measurement-add-or-update")
        .<UnitsOfMeasurementEntity, UnitsOfMeasurementUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(unitsOfMeasurementItemReader)
        .processor(convertEntityToUnitsOfMeasurementRequestItemProcessor)
        .writer(unitsOfMeasurementWriter)
        .listener(new StatisticsListener())
        .build();
  }
}
