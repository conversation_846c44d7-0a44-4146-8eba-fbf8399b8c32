package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialstatusdefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendMaterialStatusDefinitionJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMaterialStatusDefinitionAddOrUpdateStep step;

  public SendMaterialStatusDefinitionJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                final StepBuilderFactory stepBuilderFactory,
                                                final SendMaterialStatusDefinitionAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendMaterialStatusDefinitionFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T141T, T141T_Enabled")
        .build("send-material-status-definition");
  }
}
