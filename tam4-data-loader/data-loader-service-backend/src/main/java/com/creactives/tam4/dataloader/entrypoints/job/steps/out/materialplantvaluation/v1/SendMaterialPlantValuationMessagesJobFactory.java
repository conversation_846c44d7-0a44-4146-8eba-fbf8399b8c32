package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Created on 9/1/2019 7:03 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Component
public class SendMaterialPlantValuationMessagesJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMaterialPlantValuationRequestedMessagesStep step;

  public SendMaterialPlantValuationMessagesJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                      final StepBuilderFactory stepBuilderFactory,
                                                      final SendMaterialPlantValuationRequestedMessagesStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }


  @Bean
  public JobFactory sendMaterialPlantValuationsFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
//        .setDescription("Tables: MARA, MBEW, PO_History, Consumption_Data, T001W")
        .setDescription("Deprecated")
        .build("send-material-plant-valuations");
  }

}
