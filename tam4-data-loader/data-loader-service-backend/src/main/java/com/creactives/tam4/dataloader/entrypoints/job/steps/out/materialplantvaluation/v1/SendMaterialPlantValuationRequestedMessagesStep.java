package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1;

import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.messaging.materials.load.MaterialPlantValuationUpsertLoadMessage;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.integration.async.AsyncItemProcessor;
import org.springframework.batch.integration.async.AsyncItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemProcessorBuilder;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

/**
 * Created on 6/14/2019 3:09 PM Project - CreactivesSuite Copyright (c) Creactives SPA - All rights
 * reserved
 *
 * <AUTHOR>
 */
@Component
public class SendMaterialPlantValuationRequestedMessagesStep implements StepConfigurer {

  private static final String SEND_MATERIAL_PLANT_VALUATIONS = "send-material-plant-valuations";
  private final StepBuilderFactory stepBuilderFactory;
  private final MaterialPlantValuationItemProcessor itemProcessor;
  private final MaterialPlantValuationUpsertRequestProvider itemWriter;
  private final ChunkWriterListener chunkWriterListener;
  private final OptimizedMaterialPlantValuationReader optimizedMaterialPlantValuationReader;
  private final TaskExecutor taskExecutor;


  @Value("${data-loader.send-job.materials-chunk-size:1000}")
  private int chunkSize;

  public SendMaterialPlantValuationRequestedMessagesStep(final StepBuilderFactory stepBuilderFactory,
                                                         final MaterialPlantValuationItemProcessor itemProcessor,
                                                         final MaterialPlantValuationUpsertRequestProvider itemWriter,
                                                         final ChunkWriterListener chunkWriterListener,
                                                         final OptimizedMaterialPlantValuationReader optimizedMaterialPlantValuationReader,
                                                         final TaskExecutor taskExecutor
  ) {
    this.stepBuilderFactory = stepBuilderFactory;
    this.itemProcessor = itemProcessor;
    this.itemWriter = itemWriter;
    this.chunkWriterListener = chunkWriterListener;
    this.optimizedMaterialPlantValuationReader = optimizedMaterialPlantValuationReader;
    this.taskExecutor = taskExecutor;
  }

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get(SEND_MATERIAL_PLANT_VALUATIONS)
        .<AggregateDataForMaterialPlantValuation, Future<MaterialPlantValuationUpsertLoadMessage>>chunk(chunkSize)
        .listener(chunkWriterListener)
        .reader(optimizedMaterialPlantValuationReader)
        .processor(buildAsyncProcessor())
        .writer(buildAsyncWriter())
        .listener(new StatisticsListener())
        .build();
  }

  private AsyncItemWriter<MaterialPlantValuationUpsertLoadMessage> buildAsyncWriter() {
    final AsyncItemWriter<MaterialPlantValuationUpsertLoadMessage> materialAsyncItemWriter = new AsyncItemWriter<>();
    materialAsyncItemWriter.setDelegate(new CompositeItemWriterBuilder<MaterialPlantValuationUpsertLoadMessage>().delegates(List.of(itemWriter
    )).build());
    return materialAsyncItemWriter;
  }

  private AsyncItemProcessor<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage> buildAsyncProcessor() {
    final AsyncItemProcessor<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage> materialEntityFutureAsyncItemProcessor = new AsyncItemProcessor<>();
    materialEntityFutureAsyncItemProcessor.setDelegate(new CompositeItemProcessorBuilder<AggregateDataForMaterialPlantValuation, MaterialPlantValuationUpsertLoadMessage>().delegates(List.of(itemProcessor
    )).build());
    materialEntityFutureAsyncItemProcessor.setTaskExecutor(taskExecutor);
    return materialEntityFutureAsyncItemProcessor;
  }

  //FIXME: GBRRRT - Add table update!
}
