package com.creactives.tam4.dataloader.entrypoints.job.steps.in.plant.language;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlantLanguageUpdater implements Serializable {
  private String plantCode;
  private String client;
  private String languageCode;
}
