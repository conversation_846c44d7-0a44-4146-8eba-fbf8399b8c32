package com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.processors;

import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.corpus.Material;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
@RequiredArgsConstructor
@Component
public class AddShortDescriptionsItemProcessor implements ItemProcessor<Material, Material> {
  private final ShortDescriptionStagingRepository shortDescriptionStagingRepository;


  @Override
  public Material process(final Material item) throws Exception {
    final List<ShortDescriptionEntity> descriptions = shortDescriptionStagingRepository.findByClientAndMaterial(item.getClient(), item.getMaterialCode());
    final Map<String, String> shortDescriptions = new HashMap<>();
    for (final ShortDescriptionEntity description : descriptions) {
      final String lang = description.getLanguage();
      final String text = description.getDescription();
      shortDescriptions.put(lang, text);
    }
    return Material.from(item)
        .shortDescription(shortDescriptions)
        .build();
  }
}
