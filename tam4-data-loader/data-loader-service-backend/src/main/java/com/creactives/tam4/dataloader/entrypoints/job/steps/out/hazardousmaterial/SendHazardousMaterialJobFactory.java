package com.creactives.tam4.dataloader.entrypoints.job.steps.out.hazardousmaterial;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendHazardousMaterialJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendHazardousMaterialAddOrUpdateStep step;


  public SendHazardousMaterialJobFactory(final JobBuilderFactory jobBuilderFactory,
                                         final StepBuilderFactory stepBuilderFactory,
                                         final SendHazardousMaterialAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendHazardousMaterialFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: MGEF")
        .build("send-hazardous-material");
  }
}
