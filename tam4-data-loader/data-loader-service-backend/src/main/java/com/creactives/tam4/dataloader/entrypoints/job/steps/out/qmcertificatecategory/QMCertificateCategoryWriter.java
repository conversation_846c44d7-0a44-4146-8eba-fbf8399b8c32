package com.creactives.tam4.dataloader.entrypoints.job.steps.out.qmcertificatecategory;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.QMCertificateCategoryUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QMCertificateCategoryWriter implements ItemWriter<QMCertificateCategoryUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  private final QMCertificateCategoryStagingRepository qmCertificateCategoryStagingRepository;

  public QMCertificateCategoryWriter(final WriteMessageService sendMessages, final QMCertificateCategoryStagingRepository qmCertificateCategoryStagingRepository) {
    this.sendMessages = sendMessages;

    this.qmCertificateCategoryStagingRepository = qmCertificateCategoryStagingRepository;
  }

  @Override
  public void write(final List<? extends QMCertificateCategoryUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);
  }
}
