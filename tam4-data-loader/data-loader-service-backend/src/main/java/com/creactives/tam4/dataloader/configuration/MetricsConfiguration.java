package com.creactives.tam4.dataloader.configuration;

import com.codahale.metrics.Metric;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.MetricSet;
import com.codahale.metrics.jvm.GarbageCollectorMetricSet;
import com.codahale.metrics.jvm.MemoryUsageGaugeSet;
import com.google.common.collect.Maps;
import com.ryantenney.metrics.spring.config.annotation.EnableMetrics;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableMetrics
public class MetricsConfiguration implements InitializingBean {
  private final MetricReporterService reporterService;

  public MetricsConfiguration(final MetricRegistry metricRegistry, final MetricReporterService reporterService) {
    this.reporterService = reporterService;
    metricRegistry.registerAll(new GarbageCollectorMetricSet());
    metricRegistry.registerAll(new Filtered(new MemoryUsageGaugeSet()));
  }

  @Override
  public void afterPropertiesSet() throws Exception {
    reporterService.start(600000000, TimeUnit.SECONDS);
  }

  private class Filtered implements MetricSet {

    private final MemoryUsageGaugeSet memoryUsageGaugeSet;

    public Filtered(final MemoryUsageGaugeSet memoryUsageGaugeSet) {
      this.memoryUsageGaugeSet = memoryUsageGaugeSet;
    }

    @Override
    public Map<String, Metric> getMetrics() {
      final Map<String, Metric> map = memoryUsageGaugeSet.getMetrics();
      return Maps.filterEntries(map, stringMetricEntry -> !StringUtils.startsWith(stringMetricEntry.getKey(), "pools") &&
          !StringUtils.startsWith(stringMetricEntry.getKey(), "non-heap"));

    }
  }
}
