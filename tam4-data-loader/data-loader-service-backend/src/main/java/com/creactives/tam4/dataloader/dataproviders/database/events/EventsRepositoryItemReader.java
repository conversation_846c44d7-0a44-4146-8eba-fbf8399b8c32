package com.creactives.tam4.dataloader.dataproviders.database.events;

import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialcreated.SimpleJdbcBatchItemReader;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class EventsRepositoryItemReader extends SimpleJdbcBatchItemReader<Long, EventsEntity> {

  private final String GROUP_AN_ORDER_BY = " ORDER BY es.id";

  private final JdbcTemplate jdbcTemplate;
  private final int fetchSize = 10000;

  @Override
  protected Long getKey(final EventsEntity entity) {
    return entity.getId();
  }

  @Override
  protected List<EventsEntity> queryForFirstPage() {
    return jdbcTemplate.query(" SELECT es.* FROM event_staging es WHERE es.synchronized_on is null"
        + GROUP_AN_ORDER_BY + " limit ?", new BeanPropertyRowMapper<>(EventsEntity.class), fetchSize);
  }

  @Override
  protected List<EventsEntity> queryAfterId(final Long id) {
    return jdbcTemplate.query(" SELECT es.* FROM event_staging es WHERE es.synchronized_on is null and es.id > ?"
        + GROUP_AN_ORDER_BY + " limit ?", new BeanPropertyRowMapper<>(EventsEntity.class), id, fetchSize);
  }
}
