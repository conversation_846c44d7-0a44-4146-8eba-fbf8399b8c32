package com.creactives.tam4.dataloader.entrypoints.job.steps.out.hazardousmaterial;

import com.creactives.tam4.dataloader.configuration.MessagingConfiguration;
import com.creactives.tam4.dataloader.dataproviders.database.hazardousmaterial.HazardousMaterialStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.messaging.WriteMessageService;
import com.creactives.tam4.messaging.materials.commands.HazardousMaterialUpsertRequestMessage;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HazardousMaterialWriter implements ItemWriter<HazardousMaterialUpsertRequestMessage> {

  private final WriteMessageService sendMessages;

  private final HazardousMaterialStagingRepository hazardousMaterialStagingRepository;

  public HazardousMaterialWriter(final WriteMessageService sendMessages, final HazardousMaterialStagingRepository hazardousMaterialStagingRepository) {
    this.sendMessages = sendMessages;

    this.hazardousMaterialStagingRepository = hazardousMaterialStagingRepository;
  }

  @Override
  public void write(final List<? extends HazardousMaterialUpsertRequestMessage> list) throws Exception {
    sendMessages.send(MessagingConfiguration.DATA_LOADER_EXCHANGE_NAME, list);

  }
}
