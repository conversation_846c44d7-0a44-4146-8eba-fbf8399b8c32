package com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginEntity;
import com.creactives.tam4.dataloader.entrypoints.job.ChunkWriterListener;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.SchedulingMarginKeyUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
@Log4j2
public class SendSchedulingMarginKeyAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final SchedulingMarginKeyWriter writer;
  private final SchedulingMarginKeyItemReader reader;
  private final ConvertEntitytoSchedulingMarginKeyRequestItemPRocessor processor;
  private final ChunkWriterListener chunkWriterListener;
  private final DataSource dataSource;

  @Override
  public TaskletStep configureStep() {
    return stepBuilderFactory.get("send-sched-margin-key-add-or-update")
        .<SchedulingMarginEntity, SchedulingMarginKeyUpsertRequestMessage>chunk(1000)
        .listener(chunkWriterListener)
        .reader(reader)
        .processor(processor)
        .writer(new CompositeItemWriterBuilder<SchedulingMarginKeyUpsertRequestMessage>()
            .delegates(List.of(writer,
                updateStagingTable("plant_currency", List.of("plant", "sched_marg_key"))
            )).build()
        )
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<SchedulingMarginKeyUpsertRequestMessage> updateStagingTable(final String tableName, final List<String> matCodeColumn) {

    return DataLoaderUtils.updateStagingTable(dataSource, tableName, matCodeColumn, (mcrm, ps) -> {
      ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
      ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
      ps.setString(3, mcrm.getClient());
      ps.setString(4, mcrm.getPlant());
      ps.setString(5, mcrm.getSchedulingMarginKey());
    });
  }
}

