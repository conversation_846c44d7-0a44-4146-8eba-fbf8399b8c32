package com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey;

import com.creactives.tam4.dataloader.dataproviders.database.marginkey.SchedulingMarginEntity;
import com.creactives.tam4.messaging.SchedulingMarginKeyUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class ConvertEntitytoSchedulingMarginKeyRequestItemPRocessor implements ItemProcessor<SchedulingMarginEntity, SchedulingMarginKeyUpsertRequestMessage> {

  @Override
  public SchedulingMarginKeyUpsertRequestMessage process(final SchedulingMarginEntity item) throws Exception {
    return SchedulingMarginKeyUpsertRequestMessage.builder()
        .client(item.getClient())
        .plant(item.getPlant())
        .schedulingMarginKey(item.getSchedMargKey())
        .enabled(item.getEnabled()).build();
  }
}
