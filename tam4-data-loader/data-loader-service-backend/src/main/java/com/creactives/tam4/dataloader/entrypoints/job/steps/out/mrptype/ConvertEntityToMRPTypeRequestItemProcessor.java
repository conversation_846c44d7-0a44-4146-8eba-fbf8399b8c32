package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrptype;

import com.creactives.tam4.dataloader.dataproviders.database.mrptype.MRPTypeEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition.MRPTypeDefinitionEntity;
import com.creactives.tam4.dataloader.dataproviders.database.mrptypedefinition.MRPTypeDefinitionStagingRepository;
import com.creactives.tam4.messaging.materials.commands.MRPTypeUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@RequiredArgsConstructor
@Component
public class ConvertEntityToMRPTypeRequestItemProcessor implements ItemProcessor<MRPTypeEntity, MRPTypeUpsertRequestMessage> {

  private final MRPTypeDefinitionStagingRepository mrpTypeDefinitionStagingRepository;

  @Override
  public MRPTypeUpsertRequestMessage process(final MRPTypeEntity mrpTypeEntity) throws Exception {
    final List<MRPTypeDefinitionEntity> mrpTypeDefinitionEntities = mrpTypeDefinitionStagingRepository.findByClientAndMrpType(mrpTypeEntity.getClient(), mrpTypeEntity.getMrpType());
    return MRPTypeUpsertRequestMessage.builder()
        .client(mrpTypeEntity.getClient())
        .consumptionForecast(mrpTypeEntity.getConsumptionForecast())
        .deliveryScheduleForStockTransferRequisition(mrpTypeEntity.getDeliveryScheduleForStockTransferRequisition())
        .deliveryScheduleForStockTransportAgreement(mrpTypeEntity.getDeliveryScheduleForStockTransportAgreement())
        .firmingType(mrpTypeEntity.getFirmingType())
        .forecastIndicator(mrpTypeEntity.getForecastIndicator())
        .materialStagingRequirements(mrpTypeEntity.getMateriaStagingRequirements())
        .mrpForecast(mrpTypeEntity.getMrpForecast())
        .mrpProcedure(mrpTypeEntity.getMrpProcedure())
        .mrpType(mrpTypeEntity.getMrpType())
        .networkReservation(mrpTypeEntity.getNetworkReservation())
        .oderReservation(mrpTypeEntity.getNetworkReservation())
        .orderDelayScheduled(mrpTypeEntity.getOrderDelayScheduled())
        .planningMethod(mrpTypeEntity.getPlanningMethod())
        .planRegularly(mrpTypeEntity.getPlanRegularly())
        .reduceForecast(mrpTypeEntity.getReduceForecast())
        .reorderPoint(mrpTypeEntity.getReorderPoint())
        .reorderPointWithRequirements(mrpTypeEntity.getReorderPointWithRequirements())
        .rollForward(mrpTypeEntity.getRollForward())
        .safetyStock(mrpTypeEntity.getSafetyStock())
        .screenSequence(mrpTypeEntity.getScreenSequence())
        .timePhasedPlanning(mrpTypeEntity.getTimePhasedPlanning())
        .descriptions(mrpTypeDefinitionEntities.stream()
            .filter(mrpTypeDefinitionEntity -> mrpTypeDefinitionEntity.getLanguage() != null)
            .collect(Collectors.toMap(o -> o.getLanguage(),
                MRPTypeDefinitionEntity::getDescription
            )))
        .build();
  }
}
