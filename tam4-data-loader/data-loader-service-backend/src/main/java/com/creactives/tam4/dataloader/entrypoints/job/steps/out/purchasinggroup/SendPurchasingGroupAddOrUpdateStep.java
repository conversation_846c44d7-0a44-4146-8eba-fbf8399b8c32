package com.creactives.tam4.dataloader.entrypoints.job.steps.out.purchasinggroup;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.purchasinggroup.PurchasingGroupEntity;
import com.creactives.tam4.dataloader.entrypoints.job.StatisticsListener;
import com.creactives.tam4.dataloader.entrypoints.job.StepConfigurer;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import com.creactives.tam4.messaging.materials.commands.PurchasingGroupDefinitionUpsertRequestMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.support.builder.CompositeItemWriterBuilder;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
@RequiredArgsConstructor
@Component
@Log4j2
public class SendPurchasingGroupAddOrUpdateStep implements StepConfigurer {

  private final StepBuilderFactory stepBuilderFactory;
  private final ConvertEntityToPurchasingGroupResuestItemProcessor convertEntityToPurchasingGroupRequestItemProcessor;
  private final PurchasingGroupWriter purchasingGroupWriter;
  private final PurchasingGroupItemReader purchasingGroupItemReader;
  private final DataSource dataSource;


  @Override
  public TaskletStep configureStep() {

    return stepBuilderFactory.get("send-purchasing-group-definition-add-or-update")
        .<PurchasingGroupEntity, PurchasingGroupDefinitionUpsertRequestMessage>chunk(1000)
        .reader(purchasingGroupItemReader)
        .processor(convertEntityToPurchasingGroupRequestItemProcessor)
        .writer(new CompositeItemWriterBuilder<PurchasingGroupDefinitionUpsertRequestMessage>().delegates(List.of(purchasingGroupWriter,
            updateStagingTable()
        )).build())
        .listener(new StatisticsListener())
        .build();
  }

  private JdbcBatchItemWriter<PurchasingGroupDefinitionUpsertRequestMessage> updateStagingTable() {
    return DataLoaderUtils.updateStagingTable(dataSource, "purchasing_group_staging", "purchasing_group",
        (purchasingGroupDefinitionUpsertRequestMessage, ps) -> {
          ps.setString(1, SyncStatus.MESSAGE_SENT.getCode());
          ps.setTimestamp(2, new Timestamp((new Date()).getTime()));
          ps.setString(3, purchasingGroupDefinitionUpsertRequestMessage.getClient());
          ps.setString(4, purchasingGroupDefinitionUpsertRequestMessage.getIndustrySector());
        });
  }

}
