package com.creactives.tam4.dataloader.entrypoints.job.steps.out.logisticshandlinggroupdefinitions;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendLogisticsHandlingGroupDefinitionsJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendLogisticsHandlingGroupDefinitionsAddOrUpdateStep step;

  public SendLogisticsHandlingGroupDefinitionsJobFactory(final JobBuilderFactory jobBuilderFactory,
                                                         final StepBuilderFactory stepBuilderFactory,
                                                         final SendLogisticsHandlingGroupDefinitionsAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendLogisticsHandlingGroupDefinitionsFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: TLOGT, TLOGT_Enabled")
        .build("send-logistics-handling-group-definitions");
  }
}
