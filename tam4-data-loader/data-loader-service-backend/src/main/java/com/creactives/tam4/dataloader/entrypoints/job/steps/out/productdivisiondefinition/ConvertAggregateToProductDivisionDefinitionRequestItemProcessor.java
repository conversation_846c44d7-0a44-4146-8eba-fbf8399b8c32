package com.creactives.tam4.dataloader.entrypoints.job.steps.out.productdivisiondefinition;

import com.creactives.tam4.dataloader.dataproviders.database.productdivision.ProductDivisionEntity;
import com.creactives.tam4.messaging.materials.commands.SalesDivisionDefinitionUpsertRequestMessage;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class ConvertAggregateToProductDivisionDefinitionRequestItemProcessor implements ItemProcessor<AggregateDataForProductDivisionDefinition, SalesDivisionDefinitionUpsertRequestMessage> {

  @Override
  public SalesDivisionDefinitionUpsertRequestMessage process(final AggregateDataForProductDivisionDefinition aggregateDataForProductDivisionDefinition) throws Exception {
    return SalesDivisionDefinitionUpsertRequestMessage.builder()
        .client(aggregateDataForProductDivisionDefinition.getProductDivisionKeyEntity().getClient())
        .salesDivision(aggregateDataForProductDivisionDefinition.getProductDivisionKeyEntity().getDivision())
        .descriptions(aggregateDataForProductDivisionDefinition.getProductDivisionEntities().stream()
            .filter(productDivisionEntity -> productDivisionEntity.getLanguage() != null)
            .collect(Collectors.toMap(ProductDivisionEntity::getLanguage,
                ProductDivisionEntity::getDescription
            ))
        )
        .build();
  }
}
