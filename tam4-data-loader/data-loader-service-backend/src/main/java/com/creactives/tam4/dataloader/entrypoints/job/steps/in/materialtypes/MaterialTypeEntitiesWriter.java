package com.creactives.tam4.dataloader.entrypoints.job.steps.in.materialtypes;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesEntity;
import com.creactives.tam4.dataloader.dataproviders.database.materialtypes.MaterialTypesStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
@StepScope
public class MaterialTypeEntitiesWriter implements ItemWriter<MaterialTypesEntity> {

  private final MaterialTypesStagingRepository materialTypesStagingRepository;

  @Override
  public void write(final List<? extends MaterialTypesEntity> list) throws Exception {
    for (final MaterialTypesEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    materialTypesStagingRepository.saveAll(list);
  }
}
