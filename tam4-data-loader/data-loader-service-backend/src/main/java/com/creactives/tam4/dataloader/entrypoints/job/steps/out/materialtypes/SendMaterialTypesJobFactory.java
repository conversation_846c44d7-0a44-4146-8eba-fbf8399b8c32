package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialtypes;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SendMaterialTypesJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendMaterialTypesAddOrUpdateStep step;

  public SendMaterialTypesJobFactory(final JobBuilderFactory jobBuilderFactory, final StepBuilderFactory stepBuilderFactory, final SendMaterialTypesAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendMaterialTypesFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: T134, T134T_Enabled")
        .build("send-material-types");
  }
}
