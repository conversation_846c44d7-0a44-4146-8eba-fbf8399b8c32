package com.creactives.tam4.dataloader.dataproviders.database.plant;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 6/11/2019 4:06 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Table(name = "materials_plant_data_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "MARC", description = "Hazardous materials")
public class MaterialPlantEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "materials_plant_data_staging_generator")
  @SequenceGenerator(name = "materials_plant_data_staging_generator", sequenceName = "materials_plant_data_staging_seq", allocationSize = 100000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_code")
  @FieldDoc(required = true, sapField = "MATNR", referenceTable = "MARA", description = "Material Number")
  private String materialCode;

  @Column(name = "plant_id")
  @FieldDoc(required = true, sapField = "WERKS", referenceTable = "T001W", description = "Plant")
  private String plantId;

  @Column(name = "deletion_flag")
  @FieldDoc(sapField = "LVORM", description = "Flag Material for Deletion at Plant Level")
  private boolean deletionFlag;

  @Column(name = "status")
  @FieldDoc(sapField = "PSTAT", description = "Maintenance status")
  private String status;

  @Column(name = "seriable")
  @FieldDoc(sapField = "SERNP", referenceTable = "T377P", description = "Serial Number Profile")
  private String seriable;

  @Column(name = "mrp_type")
  @FieldDoc(sapField = "DISMM", referenceTable = "T438A", description = "MRP Type")
  private String mrpType;

  @Column(name = "lead_time_in_days")
  @FieldDoc(sapField = "WZEIT", description = "Total replenishment lead time (in workdays)")
  private Integer leadTimeInDays;

  @Column(name = "lot_size")
  @FieldDoc(sapField = "LOSGR", description = "Lot Size for Product Costing")
  private String lotSize;

  @Column(name = "reorder_point")
  @FieldDoc(sapField = "MINBE", description = "Reorder Point")
  private BigDecimal reorderPoint;

  @Column(name = "safety_stock")
  @FieldDoc(sapField = "EISBE", description = "Safety Stock")
  private BigDecimal safetyStock;

  @Column(name = "maximum_stock_level")
  @FieldDoc(sapField = "MABST", description = "Maximum Stock Level")
  private BigDecimal maximumStockLevel;

  @Column(name = "mrp_group")
  @FieldDoc(sapField = "DISGR", description = "MRP Group")
  private String mrpGroup;

  @Column(name = "follow_up_material")
  @FieldDoc(sapField = "NFMAT", referenceTable = "MARA", description = "Follow-Up Material")
  private String followUpMaterial;

  @Column(name = "logistics_handling_group")
  @FieldDoc(sapField = "LOGGR", referenceTable = "TLOG", description = "Logistics handling group for workload calculation")
  private String logisticsHandlingGroup;

  @Column(name = "min_lot_size")
  @FieldDoc(sapField = "MINLS", description = "Minimum lot size for Supply Demand Match")
  private BigDecimal minLotSize;

  @Column(name = "minimum_safety_stock")
  @FieldDoc(sapField = "EISLO", description = "Minimum Safety Stock")
  private BigDecimal minimumSafetyStock;

  @Column(name = "valid_from_date")
  @FieldDoc(sapField = "MMSTD", description = "Date from which the plant-specific material status is valid")
  private Long validFromDate;

  @Column(name = "purchasing_group")
  @FieldDoc(sapField = "EKGRP", referenceTable = "T024", description = "Purchasing Group")
  private String purchasingGroup;

  @Column(name = "mrp_controller")
  @FieldDoc(sapField = "DISPO", referenceTable = "T024D", description = "MRP Controller (Materials Planner)")
  private String mrpController;

  @Column(name = "in_house_production_time")
  @FieldDoc(sapField = "DZEIT", description = "In-house production time")
  private Integer inHouseProductionTime;

  @Column(name = "individual_coll")
  @FieldDoc(sapField = "SBDKZ", description = "Dependent requirements ind. for individual and coll. reqmts")
  private String individualColl;

  @Column(name = "goods_receipt_processing_time_in_days")
  @FieldDoc(sapField = "WEBAZ", description = "Goods Receipt Processing Time in Days")
  private Integer goodsReceiptProcessingTimeInDays;

  @Column(name = "control_key_for_quality_management")
  @FieldDoc(sapField = "SSQSS", description = "Control Key for Quality Management in Procurement")
  private String controlKeyForQualityManagement;

  @Column(name = "certificate_type")
  @FieldDoc(sapField = "QZGTP", description = "Certificate Type")
  private String certificateType;

  @Column(name = "batch_management_requirement")
  @FieldDoc(sapField = "XCHPF", description = "Batch management requirement indicator")
  private boolean batchManagementRequirement;

  @Column(name = "scheduling_margin_key_for_floats")
  @FieldDoc(sapField = "FHORI", referenceTable = "T436A", description = "Scheduling Margin Key for Floats")
  private String schedulingMarginKeyForFloats;

  @Column(name = "profit_center")
  @FieldDoc(sapField = "PRCTR", description = "Profit Center")
  private String profitCenter;

  @Column(name = "plant_old_material_number")
  @FieldDoc(sapField = "BISMT", description = "Plant Old Material Number")
  private String plantOldMaterialNumber;

  @Column(name = "lot_size_for_product_costing")
  @FieldDoc(sapField = "LOSGR", description = "Lot Size For Product Costing")
  private BigDecimal lotSizeForProductCosting;

  @Column(name = "intrastat_code")
  private String intrastatCode;

  @Column(name = "control_code_consumption")
  private String controlCodeConsumptionTaxesForeignTrade;

  @Column(name = "material_cfop_category")
  private String materialCFOPCategory;

  @Column(name = "period_indicator")
  @FieldDoc(sapField = "PERKZ", description = "Period Indicator")
  private String periodIndicator;

  @Column(name = "special_procurement_type")
  private String specialProcurementType;

  @Column(name = "checking_group_availability_check")
  private String checkingGroupAvailabilityCheck;

  @Column(name = "avato_log_system")
  private String avatoLogSystem;

  @Column(name = "avato_version")
  private String avatoVersion;

  @Column(name = "avato_last_sync")
  private String avatoLastSync;

  @Column(name = "avato_sequence")
  private String avatoSequence;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;

  @Column(name = "fixed_lot_size")
  @FieldDoc(sapField = "BSTFE", description = "Fixed lot Size for Product Costing")
  private BigDecimal fixedLotSize;

  @Column(name = "maximum_lot_size")
  @FieldDoc(sapField = "BSTMA", description = "Maximum Lot Size")
  private BigDecimal maximumLotSize;

  @Column(name = "ordering_costs")
  @FieldDoc(sapField = "LOSFX", description = "Ordering Costs")
  private BigDecimal orderingCosts;

  @Column(name = "storage_costs_indicator")
  @FieldDoc(sapField = "LAGPR", description = "Storage Costs Indicator")
  private String storageCostsIndicator;

  @Column(name = "rounding_value_for_purchase_order_quantity")
  @FieldDoc(sapField = "BSTRF", description = "Rounding Value for Purchase Order Quantity")
  private BigDecimal roundingValueForPurchaseOrderQuantity;

  @Column(name = "unit_of_issue")
  @FieldDoc(sapField = "AUSME", description = "Unit of Issue")
  private String unitOfIssue;

  @Column(name = "procurement_type")
  @FieldDoc(sapField = "BESKZ", description = "Procurement Type")
  private String procurementType;

  @Column(name = "strategy_group")
  @FieldDoc(sapField = "STRGR", description = "Strategy Group")
  private String strategyGroup;

  @Column(name = "critical_part")
  @FieldDoc(sapField = "KZKRI", description = "Critical Part")
  private String criticalPart;

  @Column(name = "effective_out_date")
  @FieldDoc(sapField = "AUSDT", description = "Effective Out Date")
  private Long effectiveOutDate;


  @Column(name = "country_of_origin")
  @FieldDoc(sapField = "HERKL", description = "Country of Origin")
  private String countryOfOrigin;

  @Column(name = "loading_group")
  @FieldDoc(sapField = "LADGR", description = "Loading Group")
  private String loadingGroup;

  @Column(name = "planning_time_fence")
  @FieldDoc(sapField = "FXHOR", description = "Planning Time Fence")
  private String planningTimeFence;

  @Column(name = "consumption_mode")
  @FieldDoc(sapField = "VRMOD", description = "Consumption Mode")
  private String consumptionMode;

  @Column(name = "consumption_period_backward")
  @FieldDoc(sapField = "VINT1", description = "Consumption Period Backward")
  private String consumptionPeriodBackward;

  @Column(name = "consumption_period_forward")
  @FieldDoc(sapField = "VINT2", description = "Consumption Period Forward")
  private String consumptionPeriodForward;

}  //TODO supportsMultipleValuations

