package com.creactives.tam4.dataloader.dataproviders.database.valuationclass;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "valuation_class_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ValuationClassesEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "valuation_class_staging_generator")
  @SequenceGenerator(name = "valuation_class_staging_generator", sequenceName = "valuation_class_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "valuation_class")
  private String valuationClass;

  @Column(name = "account_category_reference")
  private String accountCategoryReference;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
