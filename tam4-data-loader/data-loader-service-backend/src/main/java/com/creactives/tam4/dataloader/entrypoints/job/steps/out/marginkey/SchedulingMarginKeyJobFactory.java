package com.creactives.tam4.dataloader.entrypoints.job.steps.out.marginkey;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SchedulingMarginKeyJobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final SendSchedulingMarginKeyAddOrUpdateStep step;

  public SchedulingMarginKeyJobFactory(final JobBuilderFactory jobBuilderFactory, final StepBuilderFactory stepBuilderFactory, final SendSchedulingMarginKeyAddOrUpdateStep step) {
    this.jobBuilderFactory = jobBuilderFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.step = step;
  }

  @Bean
  public JobFactory sendMarginKeyFactory() {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .build("send-margin-key");
  }
}
