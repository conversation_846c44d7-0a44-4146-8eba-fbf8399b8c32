package com.creactives.tam4.dataloader.dataproviders.database.standardapi;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Repository
public interface StandardApiReceivedPayloadsRepository extends JpaRepository<StandardApiReceivedPayloadsEntity, PayloadEmbeddedId> {

  @Query("select count(*) > 0 from StandardApiReceivedPayloadsEntity sarp where sarp.payloadEmbeddedId.messageType = :messageType and sarp.payloadEmbeddedId.key = :key and sarp.payloadEmbeddedId.hash = :hash")
  boolean existsByMessageTypeAndKeyAndHash(@Param("messageType") String messageType, @Param("key") String key, @Param("hash") String hash);

  @Query("select sarp from StandardApiReceivedPayloadsEntity sarp where sarp.payloadEmbeddedId.messageType = :messageType and sarp.payloadEmbeddedId.key = :key and sarp.payloadEmbeddedId.hash = :hash")
  StandardApiReceivedPayloadsEntity findByMessageTypeAndKeyAndHash(@Param("messageType") String messageType, @Param("key") String key, @Param("hash") String hash);

  @Query("select sarp from StandardApiReceivedPayloadsEntity sarp where sarp.payloadEmbeddedId in (:keyHashPairList)")
  List<StandardApiReceivedPayloadsEntity> findByMessageTypeAndTupleOfKeyAndHash(@Param("keyHashPairList") List<PayloadEmbeddedId> keyHashPairList
  );

  default Map<String, StandardApiReceivedPayloadsEntity> findMapByMessageTypeAndTupleOfKeyAndHash(final List<PayloadEmbeddedId> keyHashPairList
  ) {
    final List<StandardApiReceivedPayloadsEntity> byMessageTypeAndTupleOfKeyAndHash = findByMessageTypeAndTupleOfKeyAndHash(keyHashPairList);

    return byMessageTypeAndTupleOfKeyAndHash.stream()
        .collect(Collectors.toMap(standardApiReceivedPayloadsEntity ->
                standardApiReceivedPayloadsEntity.getPayloadEmbeddedId().extractPayloadKey()
            ,
            Function.identity()
        ));
  }


}
