package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PlantRow implements Serializable {


  @JsonAlias("id")
  private long id;

  @JsonAlias("client")
  private String client;

  @JsonAlias("plant")
  private String plant;

  @JsonAlias("description")
  private String description;

  @JsonAlias("valuation_area")
  private String valuationArea;

  @JsonAlias("vendor_number")
  private String vendorNumber;

  @JsonAlias("second_description")
  private String secondDescription;

  @JsonAlias("city")
  private String city;

  @JsonAlias("country_key")
  private String countryKey;

  @JsonAlias("region")
  private String region;

  @JsonAlias("language")
  private String language;

  @JsonAlias("created_on")
  private Timestamp createdOn;

  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;

  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;

  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @JsonAlias("synchronization_state")
  private String synchronizationState;

  @JsonAlias("enabled")
  private boolean enabled;

  @JsonAlias("erp_sequence_num")
  private Long erpSequenceNum;

  @JsonAlias("language_code")
  private String languageCode;

}
