package com.creactives.tam4.dataloader.dataproviders.database.marginkey;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchedulingMarginKeyStagingRepository extends PagingAndSortingRepository<SchedulingMarginEntity, Long> {

  List<SchedulingMarginEntity> findAllByClientAndPlantAndSchedMargKeyIn(String client, String plant, List<String> schedMargKeys);

  Page<SchedulingMarginEntity> findAllBySynchronizationStateAndClient(String synchronizationState, String client, Pageable pageable);

  Page<SchedulingMarginEntity> findAllByClient(String client, Pageable pageable);

  Page<SchedulingMarginEntity> findAllBySynchronizationState(String synchronizationState, Pageable pageable);
}
