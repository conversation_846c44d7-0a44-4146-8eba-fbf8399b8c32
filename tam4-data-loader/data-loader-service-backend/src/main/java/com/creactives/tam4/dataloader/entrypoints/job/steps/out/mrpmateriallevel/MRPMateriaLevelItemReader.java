package com.creactives.tam4.dataloader.entrypoints.job.steps.out.mrpmateriallevel;

import com.creactives.tam4.dataloader.dataproviders.database.mrpmateriallevel.MRPMaterialLevelEntity;
import com.creactives.tam4.dataloader.entrypoints.job.utils.DataLoaderUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JpaCursorItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

@Component
@StepScope
public class MRPMateriaLevelItemReader extends JpaCursorItemReader<MRPMaterialLevelEntity> {

  public MRPMateriaLevelItemReader(final EntityManagerFactory entityManagerFactory,
                                   @Value("#{jobParameters['clientCode']}") final String clientCode,
                                   @Value("#{jobParameters['ignore_status']}") final String ignoreStatus
  ) {


    setSaveState(false);
    setEntityManagerFactory(entityManagerFactory);

    final StringBuilder query = new StringBuilder();
    final Map<String, Object> params = new HashMap<>();

    query.append(" SELECT m FROM MRPMaterialLevelEntity m ");
    query.append(" where 1=1 ");

    if (clientCode != null) {
      query.append(" and m.client = :clientCode ");
      params.put("clientCode", clientCode);
    }

    DataLoaderUtils.appendStatusJpa(ignoreStatus, query, params, "m");

    query.append(" order by id asc ");

    setParameterValues(params);
    setQueryString(query.toString());
  }

}
