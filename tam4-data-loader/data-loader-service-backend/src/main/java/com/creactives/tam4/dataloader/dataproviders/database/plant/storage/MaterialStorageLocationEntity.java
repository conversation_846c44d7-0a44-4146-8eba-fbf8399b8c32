package com.creactives.tam4.dataloader.dataproviders.database.plant.storage;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created on 6/19/2019 11:24 AM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Table(name = "material_storage_locations_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode()
@ClassDoc(sapTable = "MARD", description = "Descriptions of Material Status from MM/PP View")
public class MaterialStorageLocationEntity implements ClientEnrichableEntity {

  @Id

  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "material_storage_locations_staging_generator")
  @SequenceGenerator(name = "material_storage_locations_staging_generator", sequenceName = "material_storage_locations_staging_seq", allocationSize = 100000)
  private long id;

  @Column
  @FieldDoc(required = true, sapField = "MANDT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "material_code")
  @FieldDoc(required = true, sapField = "MATNR", referenceTable = "MARA", description = "Material Number")
  private String materialCode;

  @Column(name = "plant_code")
  @FieldDoc(required = true, sapField = "WERKS", referenceTable = "MARC", description = "Plant")
  private String plantCode;

  @Column(name = "storage_location")
  @FieldDoc(required = true, sapField = "LGORT", referenceTable = "T001L", description = "Storage Location")
  private String storageLocation;

  @Column(name = "valuated_unrestricted_use_stock")
  @FieldDoc(sapField = "LABST", description = "Valuated Unrestricted-Use Stock")
  private BigDecimal valuatedUnrestrictedUseStock;

  @Column(name = "stock_in_transfer")
  @FieldDoc(sapField = "UMLME", description = "Stock in transfer (from one storage location to another)")
  private BigDecimal stockInTransfer;

  @Column(name = "stock_in_quality_inspection")
  @FieldDoc(sapField = "INSME", description = "Stock in Quality Inspection")
  private BigDecimal stockInQualityInspection;

  @Column(name = "blocked_stock")
  @FieldDoc(sapField = "SPEME", description = "Blocked Stock")
  private BigDecimal blockedStock;

  @Column(name = "storage_bin")
  @FieldDoc(sapField = "LGPBE", description = "Storage Bin")
  private String storageBin;

  @Column(name = "maintenance_status")
  @FieldDoc(sapField = "PSTAT", description = "Maintenance status")
  private String maintenanceStatus;

  @Column(name = "flag_material_for_deletion_at_storage_location_level")
  @FieldDoc(sapField = "LVORM", description = "Flag Material for Deletion at Storage Location Level")
  private String flagMaterialForDeletionAtStorageLocationLevel;

  @Column(name = "avato_log_system")
  private String avatoLogSystem;

  @Column(name = "avato_version")
  private String avatoVersion;

  @Column(name = "avato_last_sync")
  private String avatoLastSync;

  @Column(name = "avato_sequence")
  private String avatoSequence;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
