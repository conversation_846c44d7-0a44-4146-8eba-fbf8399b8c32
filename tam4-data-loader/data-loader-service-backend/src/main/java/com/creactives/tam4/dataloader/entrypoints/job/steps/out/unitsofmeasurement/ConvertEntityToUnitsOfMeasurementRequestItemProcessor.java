package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;


import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.messaging.materials.commands.UnitsOfMeasurementUpsertRequestMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;


@Component
@Log4j2
@Deprecated
public class ConvertEntityToUnitsOfMeasurementRequestItemProcessor implements ItemProcessor<UnitsOfMeasurementEntity, UnitsOfMeasurementUpsertRequestMessage> {


  public ConvertEntityToUnitsOfMeasurementRequestItemProcessor() {

  }

  @Override
  public UnitsOfMeasurementUpsertRequestMessage process(final UnitsOfMeasurementEntity unitsOfMeasurementEntity) throws Exception {
    return UnitsOfMeasurementUpsertRequestMessage.builder()
        .client(unitsOfMeasurementEntity.getClient())
        .unitsOfMeasurement(unitsOfMeasurementEntity.getUnitsOfMeasurement())
        .build();
  }
}
