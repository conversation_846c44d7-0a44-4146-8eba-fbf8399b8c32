package com.creactives.tam4.dataloader.dataproviders.database.unitofmeasure;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "iso_unit_of_measure_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "T006J", description = "ISO Codes for Unit of Measure Texts")
public class ISOUnitOfMeasureEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "iso_unit_of_measure_staging_generator")
  @SequenceGenerator(name = "iso_unit_of_measure_staging_generator", sequenceName = "iso_unit_of_measure_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(required = true, sapField = "CLIENT", referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "iso_unit_of_measurement")
  @FieldDoc(required = true, sapField = "ISOCODE", referenceTable = "T006I", description = "ISO code for unit of measurement")
  private String isoUnitOfMeasure;

  @Column(name = "iso_measurement")
  @FieldDoc(sapField = "ISOTXT", description = "ISO codes for measurement unit names")
  private String isoMeasurement;

  @Column(name = "language_key")
  @FieldDoc(required = true, sapField = "LANGU", description = "Language Key")
  private String languageKey;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
