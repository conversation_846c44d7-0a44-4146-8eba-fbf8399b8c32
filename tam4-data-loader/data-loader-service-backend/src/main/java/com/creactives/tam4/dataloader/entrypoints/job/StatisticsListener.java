package com.creactives.tam4.dataloader.entrypoints.job;

import lombok.extern.log4j.Log4j2;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;


/**
 * Created on 6/12/2019 4:09 PM
 * Project - CreactivesSuite
 * Copyright (c) Creactives SPA - All rights reserved
 *
 * <AUTHOR>
 */
@Log4j2
public class StatisticsListener implements StepExecutionListener {

  private LocalDateTime start;

  @Override
  public void beforeStep(final StepExecution stepExecution) {
    start = LocalDateTime.now();
  }

  @Override
  public ExitStatus afterStep(final StepExecution stepExecution) {
    try {
      log.info("------------------------------------------------------------------------------------");
      log.info("StepName={}", stepExecution.getStepName());
      log.info("Summary={}", stepExecution.getSummary());
      log.info("CommitCount={}", stepExecution.getCommitCount());
      log.info("ReadCount={}, ReadSkipCount={}", stepExecution.getReadCount(), stepExecution.getReadSkipCount());
      log.info("Empty lines={}", stepExecution.getReadSkipCount());
      log.info("WriteCount={}, WriteSkipCount={}", stepExecution.getWriteCount(), stepExecution.getWriteSkipCount());
      log.info("StartTime={}, LastUpdated={}, EndTime={}", stepExecution.getStartTime(), stepExecution.getLastUpdated(), stepExecution.getEndTime());
      printElapsed();
      log.info("ExitStatus={}", stepExecution.getExitStatus());
      log.info("FailureExceptions={}", stepExecution.getFailureExceptions());
      log.info("------------------------------------------------------------------------------------");
    } catch (final Throwable t) {
      log.error("Error while printing stats", t);
    }
    return null;
  }

  private void printElapsed() {
    final LocalDateTime end = LocalDateTime.now();
    final long hours = start.until(end, ChronoUnit.HOURS);
    start = start.plusHours(hours);

    final long minutes = start.until(end, ChronoUnit.MINUTES);
    start = start.plusMinutes(minutes);

    final long seconds = start.until(end, ChronoUnit.SECONDS);
    log.info("Elapsed={} hours, {} minutes, {} seconds", hours, minutes, seconds);
  }
}
