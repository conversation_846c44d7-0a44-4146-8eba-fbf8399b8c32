package com.creactives.tam4.dataloader.entrypoints.job.steps.in.qmcertificatecategory;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryEntity;
import com.creactives.tam4.dataloader.dataproviders.database.qmcertificatecategory.QMCertificateCategoryStagingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@StepScope
public class QMCertificateCategoryEntitiesWriter implements ItemWriter<QMCertificateCategoryEntity> {

  private final QMCertificateCategoryStagingRepository qmCertificateCategoryStagingRepository;

  @Override
  public void write(final List<? extends QMCertificateCategoryEntity> list) throws Exception {
    for (final QMCertificateCategoryEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    qmCertificateCategoryStagingRepository.saveAll(list);
  }
}
