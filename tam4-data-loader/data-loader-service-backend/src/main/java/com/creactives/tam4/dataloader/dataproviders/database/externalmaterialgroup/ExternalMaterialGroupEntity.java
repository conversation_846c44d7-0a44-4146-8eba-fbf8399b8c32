package com.creactives.tam4.dataloader.dataproviders.database.externalmaterialgroup;

import com.creactives.tam4.dataloader.core.annotations.ClassDoc;
import com.creactives.tam4.dataloader.core.annotations.FieldDoc;
import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "external_material_group_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ClassDoc(sapTable = "TWEW", description = "External Material Groups")
public class ExternalMaterialGroupEntity implements ClientEnrichableEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "external_material_group_staging_generator")
  @SequenceGenerator(name = "external_material_group_staging_generator", sequenceName = "external_material_group_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  @FieldDoc(sapField = "MANDT", required = true, referenceTable = "T000", description = "Client")
  private String client;

  @Column(name = "external_material_group")
  @FieldDoc(sapField = "EXTWG", required = true, description = "External Material Group")
  private String externalMaterialGroup;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "enabled")
  private Boolean enabled;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
