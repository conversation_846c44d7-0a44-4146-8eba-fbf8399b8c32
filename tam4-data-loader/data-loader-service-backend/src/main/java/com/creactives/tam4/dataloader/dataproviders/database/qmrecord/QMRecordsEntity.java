package com.creactives.tam4.dataloader.dataproviders.database.qmrecord;

import com.creactives.tam4.dataloader.dataproviders.database.ClientEnrichableEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Table(name = "qm_record_staging")
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class QMRecordsEntity implements ClientEnrichableEntity {

  @Id

  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "qm_record_staging_generator")
  @SequenceGenerator(name = "qm_record_staging_generator", sequenceName = "qm_record_staging_seq", allocationSize = 1000)
  private long id;

  @Column(name = "client")
  private String client;

  @Column(name = "internal_counter")
  private String internalCounter;

  @Column(name = "material_number")
  private String materialNumber;

  @Column(name = "plant")
  private String plant;

  @Column(name = "vendor_account_number")
  private String vendorAccountNumber;

  @Column(name = "valid_date")
  private String validDate;

  @Column(name = "release_quantity_active")
  private String releaseQuantityActive;

  @Column(name = "base_unit_of_measure")
  private String baseUnitOfMeasure;

  @Column(name = "released_material_quantity")
  private String releasedMaterialQuantity;

  @Column(name = "material_quantity_order")
  private String materialQuantityOrder;

  @Column(name = "blocking_version")
  private String blockingVersion;

  @Column(name = "language_key")
  private String languageKey;

  @Column(name = "blocked_function")
  private String blockedfunction;

  @Column(name = "inspection_type")
  private String inspectiontype;

  @Column(name = "language")
  private String language;

  @Column(name = "created_on")
  private Timestamp createdOn;

  @Column(name = "last_modified_on")
  private Timestamp lastModifiedOn;

  @Column(name = "synchronized_on")
  private Timestamp synchronizedOn;

  @Column(name = "synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @Column(name = "synchronization_state")
  private String synchronizationState;

  @Column(name = "api_sent")
  private Boolean apiSent = Boolean.FALSE;
}
