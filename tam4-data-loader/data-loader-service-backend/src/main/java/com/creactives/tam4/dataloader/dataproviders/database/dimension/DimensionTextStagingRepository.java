package com.creactives.tam4.dataloader.dataproviders.database.dimension;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DimensionTextStagingRepository extends PagingAndSortingRepository<DimensionTextEntity, Long> {

  List<DimensionTextEntity> findByClientAndLanguageKeyAndDimensionKeyIn(String client, String languageKey, List<String> dimensionKeys);
}
