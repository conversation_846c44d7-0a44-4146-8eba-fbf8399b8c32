package com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.noshorttextmaterials;

import com.creactives.tam4.dataloader.core.usecase.incorrectmaterials.totalmaterials.DetectIncorrectMaterialsInDatabase;
import com.creactives.tam4.dataloader.entrypoints.rest.DataAnalyseResponse;
import org.springframework.stereotype.Service;

@Service
public class DetectMaterialsWithNoShortTextUseCase {

  private final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase;

  public DetectMaterialsWithNoShortTextUseCase(final DetectIncorrectMaterialsInDatabase detectIncorrectMaterialsInDatabase) {
    this.detectIncorrectMaterialsInDatabase = detectIncorrectMaterialsInDatabase;
  }

  public DataAnalyseResponse materialsWithNoShortText(final String client) {
    return DataAnalyseResponse.builder()
        .code("no-short-text")
        .rowCount(detectIncorrectMaterialsInDatabase.detectMaterialsWithNoShortText(client))
        .build();
  }
}
