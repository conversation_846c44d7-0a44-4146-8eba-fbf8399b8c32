package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Log4j2
public class CalculateAggregateForPlantDefinition implements ItemProcessor<PlantEntity, AggregateDataForPlantDefinition> {

  private final PlantCurrencyRepository plantCurrencyRepository;
  private final PlantsAdditionalLanguagesStagingRepository plantsAdditionalLanguagesStagingRepository;

  @NotNull
  private static Predicate<String> isNotAlreadyThePlantLanguage(final String plantLanguage) {
    return additionalLanguage -> !Objects.equals(plantLanguage, additionalLanguage);
  }

  @Override
  public AggregateDataForPlantDefinition process(final PlantEntity plantEntity) throws Exception {
    final PlantCurrencyEntity plantCurrencyEntity = plantCurrencyRepository.findByClientAndPlant(plantEntity.getClient(), plantEntity.getPlant());
    final Set<PlantsAdditionalLanguagesStagingEntity> languages = plantsAdditionalLanguagesStagingRepository.findByClientAndPlant(plantEntity.getClient(), plantEntity.getPlant());
    return AggregateDataForPlantDefinition.builder()
        .plantEntity(plantEntity)
        .currencyEntity(plantCurrencyEntity)
        .additionalLanguages(getDistinctAdditionalLanguages(plantEntity, languages))
        .build();

  }

  @NotNull
  private Set<String> getDistinctAdditionalLanguages(final PlantEntity plantEntity, final Set<PlantsAdditionalLanguagesStagingEntity> languages) {
    return languages.stream().map(PlantsAdditionalLanguagesStagingEntity::getLanguageCode)
        .filter(isNotAlreadyThePlantLanguage(plantEntity.getLanguage()))
        .collect(Collectors.toSet());
  }
}
