package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.creactives.tam4.dataloader.entrypoints.job.SimpleJobBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.JobFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class SendMaterialPlantValuationV2JobFactory {

  private final JobBuilderFactory jobBuilderFactory;
  private final StepBuilderFactory stepBuilderFactory;

  @Bean(SendMaterialPlantValuationV2Constants.JOB_SEND_MATERIAL_PLANT_VALUATION)
  public JobFactory sendMaterialApplyExtensionRequestedV4Job(@Qualifier(SendMaterialPlantValuationV2Constants.STEP_SEND_MATERIAL_PLANT_VALUATION) final Step step) {
    return new SimpleJobBuilder(jobBuilderFactory, stepBuilderFactory)
        .loadStep(step)
        .setDescription("Tables: MARA, MBEW, PO_History, Consumption_Data, T001W")
        .build("send-material-plant-valuations-v2");
  }
}
