package com.creactives.tam4.dataloader.entrypoints.job.steps.in.industrysectordefinitions.enabled;

import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorEntity;
import com.creactives.tam4.dataloader.dataproviders.database.industrysectordefinitions.IndustrySectorsDefinitionKeysStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Log4j2
public class IndustrySectorsDefinitionEnabledEntitiesWriter implements ItemWriter<IndustrySectorEntity> {

  private final IndustrySectorsDefinitionKeysStagingRepository industrySectorsDefinitionKeysStagingRepository;

  @Override
  public void write(final List<? extends IndustrySectorEntity> list) throws Exception {
    for (final IndustrySectorEntity entity : list) {
      if (entity.getEnabled()) {
        industrySectorsDefinitionKeysStagingRepository.setEnabled(entity.getEnabled(), entity.getIndustrySector(), entity.getClient());
      }
    }
  }
}
