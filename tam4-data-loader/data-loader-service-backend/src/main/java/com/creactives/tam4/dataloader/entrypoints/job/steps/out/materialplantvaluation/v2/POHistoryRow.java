package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v2;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class POHistoryRow implements Serializable {

  @JsonAlias("id")
  private long id;

  @JsonAlias("client")
  private String client;

  @JsonAlias("material_code")
  private String materialCode;

  @JsonAlias("ordered_quantity")
  private BigDecimal orderedQuantity;

  @JsonAlias("plant_code")
  private String plantCode;

  @JsonAlias("price")
  private String price;

  @JsonAlias("ordered_amount")
  private BigDecimal orderedAmount;

  @JsonAlias("currency")
  private String currency;

  @JsonAlias("created_on")
  private Timestamp createdOn;

  @JsonAlias("last_modified_on")
  private Timestamp lastModifiedOn;

  @JsonAlias("synchronized_on")
  private Timestamp synchronizedOn;

  @JsonAlias("synchronization_confirmed_on")
  private Timestamp synchronizationConfirmedOn;

  @JsonAlias("synchronization_state")
  private String synchronizationState;
}
