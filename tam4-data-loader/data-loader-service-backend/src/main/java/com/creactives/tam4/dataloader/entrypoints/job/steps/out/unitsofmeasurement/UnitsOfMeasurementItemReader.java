package com.creactives.tam4.dataloader.entrypoints.job.steps.out.unitsofmeasurement;

import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementEntity;
import com.creactives.tam4.dataloader.dataproviders.database.unitsofmeasurement.UnitsOfMeasurementStagingRepository;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Deprecated
public class UnitsOfMeasurementItemReader extends RepositoryItemReader<UnitsOfMeasurementEntity> {

  public UnitsOfMeasurementItemReader(final UnitsOfMeasurementStagingRepository unitsOfMeasurementStagingRepository) {
    this.setRepository(unitsOfMeasurementStagingRepository);
    this.setPageSize(100);
    this.setMethodName("findAll");

    final Map<String, Sort.Direction> sorts = Map.of("id", Sort.Direction.ASC);
    this.setSort(sorts);
  }


}
