package com.creactives.tam4.dataloader.entrypoints.job.steps.in.company;

import com.creactives.tam4.dataloader.core.entity.SyncStatus;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyTextEntity;
import com.creactives.tam4.dataloader.dataproviders.database.company.CompanyTextStagingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Log4j2
@Component
public class CompanyTextEntitiesWriter implements ItemWriter<CompanyTextEntity> {

  private final CompanyTextStagingRepository companyStagingRepository;

  @Override
  public void write(final List<? extends CompanyTextEntity> list) throws Exception {
    for (final CompanyTextEntity entity : list) {
      entity.setSynchronizationState(SyncStatus.PENDING.getCode());
    }
    companyStagingRepository.saveAll(list);
  }
}
