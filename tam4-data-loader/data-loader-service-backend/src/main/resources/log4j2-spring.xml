<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Properties>
        <Property name="output-pattern">[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%C{1.}][%t][%-5p]: %m%n%throwable</Property>

        <Property name="file-name">${spring:logging.file.name:-./logs/data-loader-service.log}</Property>
        <Property name="file-pattern">${file-name}.%d{yyyy-MM-dd}.%i.gz</Property>
        <Property name="max-size">${spring:logging.file.max-size:-50MB}</Property>
        <Property name="max-history">${spring:logging.file.max-history:-100}</Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${output-pattern}"/>
        </Console>

        <RollingFile name="RollingFile"
                     fileName="${file-name}"
                     filePattern="${file-pattern}">
            <PatternLayout>
                <pattern>${output-pattern}</pattern>
            </PatternLayout>
            <DefaultRolloverStrategy max="${max-history}"/>
            <Policies>
                <!-- rollover on startup, daily and when the file reaches
                    10 MegaBytes -->
                <!--<OnStartupTriggeringPolicy/>-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="${max-size}"/>
                <!--        <RolloverStrategy max=""/>-->
            </Policies>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- LOG everything at INFO level -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration>
