<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="3.7.0" author="davide.fazio">
        <sql> ALTER TABLE public.validation_ausp ALTER COLUMN atinn TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_ausp ALTER COLUMN atzhl TYPE varchar </sql>
        <sql> ALTER TABLE public.validation_ausp ALTER COLUMN adzhl TYPE varchar </sql>
    </changeSet>

    <changeSet id="3.7.1" author="davide.fazio">
        <sql> ALTER TABLE public.validation_klah ALTER COLUMN clint TYPE varchar</sql>
    </changeSet>

    <changeSet id="3.7.2" author="davide.fazio">
        <sql> ALTER TABLE public.validation_kssk ALTER COLUMN clint TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_kssk ALTER COLUMN adzhl TYPE varchar </sql>
    </changeSet>

    <changeSet id="3.7.3" author="davide.fazio">
        <sql> ALTER TABLE public.validation_cawn ALTER COLUMN atinn TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_cawn ALTER COLUMN atzhl TYPE varchar </sql>
        <sql> ALTER TABLE public.validation_cawn ALTER COLUMN atflv TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_cawn ALTER COLUMN atflb TYPE varchar </sql>
    </changeSet>

    <changeSet id="3.7.4" author="davide.fazio">
        <sql> ALTER TABLE public.validation_cabn ALTER COLUMN atinn TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_cabn ALTER COLUMN adzhl TYPE varchar </sql>
    </changeSet>

    <changeSet id="3.7.5" author="davide.fazio">
        <sql> ALTER TABLE public.validation_inob ALTER COLUMN clint TYPE varchar</sql>
        <sql> ALTER TABLE public.validation_inob ALTER COLUMN cucozhl TYPE varchar </sql>
    </changeSet>
</databaseChangeLog>