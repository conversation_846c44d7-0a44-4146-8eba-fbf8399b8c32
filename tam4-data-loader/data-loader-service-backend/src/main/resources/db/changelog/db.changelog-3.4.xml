<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

    <changeSet id="3.4.0" author="davide.leonardi">
        <createIndex indexName="plant_currency_client_idx_unique" tableName="plant_currency">
            <column name="client"/>
            <column name="plant"/>
        </createIndex>
    </changeSet>

    <changeSet id="3.4.1" author="giulio.banterla">
      <preConditions onFail="MARK_RAN">
        <not>
          <columnExists tableName="materials_plant_data_staging" columnName="profit_center"/>
        </not>
      </preConditions>
      <addColumn tableName="materials_plant_data_staging">
        <column name="profit_center" type="varchar"/>
      </addColumn>
    </changeSet>

    <changeSet id="3.4.2" author="giulio.banterla">
      <preConditions onFail="MARK_RAN">
        <not>
          <columnExists tableName="materials_plant_data_staging" columnName="plant_old_material_number"/>
        </not>
      </preConditions>
      <addColumn tableName="materials_plant_data_staging">
        <column name="plant_old_material_number" type="varchar"/>
      </addColumn>
    </changeSet>

  <changeSet id="3.4.3" author="giulio.banterla">
    <preConditions onFail="MARK_RAN">
      <not>
        <columnExists tableName="materials_plant_data_staging" columnName="lot_size_for_product_costing"/>
      </not>
    </preConditions>
    <addColumn tableName="materials_plant_data_staging">
      <column name="lot_size_for_product_costing" type="numeric"/>
    </addColumn>
  </changeSet>

  <changeSet id="3.4.4" author="giulio.banterla">
    <preConditions onFail="MARK_RAN">
       <columnExists tableName="relationships" columnName="event_id"/>
    </preConditions>
    <dropColumn tableName="relationships" columnName="event_id"/>
  </changeSet>
  <!-- CHANGESET 1 - CREAZIONE TABELLE -->
  <changeSet id="3.4.5-1-01-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_ausp"/>
    </preConditions>
    <dropTable tableName="validation_ausp"/>
  </changeSet>
  <changeSet id="3.4.5-1-01-C" author="davide.bitetto">
    <createTable tableName="validation_ausp">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="objek" type="varchar"/>
      <column name="atinn" type="varchar"/>
      <column name="atzhl" type="varchar"/>
      <column name="mafid" type="varchar"/>
      <column name="klart" type="varchar"/>
      <column name="adzhl" type="varchar"/>
      <column name="atwrt" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-02-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_cabn"/>
    </preConditions>
    <dropTable tableName="validation_cabn"/>
  </changeSet>
  <changeSet id="3.4.5-1-02-C" author="davide.bitetto">
    <createTable tableName="validation_cabn">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="atinn" type="varchar"/>
      <column name="adzhl" type="varchar"/>
      <column name="atnam" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-03-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_cawn"/>
    </preConditions>
    <dropTable tableName="validation_cawn"/>
  </changeSet>
  <changeSet id="3.4.5-1-03-C" author="davide.bitetto">
    <createTable tableName="validation_cawn">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="atinn" type="varchar"/>
      <column name="atzhl" type="varchar"/>
      <column name="adzhl" type="varchar"/>
      <column name="atwrt" type="varchar"/>
      <column name="atflv" type="varchar"/>
      <column name="atflb" type="varchar"/>
      <column name="atcod" type="varchar"/>
      <column name="atstd" type="varchar"/>
      <column name="atawe" type="varchar"/>
      <column name="ataw1" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-04-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_consumption_data"/>
    </preConditions>
    <dropTable tableName="validation_consumption_data"/>
  </changeSet>
  <changeSet id="3.4.5-1-04-C" author="davide.bitetto">
    <createTable tableName="validation_consumption_data">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="client" type="varchar"/>
      <column name="materialnumber" type="varchar"/>
      <column name="plant" type="varchar"/>
      <column name="quantityconsumed" type="varchar"/>
      <column name="amountconsumed" type="varchar"/>
      <column name="currency" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-05-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_inob"/>
    </preConditions>
    <dropTable tableName="validation_inob"/>
  </changeSet>
  <changeSet id="3.4.5-1-05-C" author="davide.bitetto">
    <createTable tableName="validation_inob">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="cuobj" type="varchar"/>
      <column name="klart" type="varchar"/>
      <column name="obtab" type="varchar"/>
      <column name="objek" type="varchar"/>
      <column name="robtab" type="varchar"/>
      <column name="robjek" type="varchar"/>
      <column name="clint" type="varchar"/>
      <column name="statu" type="varchar"/>
      <column name="cucozhl" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="datuv" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-06-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_klah"/>
    </preConditions>
    <dropTable tableName="validation_klah"/>
  </changeSet>
  <changeSet id="3.4.5-1-06-C" author="davide.bitetto">
    <createTable tableName="validation_klah">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="clint" type="varchar"/>
      <column name="klart" type="varchar"/>
      <column name="class" type="varchar"/>
      <column name="statu" type="varchar"/>
      <column name="klagr" type="varchar"/>
      <column name="bgrse" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-07-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_kssk"/>
    </preConditions>
    <dropTable tableName="validation_kssk"/>
  </changeSet>
  <changeSet id="3.4.5-1-07-C" author="davide.bitetto">
    <createTable tableName="validation_kssk">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="objek" type="varchar"/>
      <column name="mafid" type="varchar"/>
      <column name="klart" type="varchar"/>
      <column name="clint" type="varchar"/>
      <column name="adzhl" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-08-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_lfa1"/>
    </preConditions>
    <dropTable tableName="validation_lfa1"/>
  </changeSet>
  <changeSet id="3.4.5-1-08-C" author="davide.bitetto">
    <createTable tableName="validation_lfa1">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="lifnr" type="varchar"/>
      <column name="land1" type="varchar"/>
      <column name="name1" type="varchar"/>
      <column name="name2" type="varchar"/>
      <column name="name3" type="varchar"/>
      <column name="name4" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-09-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_makt"/>
    </preConditions>
    <dropTable tableName="validation_makt"/>
  </changeSet>
  <changeSet id="3.4.5-1-09-C" author="davide.bitetto">
    <createTable tableName="validation_makt">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="maktx" type="varchar"/>
      <column name="spras" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-10-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_mara"/>
    </preConditions>
    <dropTable tableName="validation_mara"/>
  </changeSet>
  <changeSet id="3.4.5-1-10-C" author="davide.bitetto">
    <createTable tableName="validation_mara">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="laeda" type="varchar"/>
      <column name="lvorm" type="varchar"/>
      <column name="matkl" type="varchar"/>
      <column name="mtart" type="varchar"/>
      <column name="spart" type="varchar"/>
      <column name="meins" type="varchar"/>
      <column name="mstde" type="varchar"/>
      <column name="bstme" type="varchar"/>
      <column name="mbrsh" type="varchar"/>
      <column name="ersda" type="varchar"/>
      <column name="mfrnr" type="varchar"/>
      <column name="mfrpn" type="varchar"/>
      <column name="mstae" type="varchar"/>
      <column name="bismt" type="varchar"/>
      <column name="begru" type="varchar"/>
      <column name="mtpos_mara" type="varchar"/>
      <column name="extwg" type="varchar"/>
      <column name="gewei" type="varchar"/>
      <column name="ntgew" type="varchar"/>
      <column name="brgew" type="varchar"/>
      <column name="groes" type="varchar"/>
      <column name="stoff" type="varchar"/>
      <column name="zeinr" type="varchar"/>
      <column name="wrkst" type="varchar"/>
      <column name="labor" type="varchar"/>
      <column name="xchpf" type="varchar"/>
      <column name="prdha" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-11-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_marc"/>
    </preConditions>
    <dropTable tableName="validation_marc"/>
  </changeSet>
  <changeSet id="3.4.5-1-11-C" author="davide.bitetto">
    <createTable tableName="validation_marc">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="lvorm" type="varchar"/>
      <column name="mmsta" type="varchar"/>
      <column name="plifz" type="varchar"/>
      <column name="mmstd" type="varchar"/>
      <column name="disls" type="varchar"/>
      <column name="minbe" type="varchar"/>
      <column name="eisbe" type="varchar"/>
      <column name="eislo" type="varchar"/>
      <column name="sernp" type="varchar"/>
      <column name="mabst" type="varchar"/>
      <column name="dismm" type="varchar"/>
      <column name="disgr" type="varchar"/>
      <column name="ekgrp" type="varchar"/>
      <column name="nfmat" type="varchar"/>
      <column name="loggr" type="varchar"/>
      <column name="dispo" type="varchar"/>
      <column name="dzeit" type="varchar"/>
      <column name="sbdkz" type="varchar"/>
      <column name="webaz" type="varchar"/>
      <column name="ssqss" type="varchar"/>
      <column name="qzgtp" type="varchar"/>
      <column name="bstmi" type="varchar"/>
      <column name="xchpf" type="varchar"/>
      <column name="fhori" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-12-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_mard"/>
    </preConditions>
    <dropTable tableName="validation_mard"/>
  </changeSet>
  <changeSet id="3.4.5-1-12-C" author="davide.bitetto">
    <createTable tableName="validation_mard">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="lgort" type="varchar"/>
      <column name="pstat" type="varchar"/>
      <column name="lvorm" type="varchar"/>
      <column name="labst" type="varchar"/>
      <column name="umlme" type="varchar"/>
      <column name="insme" type="varchar"/>
      <column name="speme" type="varchar"/>
      <column name="lgpbe" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-13-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_marm"/>
    </preConditions>
    <dropTable tableName="validation_marm"/>
  </changeSet>
  <changeSet id="3.4.5-1-13-C" author="davide.bitetto">
    <createTable tableName="validation_marm">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="meinh" type="varchar"/>
      <column name="umrez" type="varchar"/>
      <column name="umren" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-14-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_mbew"/>
    </preConditions>
    <dropTable tableName="validation_mbew"/>
  </changeSet>
  <changeSet id="3.4.5-1-14-C" author="davide.bitetto">
    <createTable tableName="validation_mbew">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matnr" type="varchar"/>
      <column name="bwkey" type="varchar"/>
      <column name="bwtar" type="varchar"/>
      <column name="lvorm" type="varchar"/>
      <column name="lbkum" type="varchar"/>
      <column name="salk3" type="varchar"/>
      <column name="vprsv" type="varchar"/>
      <column name="verpr" type="varchar"/>
      <column name="stprs" type="varchar"/>
      <column name="peinh" type="varchar"/>
      <column name="bklas" type="varchar"/>
      <column name="bwtty" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-15-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_mgef"/>
    </preConditions>
    <dropTable tableName="validation_mgef"/>
  </changeSet>
  <changeSet id="3.4.5-1-15-C" author="davide.bitetto">
    <createTable tableName="validation_mgef">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="stoff" type="varchar"/>
      <column name="regkz" type="varchar"/>
      <column name="lagkl" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-16-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_long_text"/>
    </preConditions>
    <dropTable tableName="validation_long_text"/>
  </changeSet>
  <changeSet id="3.4.5-1-16-C" author="davide.bitetto">
    <createTable tableName="validation_long_text">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="client" type="varchar"/>
      <column name="materialnumber" type="varchar"/>
      <column name="textid" type="varchar"/>
      <column name="language" type="varchar"/>
      <column name="longtext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-17-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_po_history"/>
    </preConditions>
    <dropTable tableName="validation_po_history"/>
  </changeSet>
  <changeSet id="3.4.5-1-17-C" author="davide.bitetto">
    <createTable tableName="validation_po_history">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="client" type="varchar"/>
      <column name="materialnumber" type="varchar"/>
      <column name="plant" type="varchar"/>
      <column name="quantitypurchased" type="varchar"/>
      <column name="amountpurchased" type="varchar"/>
      <column name="currency" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-18-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t001"/>
    </preConditions>
    <dropTable tableName="validation_t001"/>
  </changeSet>
  <changeSet id="3.4.5-1-18-C" author="davide.bitetto">
    <createTable tableName="validation_t001">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="bukrs" type="varchar"/>
      <column name="butxt" type="varchar"/>
      <column name="ort01" type="varchar"/>
      <column name="land1" type="varchar"/>
      <column name="waers" type="varchar"/>
      <column name="spras" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-19-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t001k"/>
    </preConditions>
    <dropTable tableName="validation_t001k"/>
  </changeSet>
  <changeSet id="3.4.5-1-19-C" author="davide.bitetto">
    <createTable tableName="validation_t001k">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="bwkey" type="varchar"/>
      <column name="bukrs" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-20-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t001l"/>
    </preConditions>
    <dropTable tableName="validation_t001l"/>
  </changeSet>
  <changeSet id="3.4.5-1-20-C" author="davide.bitetto">
    <createTable tableName="validation_t001l">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="lgort" type="varchar"/>
      <column name="lgobe" type="varchar"/>
      <column name="spart" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-21-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t001w"/>
    </preConditions>
    <dropTable tableName="validation_t001w"/>
  </changeSet>
  <changeSet id="3.4.5-1-21-C" author="davide.bitetto">
    <createTable tableName="validation_t001w">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="name1" type="varchar"/>
      <column name="bwkey" type="varchar"/>
      <column name="lifnr" type="varchar"/>
      <column name="name2" type="varchar"/>
      <column name="ort01" type="varchar"/>
      <column name="land1" type="varchar"/>
      <column name="regio" type="varchar"/>
      <column name="spras" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-22-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t005"/>
    </preConditions>
    <dropTable tableName="validation_t005"/>
  </changeSet>
  <changeSet id="3.4.5-1-22-C" author="davide.bitetto">
    <createTable tableName="validation_t005">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="land1" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="intca" type="varchar"/>
      <column name="intca3" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-23-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t005t"/>
    </preConditions>
    <dropTable tableName="validation_t005t"/>
  </changeSet>
  <changeSet id="3.4.5-1-23-C" author="davide.bitetto">
    <createTable tableName="validation_t005t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="land1" type="varchar"/>
      <column name="landx" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-24-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t006"/>
    </preConditions>
    <dropTable tableName="validation_t006"/>
  </changeSet>
  <changeSet id="3.4.5-1-24-C" author="davide.bitetto">
    <createTable tableName="validation_t006">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="msehi" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-25-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t006a"/>
    </preConditions>
    <dropTable tableName="validation_t006a"/>
  </changeSet>
  <changeSet id="3.4.5-1-25-C" author="davide.bitetto">
    <createTable tableName="validation_t006a">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="msehi" type="varchar"/>
      <column name="mseh3" type="varchar"/>
      <column name="mseh6" type="varchar"/>
      <column name="mseht" type="varchar"/>
      <column name="msehl" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-26-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t023"/>
    </preConditions>
    <dropTable tableName="validation_t023"/>
  </changeSet>
  <changeSet id="3.4.5-1-26-C" author="davide.bitetto">
    <createTable tableName="validation_t023">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="matkl" type="varchar"/>
      <column name="spart" type="varchar"/>
      <column name="begru" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-27-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t023t"/>
    </preConditions>
    <dropTable tableName="validation_t023t"/>
  </changeSet>
  <changeSet id="3.4.5-1-27-C" author="davide.bitetto">
    <createTable tableName="validation_t023t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="matkl" type="varchar"/>
      <column name="wgbez" type="varchar"/>
      <column name="wgbez60" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-28-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t024"/>
    </preConditions>
    <dropTable tableName="validation_t024"/>
  </changeSet>
  <changeSet id="3.4.5-1-28-C" author="davide.bitetto">
    <createTable tableName="validation_t024">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="ekgrp" type="varchar"/>
      <column name="eknam" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-29-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t024d"/>
    </preConditions>
    <dropTable tableName="validation_t024d"/>
  </changeSet>
  <changeSet id="3.4.5-1-29-C" author="davide.bitetto">
    <createTable tableName="validation_t024d">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="dispo" type="varchar"/>
      <column name="dsnam" type="varchar"/>
      <column name="ekgrp" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-30-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t024l"/>
    </preConditions>
    <dropTable tableName="validation_t024l"/>
  </changeSet>
  <changeSet id="3.4.5-1-30-C" author="davide.bitetto">
    <createTable tableName="validation_t024l">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="labor" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-31-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t025t"/>
    </preConditions>
    <dropTable tableName="validation_t025t"/>
  </changeSet>
  <changeSet id="3.4.5-1-31-C" author="davide.bitetto">
    <createTable tableName="validation_t025t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="bklas" type="varchar"/>
      <column name="bkbez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-32-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t134"/>
    </preConditions>
    <dropTable tableName="validation_t134"/>
  </changeSet>
  <changeSet id="3.4.5-1-32-C" author="davide.bitetto">
    <createTable tableName="validation_t134">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="mtart" type="varchar"/>
      <column name="pstat" type="varchar"/>
      <column name="vprsv" type="varchar"/>
      <column name="kzvpr" type="varchar"/>
      <column name="vmtpo" type="varchar"/>
      <column name="begru" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-33-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t134t"/>
    </preConditions>
    <dropTable tableName="validation_t134t"/>
  </changeSet>
  <changeSet id="3.4.5-1-33-C" author="davide.bitetto">
    <createTable tableName="validation_t134t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="mtart" type="varchar"/>
      <column name="mtbez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-34-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t137t"/>
    </preConditions>
    <dropTable tableName="validation_t137t"/>
  </changeSet>
  <changeSet id="3.4.5-1-34-C" author="davide.bitetto">
    <createTable tableName="validation_t137t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="mbrsh" type="varchar"/>
      <column name="mbbez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-35-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t141"/>
    </preConditions>
    <dropTable tableName="validation_t141"/>
  </changeSet>
  <changeSet id="3.4.5-1-35-C" author="davide.bitetto">
    <createTable tableName="validation_t141">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="mmsta" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-36-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t141t"/>
    </preConditions>
    <dropTable tableName="validation_t141t"/>
  </changeSet>
  <changeSet id="3.4.5-1-36-C" author="davide.bitetto">
    <createTable tableName="validation_t141t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="mmsta" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="mtstb" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-37-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t149t"/>
    </preConditions>
    <dropTable tableName="validation_t149t"/>
  </changeSet>
  <changeSet id="3.4.5-1-37-C" author="davide.bitetto">
    <createTable tableName="validation_t149t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="bwtty" type="varchar"/>
      <column name="btbez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-38-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t179"/>
    </preConditions>
    <dropTable tableName="validation_t179"/>
  </changeSet>
  <changeSet id="3.4.5-1-38-C" author="davide.bitetto">
    <createTable tableName="validation_t179">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="prodh" type="varchar"/>
      <column name="stufe" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-39-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t179t"/>
    </preConditions>
    <dropTable tableName="validation_t179t"/>
  </changeSet>
  <changeSet id="3.4.5-1-39-C" author="davide.bitetto">
    <createTable tableName="validation_t179t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="prodh" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="vtext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-40-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t377p"/>
    </preConditions>
    <dropTable tableName="validation_t377p"/>
  </changeSet>
  <changeSet id="3.4.5-1-40-C" author="davide.bitetto">
    <createTable tableName="validation_t377p">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="serail" type="varchar"/>
      <column name="serscha" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-41-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t438a"/>
    </preConditions>
    <dropTable tableName="validation_t438a"/>
  </changeSet>
  <changeSet id="3.4.5-1-41-C" author="davide.bitetto">
    <createTable tableName="validation_t438a">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="dismm" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-42-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t438m"/>
    </preConditions>
    <dropTable tableName="validation_t438m"/>
  </changeSet>
  <changeSet id="3.4.5-1-42-C" author="davide.bitetto">
    <createTable tableName="validation_t438m">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="werks" type="varchar"/>
      <column name="mtart" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-43-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t438t"/>
    </preConditions>
    <dropTable tableName="validation_t438t"/>
  </changeSet>
  <changeSet id="3.4.5-1-43-C" author="davide.bitetto">
    <createTable tableName="validation_t438t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="dismm" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="dibez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-44-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_t439t"/>
    </preConditions>
    <dropTable tableName="validation_t439t"/>
  </changeSet>
  <changeSet id="3.4.5-1-44-C" author="davide.bitetto">
    <createTable tableName="validation_t439t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="disls" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="loslt" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-45-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tcla"/>
    </preConditions>
    <dropTable tableName="validation_tcla"/>
  </changeSet>
  <changeSet id="3.4.5-1-45-C" author="davide.bitetto">
    <createTable tableName="validation_tcla">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="klart" type="varchar"/>
      <column name="obtab" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-46-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tcurc"/>
    </preConditions>
    <dropTable tableName="validation_tcurc"/>
  </changeSet>
  <changeSet id="3.4.5-1-46-C" author="davide.bitetto">
    <createTable tableName="validation_tcurc">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="waers" type="varchar"/>
      <column name="isocd" type="varchar"/>
      <column name="altwr" type="varchar"/>
      <column name="gdatu" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-47-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tlogt"/>
    </preConditions>
    <dropTable tableName="validation_tlogt"/>
  </changeSet>
  <changeSet id="3.4.5-1-47-C" author="davide.bitetto">
    <createTable tableName="validation_tlogt">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="loggr" type="varchar"/>
      <column name="ltext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-48-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tptmt"/>
    </preConditions>
    <dropTable tableName="validation_tptmt"/>
  </changeSet>
  <changeSet id="3.4.5-1-48-C" author="davide.bitetto">
    <createTable tableName="validation_tptmt">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="mtpos" type="varchar"/>
      <column name="bezei" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-49-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tq05"/>
    </preConditions>
    <dropTable tableName="validation_tq05"/>
  </changeSet>
  <changeSet id="3.4.5-1-49-C" author="davide.bitetto">
    <createTable tableName="validation_tq05">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="zgtyp" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-50-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tq05t"/>
    </preConditions>
    <dropTable tableName="validation_tq05t"/>
  </changeSet>
  <changeSet id="3.4.5-1-50-C" author="davide.bitetto">
    <createTable tableName="validation_tq05t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="sprache" type="varchar"/>
      <column name="zgtyp" type="varchar"/>
      <column name="kurztext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-51-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tq08"/>
    </preConditions>
    <dropTable tableName="validation_tq08"/>
  </changeSet>
  <changeSet id="3.4.5-1-51-C" author="davide.bitetto">
    <createTable tableName="validation_tq08">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="qm_pur" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-52-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tq08t"/>
    </preConditions>
    <dropTable tableName="validation_tq08t"/>
  </changeSet>
  <changeSet id="3.4.5-1-52-C" author="davide.bitetto">
    <createTable tableName="validation_tq08t">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="sprache" type="varchar"/>
      <column name="qm_pur" type="varchar"/>
      <column name="kurztext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-53-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_tspat"/>
    </preConditions>
    <dropTable tableName="validation_tspat"/>
  </changeSet>
  <changeSet id="3.4.5-1-53-C" author="davide.bitetto">
    <createTable tableName="validation_tspat">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="spart" type="varchar"/>
      <column name="vtext" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-54-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_twew"/>
    </preConditions>
    <dropTable tableName="validation_twew"/>
  </changeSet>
  <changeSet id="3.4.5-1-54-C" author="davide.bitetto">
    <createTable tableName="validation_twew">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="extwg" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-55-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_twewt"/>
    </preConditions>
    <dropTable tableName="validation_twewt"/>
  </changeSet>
  <changeSet id="3.4.5-1-55-C" author="davide.bitetto">
    <createTable tableName="validation_twewt">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="spras" type="varchar"/>
      <column name="extwg" type="varchar"/>
      <column name="ewbez" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-56-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_twspr"/>
    </preConditions>
    <dropTable tableName="validation_twspr"/>
  </changeSet>
  <changeSet id="3.4.5-1-56-C" author="davide.bitetto">
    <createTable tableName="validation_twspr">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="mandt" type="varchar"/>
      <column name="wrkst" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-57-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_log_detailed"/>
    </preConditions>
    <dropTable tableName="validation_log_detailed"/>
  </changeSet>
  <changeSet id="3.4.5-1-57-C" author="davide.bitetto">
    <createTable tableName="validation_log_detailed">
      <column name="table" type="varchar"/>
      <column name="field" type="varchar"/>
      <column name="method" type="varchar"/>
      <column name="isblocking" type="bit(1)"/>
      <column name="row" type="bigint"/>
      <column name="description" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-58-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_log_simple"/>
    </preConditions>
    <dropTable tableName="validation_log_simple"/>
  </changeSet>
  <changeSet id="3.4.5-1-58-C" author="davide.bitetto">
    <createTable tableName="validation_log_simple">
      <column name="chainid" type="int"/>
      <column name="chainorder" type="int"/>
      <column name="table" type="varchar"/>
      <column name="field" type="varchar"/>
      <column name="numrows" type="int"/>
      <column name="status" type="varchar"/>
      <column name="method" type="varchar"/>
      <column name="description" type="varchar"/>
      <column name="executiontime" type="java.sql.Types.TIMESTAMP"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-59-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_log_status"/>
    </preConditions>
    <dropTable tableName="validation_log_status"/>
  </changeSet>
  <changeSet id="3.4.5-1-59-C" author="davide.bitetto">
    <createTable tableName="validation_log_status">
      <column name="id" type="bigint">
        <constraints nullable="false" unique="true" primaryKey="true"/>
      </column>
      <column name="job_execution_id" type="bigint"/>
      <column name="file" type="varchar"/>
      <column name="startdate" type="java.sql.Types.TIMESTAMP"/>
      <column name="enddate" type="java.sql.Types.TIMESTAMP"/>
      <column name="status" type="varchar"/>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5-1-60-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="regexp"/>
    </preConditions>
    <dropTable tableName="regexp"/>
  </changeSet>
  <changeSet id="3.4.5-1-60-C" author="davide.bitetto">
    <createTable tableName="regexp">
      <column name="name" type="varchar"/>
      <column name="reg" type="varchar"/>
    </createTable>
  </changeSet>

  <!-- CHANGESET 2 - AGGIUNTA AUTOINCREMENT SU TABELLE -->
  <changeSet id="3.4.5-2" author="davide.bitetto">
    <addAutoIncrement tableName="validation_ausp" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_cabn" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_cawn" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_consumption_data" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_inob" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_klah" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_kssk" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_lfa1" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_makt" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_mara" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_marc" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_mard" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_marm" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_mbew" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_mgef" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_long_text" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_po_history" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t001" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t001k" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t001l" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t001w" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t005" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t005t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t006" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t006a" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t023" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t023t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t024" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t024d" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t024l" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t025t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t134" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t134t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t137t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t141" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t141t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t149t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t179" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t179t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t377p" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t438a" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t438m" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t438t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_t439t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tcla" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tcurc" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tlogt" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tptmt" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tq05" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tq05t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tq08" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tq08t" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_tspat" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_twew" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_twewt" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_twspr" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
    <addAutoIncrement tableName="validation_log_status" columnName="id" incrementBy="1" schemaName="public"
      startWith="1"/>
  </changeSet>

  <!-- CHANGESET 3 - AGGIUNTA VISTE -->
  <changeSet id="3.4.5-3" author="davide.bitetto">
    <createView replaceIfExists="true" viewName="v_validation_status">
      select case
               when s.error > 0 then 'ERROR'
               when s.warning > 0 then 'WARNING'
               when s.ok > 0 then 'OK'
               else 'UNDEFINED'
               end as status
      from (select o.rules_count as ok, w.rules_count as warning, e.rules_count as error
            from (select 'OK' as status, count(*) as rules_count
                  from validation_log_simple
                  where status = 'OK') as o
                   join
                 (select 'WARNING' as status, count(*) as rules_count
                  from validation_log_simple
                  where status = 'WARNING') as w
                 on 1 = 1
                   join
                 (select 'ERROR' as status, count(*) as rules_count
                  from validation_log_simple
                  where status = 'ERROR') as e
                 on 1 = 1) as s
    </createView>
  </changeSet>

  <!-- CHANGESET 4 - INSERIMENTO VALORI -->
  <changeSet id="3.4.5-4" author="davide.bitetto">
    <insert tableName="regexp">
      <column name="name" value="HasValue"/>
      <column name="reg" value="^(?!\s*$).+"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="Numeric"/>
      <column name="reg" value="^\d+(\.\d+)?$"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="Data"/>
      <column name="reg"
        value="^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(?:0?[13-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9])|(?:1[0-2]))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$"/>
    </insert>
  </changeSet>

  <changeSet id="3.4.5-5-D" author="davide.bitetto">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_table_load_log"/>
    </preConditions>
    <dropTable tableName="validation_table_load_log"/>
  </changeSet>
  <changeSet id="3.4.5-5-C" author="davide.bitetto">
    <createTable tableName="validation_table_load_log">
      <column name="table" type="varchar">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
      <column name="message" type="varchar">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
      <column name="step_execution_id" type="bigint">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
    </createTable>
  </changeSet>

  <changeSet id="3.4.5" author="ardit.sarja">
    <createSequence incrementBy="1000" sequenceName="plant_languages_entity_seq" startValue="1"/>
  </changeSet>


  <changeSet id="3.4.6" author="ardit.sarja">
    <createTable tableName="plants_additional_languages_staging">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true"
                     primaryKeyName="pk_plants_additional_languages_staging"/>
      </column>
      <column name="language_code" type="VARCHAR">
        <constraints nullable="false"/>
      </column>
      <column name="client" type="VARCHAR">
        <constraints nullable="false"/>
      </column>
      <column name="plant" type="VARCHAR">
        <constraints nullable="false"/>
      </column>
    </createTable>
    <createIndex tableName="plants_additional_languages_staging" indexName="ix_plants_additional_languages_staging_client_plant">
      <column name="client"/>
      <column name="plant"/>
    </createIndex>
  </changeSet>

  <changeSet id="3.4.7" author="matteo.laurenzi">
    <addUniqueConstraint tableName="plants_additional_languages_staging" columnNames="client, plant, language_code"/>
  </changeSet>

  <changeSet id="3.4.8-1" author="federico.sergio">
    <insert tableName="regexp">
      <column name="name" value="SPRAS"/>
      <column name="reg"
        value="^(a|A|W|c|1|M|6|C|K|N|E|9|U|F|D|G|B|H|b|i|I|J|3|Y|X|7|O|L|P|Z|4|R|0|d|Q|5|S|V|2|T|8)"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="Country"/>
      <column name="reg"
        value="^(AF|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|CV|KH|CM|CA|KY|CF|TD|CL|CN|CX|CC|CO|KM|CD|CG|CK|CR|HR|CU|CW|CY|CZ|CI|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|SZ|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|MK|RO|RU|RW|RE|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|UM|US|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW|AX|Z1|Z2|ZI|ZZ)"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="Country3"/>
      <column name="reg"
        value="^(ABW|AFG|AGO|AIA|ALA|ALB|AND|ARE|ARG|ARM|ASM|ATA|ATF|ATG|AUS|AUT|AZE|BDI|BEL|BEN|BES|BFA|BGD|BGR|BHR|BHS|BIH|BLM|BLR|BLZ|BMU|BOL|BRA|BRB|BRN|BTN|BVT|BWA|CAF|CAN|CCK|CHE|CHL|CHN|CIV|CMR|COD|COG|COK|COL|COM|CPV|CRI|CUB|CUW|CXR|CYM|CYP|CZE|DEU|DJI|DMA|DNK|DOM|DZA|ECU|EGY|ERI|ESH|ESP|EST|ETH|FIN|FJI|FLK|FRA|FRO|FSM|GAB|GBR|GEO|GGY|GHA|GIB|GIN|GLP|GMB|GNB|GNQ|GRC|GRD|GRL|GTM|GUF|GUM|GUY|HKG|HMD|HND|HRV|HTI|HUN|IDN|IMN|IND|IOT|IRL|IRN|IRQ|ISL|ISR|ITA|JAM|JEY|JOR|JPN|KAZ|KEN|KGZ|KHM|KIR|KNA|KOR|KWT|LAO|LBN|LBR|LBY|LCA|LIE|LKA|LSO|LTU|LUX|LVA|MAC|MAF|MAR|MCO|MDA|MDG|MDV|MEX|MHL|MKD|MLI|MLT|MMR|MNE|MNG|MNP|MOZ|MRT|MSR|MTQ|MUS|MWI|MYS|MYT|NAM|NCL|NER|NFK|NGA|NIC|NIU|NLD|NOR|NPL|NRU|NZL|OMN|PAK|PAN|PCN|PER|PHL|PLW|PNG|POL|PRI|PRK|PRT|PRY|PSE|PYF|QAT|REU|ROU|RUS|RWA|SAU|SDN|SEN|SGP|SGS|SHN|SJM|SLB|SLE|SLV|SMR|SOM|SPM|SRB|SSD|STP|SUR|SVK|SVN|SWE|SWZ|SXM|SYC|SYR|TCA|TCD|TGO|THA|TJK|TKL|TKM|TLS|TON|TTO|TUN|TUR|TUV|TWN|TZA|UGA|UKR|UMI|URY|USA|UZB|VAT|VCT|VEN|VGB|VIR|VNM|VUT|WLF|WSM|YEM|ZAF|ZMB|ZWE)"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="TextID"/>
      <column name="reg" value="^(BEST|GRUN|IVER|PRUE)"/>
    </insert>
    <insert tableName="regexp">
      <column name="name" value="Currency"/>
      <column name="reg"
        value="^(AED|AFN|ALL|AMD|ANG|AOA|ARS|AUD|AWG|AZN|BAM|BBD|BDT|BGN|BHD|BIF|BMD|BND|BOB|BOV|BRL|BSD|BTN|BWP|BYN|BZD|CAD|CDF|CHE|CHF|CHW|CLF|CLP|CNY|COP|COU|CRC|CUC|CUP|CVE|CZK|DJF|DKK|DOP|DZD|EGP|ERN|ETB|EUR|FJD|FKP|GBP|GEL|GHS|GIP|GMD|GNF|GTQ|GYD|HKD|HNL|HRK|HTG|HUF|IDR|ILS|INR|IQD|IRR|ISK|JMD|JOD|JPY|KES|KGS|KHR|KMF|KPW|KRW|KWD|KYD|KZT|LAK|LBP|LKR|LRD|LSL|LYD|MAD|MDL|MGA|MKD|MMK|MNT|MOP|MRU|MUR|MVR|MWK|MXN|MXV|MYR|MZN|NAD|NGN|NIO|NOK|NPR|NZD|OMR|PAB|PEN|PGK|PHP|PKR|PLN|PYG|QAR|RON|RSD|RUB|RWF|SAR|SBD|SCR|SDG|SEK|SGD|SHP|SLE|SOS|SRD|SSP|STN|SVC|SYP|SZL|THB|TJS|TMT|TND|TOP|TRY|TTD|TWD|TZS|UAH|UGX|USD|USN|UYI|UYU|UZS|VED|VEF|VND|VUV|WST|XAF|XCD|XDR|XOF|XPF|XSU|XUA|YER|ZAR|ZMW|ZWL)"/>
    </insert>
  </changeSet>

  <changeSet id="3.4.9" author="federico.sergio">
    <preConditions onFail="MARK_RAN">
      <tableExists tableName="validation_table_load_log"/>
    </preConditions>
    <dropTable tableName="validation_table_load_log"/>
  </changeSet>

  <changeSet id="3.4.9-1" author="federico.sergio">
    <createTable tableName="validation_table_load_log">
      <column name="table" type="varchar">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
      <column name="status" type="varchar">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
      <column name="message" type="varchar">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
      <column name="step_execution_id" type="bigint">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="PK_VALIDATION_TABLE_LOAD_LOG"></constraints>
      </column>
    </createTable>
  </changeSet>

  <changeSet id="3.4.10" author="carmela.anzelmo">
    <preConditions onFail="MARK_RAN">
      <not>
        <columnExists tableName="unit_of_measure_keys_staging" columnName="enabled" />
      </not>
    </preConditions>
    <addColumn tableName="unit_of_measure_keys_staging">
      <column name="enabled" type="boolean" defaultValue="true"/>
    </addColumn>
  </changeSet>


</databaseChangeLog>
