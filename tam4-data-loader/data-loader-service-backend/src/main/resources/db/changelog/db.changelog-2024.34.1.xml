<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

    <changeSet id="2024.34.1_001" author="roberto.gabrieli" runAlways="true">
        <sql>alter table outbound_queue alter column id set default nextval('outbound_queue_id_seq');</sql>
        <sql>alter sequence outbound_queue_id_seq cache 1;</sql>
        <sql>SELECT SETVAL('outbound_queue_id_seq', (Select coalesce(max(id), 1) from outbound_queue));</sql>
    </changeSet>

</databaseChangeLog>
