<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">
    <changeSet id="1.0-2" author="calin.dumitru">
        <createSequence sequenceName="hibernate_sequence"/>

        <!--MARA OK-->
        <createSequence sequenceName="materials_data_staging_seq" incrementBy="1000"/>
        <createTable tableName="materials_data_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="revision_number" type="varchar"/>
            <column name="deletion_flag" type="boolean"/>
            <column name="material_type" type="varchar"/>
            <column name="industry_sector" type="varchar"/>
            <column name="material_group" type="varchar"/>
            <column name="old_material_number" type="varchar"/>
            <column name="base_unit_of_measurement" type="varchar"/>
            <column name="product_division" type="varchar"/>
            <column name="authorization_group" type="varchar"/>
            <column name="cross_plant_material_status" type="varchar"/>
            <column name="material_status_valid_from_date" type="varchar"/>
            <column name="manufacturer_part_number" type="varchar"/>
            <column name="manufacturer_code" type="varchar"/>
            <column name="generic_item_group" type="varchar"/>
            <column name="purchasing_measurement_units" type="varchar"/>
            <column name="created_on" type="varchar"/>
            <column name="external_material_group" type="varchar"/>
            <column name="weight_unit" type="varchar"/>
            <column name="net_weight" type="double precision"/>
            <column name="gross_weight" type="double precision"/>
            <column name="size_dimension" type="varchar"/>
            <column name="hazardous_material_number" type="varchar"/>
        </createTable>

        <!--MARC ok-->
        <createSequence sequenceName="materials_plant_data_staging_seq" incrementBy="1000"/>
        <createTable tableName="materials_plant_data_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="composite_business_key" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="plant_id" type="varchar"/>
            <column name="deletion_flag" type="boolean"/>
            <column name="status" type="varchar"/>
            <column name="seriable" type="varchar"/>
            <column name="mrp_type" type="varchar"/>
            <column name="lead_time_in_days" type="varchar"/>
            <column name="lot_size" type="varchar"/>
            <column name="reorder_point" type="varchar"/>
            <column name="safety_stock" type="varchar"/>
            <column name="maximum_stock_level" type="varchar"/>
            <column name="mrp_group" type="varchar"/>
            <column name="follow_up_material" type="varchar"/>
            <column name="logistics_handling_group" type="varchar"/>
            <column name="min_lot_size" type="varchar"/>
            <column name="minimum_safety_stock" type="varchar"/>
        </createTable>
        <createIndex tableName="materials_plant_data_staging"
                     indexName="ix_materials_plant_data_staging_client_materialcode">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>

        <!--MBEW-->
        <createSequence sequenceName="plants_valuation_data_staging_seq" incrementBy="1000"/>
        <createTable tableName="plants_valuation_data_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="composite_business_key" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="plant_id" type="varchar"/>
            <column name="valuation_type" type="varchar"/>
            <column name="deletion_flag_material" type="boolean"/>
            <column name="valuation_class" type="varchar"/>
            <column name="valuation_category" type="varchar"/>
            <column name="inventory_amount" type="varchar"/>
            <column name="price_control_indicator" type="varchar"/>
            <column name="moving_average_price" type="varchar"/>
            <column name="standard_price" type="varchar"/>
            <column name="price_unit" type="varchar"/>
            <column name="total_value" type="varchar"/>
        </createTable>
        <createIndex tableName="plants_valuation_data_staging"
                     indexName="ix_plants_valuation_data_staging_client_materialcode">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>

        <!--MAKT ok-->
        <createSequence sequenceName="short_descriptions_staging_seq" incrementBy="1000"/>
        <createTable tableName="short_descriptions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material" type="varchar"/>
            <column name="language" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="short_descriptions_staging" indexName="ix_short_descriptions_staging_client_material">
            <column name="client"/>
            <column name="material"/>
        </createIndex>

        <!--MARM ok-->
        <createSequence sequenceName="units_of_measure_staging_seq" incrementBy="1000"/>
        <createTable tableName="units_of_measure_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="alternative_unit_of_measurement" type="varchar"/>
            <column name="numerator" type="varchar"/>
            <column name="denominator" type="varchar"/>
        </createTable>
        <createIndex tableName="units_of_measure_staging" indexName="ix_units_of_measure_staging_client_materialcode">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>

        <!--MARD-->
        <createSequence sequenceName="material_storage_locations_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_storage_locations_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="composite_business_key" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="plant_code" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="valuated_unrestricted_use_stock" type="varchar"/>
            <column name="stock_in_transfer" type="varchar"/>
            <column name="stock_in_quality_inspection" type="varchar"/>
            <column name="blocked_stock" type="varchar"/>
            <column name="storage_bin" type="varchar"/>
        </createTable>
        <createIndex tableName="material_storage_locations_staging"
                     indexName="ix_material_storage_locations_staging_client_materialcode">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>

        <!--LFA1 ok-->
        <createSequence sequenceName="suppliers_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="suppliers_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="code" type="varchar"/>
            <column name="vat_registration_number" type="varchar"/>
            <column name="name" type="varchar"/>
        </createTable>
        <createIndex tableName="suppliers_definitions_staging" indexName="ix_suppliers_definitions_staging_client_code">
            <column name="client"/>
            <column name="code"/>
        </createIndex>

        <!--T023 ok-->
        <createSequence sequenceName="material_group_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_group_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_group" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="long_description" type="varchar"/>
        </createTable>
        <createIndex tableName="material_group_definitions_staging"
                     indexName="ix_material_group_definitions_staging_client_materialgroup">
            <column name="client"/>
            <column name="material_group"/>
        </createIndex>


    </changeSet>
    <changeSet id="1.0-3" author="daniele.canteri">
        <createSequence sequenceName="material_group_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_group_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_group" type="varchar"/>
        </createTable>
        <createIndex tableName="material_group_keys_staging"
                     indexName="ix_material_group_keys_staging_client_materialgroup">
            <column name="client"/>
            <column name="material_group"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-4" author="bejane.zenjelaj">

        <!--T134 ok-->
        <createSequence sequenceName="material_type_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_type_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_type" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="material_type_definitions_staging"
                     indexName="ix_material_type_definitions_staging_client_materialtype">
            <column name="client"/>
            <column name="material_type"/>
        </createIndex>

        <createSequence sequenceName="material_type_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_type_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_type" type="varchar"/>
        </createTable>
        <createIndex tableName="material_type_keys_staging"
                     indexName="ix_material_type_keys_staging_client_materialtype">
            <column name="client"/>
            <column name="material_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-5" author="bejane.zenjelaj">

        <!--T137 ok-->
        <createSequence sequenceName="industry_sector_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="industry_sector_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="industry_sector" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="industry_sector_definitions_staging"
                     indexName="ix_industry_sector_definitions_staging_client_industry_sector">
            <column name="client"/>
            <column name="industry_sector"/>
        </createIndex>

        <createSequence sequenceName="industry_sector_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="industry_sector_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="industry_sector" type="varchar"/>
        </createTable>
        <createIndex tableName="industry_sector_keys_staging"
                     indexName="ix_industry_sector_keys_staging_client_industry_sector">
            <column name="client"/>
            <column name="industry_sector"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-6" author="bejane.zenjelaj">
        <!--T141 ok-->
        <createSequence sequenceName="material_status_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_status_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_status" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="material_status_definitions_staging"
                     indexName="ix_material_status_definitions_staging_client_material_status">
            <column name="client"/>
            <column name="material_status"/>
        </createIndex>

        <createSequence sequenceName="material_status_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_status_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_status" type="varchar"/>
        </createTable>
        <createIndex tableName="material_status_keys_staging"
                     indexName="ix_material_status_keys_staging_client_material_status">
            <column name="client"/>
            <column name="material_status"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-7" author="michele.masili">
        <createIndex tableName="suppliers_definitions_staging"
                     indexName="ix_suppliers_definitions_staging_client_vat_registration_number">
            <column name="client"/>
            <column name="vat_registration_number"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-8" author="bejane.zenjelaj">
        <!--T006 ok-->
        <createSequence sequenceName="unit_of_measure_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="unit_of_measure_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="unit_of_measure" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="unit_of_measure_definitions_staging"
                     indexName="ix_unit_of_measure_definitions_staging_client_unit_of_measure">
            <column name="client"/>
            <column name="unit_of_measure"/>
        </createIndex>

        <createSequence sequenceName="unit_of_measure_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="unit_of_measure_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="unit_of_measure" type="varchar"/>
        </createTable>
        <createIndex tableName="unit_of_measure_keys_staging"
                     indexName="ix_unit_of_measure_keys_staging_client_unit_of_measure">
            <column name="client"/>
            <column name="unit_of_measure"/>
        </createIndex>
    </changeSet>


    <changeSet id="1.0-9" author="bejane.zenjelaj">
        <!--T001L ok-->
        <createSequence sequenceName="storage_location_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="storage_location_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="storage_location_definitions_staging"
                     indexName="ix_storage_location_definitions_staging_client_storage_location">
            <column name="client"/>
            <column name="storage_location"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-10" author="bejane.zenjelaj">

        <!--T134 ok-->
        <createSequence sequenceName="item_category_group_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="item_category_group_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="item_category_group" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="item_category_group_definitions_staging"
                     indexName="ix_item_category_group_definitions_staging_client_item_category_group">
            <column name="client"/>
            <column name="item_category_group"/>
        </createIndex>

        <createSequence sequenceName="item_category_group_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="item_category_group_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="item_category_group" type="varchar"/>
        </createTable>
        <createIndex tableName="item_category_group_keys_staging"
                     indexName="ix_item_category_group_keys_staging_client_item_category_group">
            <column name="client"/>
            <column name="item_category_group"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-11" author="bejane.zenjelaj">

        <!--T134 ok-->
        <createSequence sequenceName="country_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="country_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="long_description" type="varchar"/>
        </createTable>
        <createIndex tableName="country_definitions_staging" indexName="ix_country_definitions_staging_client_country">
            <column name="client"/>
            <column name="country"/>
        </createIndex>

        <createSequence sequenceName="country_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="country_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="country" type="varchar"/>
        </createTable>
        <createIndex tableName="country_keys_staging" indexName="ix_country_keys_staging_client_country">
            <column name="client"/>
            <column name="country"/>
        </createIndex>
    </changeSet>


    <changeSet id="1.0-12" author="bejane.zenjelaj">
        <!--T001W ok-->
        <createSequence sequenceName="plant_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="plant_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="second_description" type="varchar"/>
            <column name="country" type="varchar"/>
        </createTable>
        <createIndex tableName="plant_definitions_staging" indexName="ix_plant_definitions_staging_client_plant">
            <column name="client"/>
            <column name="plant"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-13" author="bejane.zenjelaj">

        <!--T025 ok-->
        <createSequence sequenceName="valuation_class_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="valuation_class_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="valuation_class" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="valuation_class_definitions_staging"
                     indexName="ix_valuation_class_definitions_staging_client_valuation_class">
            <column name="client"/>
            <column name="valuation_class"/>
        </createIndex>

        <createSequence sequenceName="valuation_class_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="valuation_class_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="valuation_class" type="varchar"/>
        </createTable>
        <createIndex tableName="valuation_class_keys_staging"
                     indexName="ix_valuation_class_keys_staging_client_valuation_class">
            <column name="client"/>
            <column name="valuation_class"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-14" author="bejane.zenjelaj">
        <!--T149 ok-->
        <createSequence sequenceName="global_valuation_category_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="global_valuation_category_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="global_valuation_category" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="global_valuation_category_definitions_staging"
                     indexName="ix_global_valuation_category_definitions_staging_client_global_valuation_category">
            <column name="client"/>
            <column name="global_valuation_category"/>
        </createIndex>

        <createSequence sequenceName="global_valuation_category_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="global_valuation_category_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="global_valuation_category" type="varchar"/>
        </createTable>
        <createIndex tableName="global_valuation_category_keys_staging"
                     indexName="ix_global_valuation_category_keys_staging_client_global_valuation_category">
            <column name="client"/>
            <column name="global_valuation_category"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-15" author="bejane.zenjelaj">

        <!--T024 ok-->
        <createSequence sequenceName="purchasing_group_staging_seq" incrementBy="1000"/>
        <createTable tableName="purchasing_group_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="purchasing_group" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="purchasing_group_staging"
                     indexName="ix_purchasing_group_staging_client_purchasing_group">
            <column name="client"/>
            <column name="purchasing_group"/>
        </createIndex>

    </changeSet>

    <changeSet id="1.0-16" author="bejane.zenjelaj">
        <!--ZM119 ok-->
        <createSequence sequenceName="long_purchase_order_descriptions_staging_seq" incrementBy="1000"/>
        <createTable tableName="long_purchase_order_descriptions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="material" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="po_description" type="varchar"/>
            <column name="language" type="varchar"/>
        </createTable>
        <createIndex tableName="long_purchase_order_descriptions_staging"
                     indexName="ix_long_purchase_order_descriptions_staging_material">
            <column name="material"/>
        </createIndex>

    </changeSet>

    <changeSet id="1.0-17" author="bejane.zenjelaj">
        <!--PO History 24M_ME80FN ok-->
        <createSequence sequenceName="history_order_staging_seq" incrementBy="1000"/>
        <createTable tableName="history_order_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="ordered_quantity" type="varchar"/>
            <column name="plant_code" type="varchar"/>
            <column name="price" type="varchar"/>
            <column name="ordered_amount" type="varchar"/>
            <column name="currency" type="varchar"/>
        </createTable>
        <createIndex tableName="history_order_staging" indexName="ix_history_order_staging_client_material_code">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>

        <!--Consumption data_24M ok-->
        <createSequence sequenceName="consumption_order_staging_seq" incrementBy="1000"/>
        <createTable tableName="consumption_order_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="consumption_quantity" type="varchar"/>
            <column name="plant_code" type="varchar"/>
            <column name="unitary_price_consumption" type="varchar"/>
            <column name="consumption_amount" type="varchar"/>
            <column name="currency" type="varchar"/>
        </createTable>
        <createIndex tableName="consumption_order_staging"
                     indexName="ix_consumption_order_staging_client_material_code">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-18" author="bejane.zenjelaj">
        <createSequence sequenceName="characteristics_staging_seq" incrementBy="1000"/>
        <createTable tableName="characteristics_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="material_code" type="varchar"/>
            <column name="code" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="value" type="varchar"/>
        </createTable>
        <createIndex tableName="characteristics_staging" indexName="ix_characteristics_staging_material">
            <column name="material_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-19" author="bejane.zenjelaj">
        <addColumn tableName="materials_plant_data_staging">
            <column name="valid_from_date" type="varchar"></column>
            <column name="purchasing_group" type="varchar"></column>
            <column name="mrp_controller" type="varchar"></column>
            <column name="in_house_production_time" type="double precision"></column>
            <column name="individual_coll" type="varchar"></column>
            <column name="goods_receipt_processing_time_in_days" type="double precision"></column>
            <column name="control_key_for_quality_management" type="varchar"></column>
            <column name="certificate_type" type="varchar"></column>
            <column name="batch_management_requirement" type="varchar"></column>
            <column name="scheduling_margin_key_for_floats" type="varchar"></column>
            <column name="avato_log_system" type="varchar"></column>
            <column name="avato_version" type="varchar"></column>
            <column name="avato_last_sync" type="varchar"></column>
            <column name="avato_sequence" type="varchar"></column>
        </addColumn>
    </changeSet>

    <changeSet id="1.0-20" author="bejane.zenjelaj">
        <addColumn tableName="unit_of_measure_definitions_staging">
            <column name="external_unit_of_measure_commercial" type="varchar"></column>
            <column name="external_unit_of_measure_technical" type="varchar"></column>
            <column name="short_description" type="varchar"></column>
        </addColumn>
    </changeSet>


    <changeSet id="1.0-21" author="bejane.zenjelaj">
        <!-- TSPAT -->
        <createSequence sequenceName="product_division_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="product_division_definitions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="division" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="product_division_definitions_staging"
                     indexName="ix_product_division_definitions_staging_client">
            <column name="client"></column>
            <column name="division"/>
        </createIndex>

        <createSequence sequenceName="product_division_definitions_keys_staging_seq" incrementBy="1000"/>
        <createTable tableName="product_division_definitions_keys_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="division" type="varchar"/>
        </createTable>
        <createIndex tableName="product_division_definitions_keys_staging"
                     indexName="ix_product_division_definitions_keys_staging_client_product_division">
            <column name="client"></column>
            <column name="division"></column>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-22" author="bejane.zenjelaj">
        <!-- T001 OK-->
        <createSequence sequenceName="company_staging_seq" incrementBy="1000"/>
        <createTable tableName="company_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="company_code" type="varchar"/>
            <column name="company_name" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="country_key" type="varchar"/>
            <column name="currency_key" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </createTable>
        <createIndex tableName="company_staging" indexName="ix_company_staging_client_company_code">
            <column name="client"></column>
            <column name="company_code"></column>
        </createIndex>

        <createSequence sequenceName="currency_staging_seq" incrementBy="1000"/>
        <createTable tableName="currency_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="currency_key" type="varchar"/>
            <column name="iso_currency_code" type="varchar"/>
            <column name="alternative_currency_key" type="varchar"/>
            <column name="valid_date" type="varchar"/>
        </createTable>
        <createIndex tableName="currency_staging" indexName="ix_currency_staging_client_currency_key">
            <column name="client"></column>
            <column name="currency_key"></column>
        </createIndex>

        <createSequence sequenceName="valuation_area_staging_eq" incrementBy="1000"/>
        <createTable tableName="valuation_area_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="valuation_area" type="varchar"/>
            <column name="company_code" type="varchar"/>
        </createTable>
        <createIndex tableName="valuation_area_staging" indexName="ix_valuation_area_staging_client_valuation_area">
            <column name="client"></column>
            <column name="valuation_area"></column>
        </createIndex>


        <addColumn tableName="plant_definitions_staging">
            <column name="valuation_area" type="varchar"/>
            <column name="vendor_number" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="region" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
        </addColumn>
        <renameColumn tableName="plant_definitions_staging" oldColumnName="country" newColumnName="country_key"/>
    </changeSet>

    <changeSet id="1.0-23" author="bejane.zenjelaj">
        <createSequence sequenceName="serial_number_management_values_staging_seq" incrementBy="1000"/>
        <createTable tableName="serial_number_management_values_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="serial_number_profile" type="varchar"/>
        </createTable>
        <createIndex tableName="serial_number_management_values_staging"
                     indexName="ix_serial_number_management_values_staging_material">
            <column name="client"></column>
            <column name="serial_number_profile"></column>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-24" author="calin.dumitru">
        <addColumn tableName="materials_data_staging">
            <column name="ignore" type="boolean" defaultValueBoolean="false"/>
        </addColumn>
        <createIndex tableName="materials_data_staging" indexName="ix_materials_data_staging_client_materialcode">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-25" author="calin.dumitru">
        <createSequence sequenceName="plant_currency_seq" incrementBy="1000"/>
        <createTable tableName="plant_currency">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="currency" type="varchar"/>
        </createTable>
    </changeSet>


    <changeSet id="1.0-26" author="bejane.zenjelaj">
        <addColumn tableName="material_storage_locations_staging">
            <column name="maintenance_status" type="varchar"></column>
            <column name="flag_material_for_deletion_at_storage_location_level" type="varchar"></column>
            <column name="avato_log_system" type="varchar"></column>
            <column name="avato_version" type="varchar"></column>
            <column name="avato_last_sync" type="varchar"></column>
            <column name="avato_sequence" type="varchar"></column>
        </addColumn>
    </changeSet>

    <changeSet id="1.0-27" author="calin.dumitru">
        <createIndex tableName="materials_data_staging" indexName="ix_materials_data_staging_client_ignore">
            <column name="id"/>
            <column name="ignore"/>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-28" author="bejane.zenjelaj">
        <createSequence sequenceName="material_group_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_group_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_group" type="varchar"/>
            <column name="division" type="varchar"/>
            <column name="authorization_group" type="varchar"/>
        </createTable>
        <createIndex tableName="material_group_staging" indexName="ix_material_group_staging_client_material_group">
            <column name="client"></column>
            <column name="material_group"></column>
        </createIndex>

        <createSequence sequenceName="material_type_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_type_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_type" type="varchar"/>
            <column name="maintenance_status" type="varchar"/>
            <column name="price_control_indicator" type="varchar"/>
            <column name="price_control_mandatory" type="varchar"/>
            <column name="category_group" type="varchar"/>
            <column name="authorization_group" type="varchar"/>
        </createTable>
        <createIndex tableName="material_type_staging" indexName="ix_material_type_staging_client_material_type">
            <column name="client"></column>
            <column name="material_type"></column>
        </createIndex>

        <createSequence sequenceName="units_of_measurement_staging_seq" incrementBy="1000"/>
        <createTable tableName="units_of_measurement_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="units_of_measurement" type="varchar"/>
        </createTable>
        <createIndex tableName="units_of_measurement_staging"
                     indexName="ix_units_of_measurement_staging_client_units_of_measurement">
            <column name="client"></column>
            <column name="units_of_measurement"></column>
        </createIndex>
    </changeSet>

    <changeSet id="1.0-29" author="federico.franceschetti">
        <createTable tableName="event_staging">
            <column name="id" type="integer">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="correlation_id" type="varchar">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="entity" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="event_type" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="rule_id" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="attributes" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>

    <changeSet id="1.0-30" author="bejane.zenjelaj">
        <createSequence sequenceName="basic_material_staging_seq" incrementBy="1000"/>
        <createTable tableName="basic_material_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="basic_material" type="varchar"/>
        </createTable>
        <createIndex tableName="basic_material_staging" indexName="ix_basic_material_staging_client_basic_material">
            <column name="client"></column>
            <column name="basic_material"></column>
        </createIndex>

        <createSequence sequenceName="certificate_type_staging_seq" incrementBy="1000"/>
        <createTable tableName="certificate_type_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="certificate_type" type="varchar"/>
            <column name="short_text" type="varchar"/>
        </createTable>
        <createIndex tableName="certificate_type_staging"
                     indexName="ix_certificate_type_staging_client_certificate_type">
            <column name="client"></column>
            <column name="certificate_type"></column>
        </createIndex>

        <createSequence sequenceName="description_external_material_group_staging_seq" incrementBy="1000"/>
        <createTable tableName="description_external_material_group_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="external_material_group" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="description_external_material_group_staging"
                     indexName="ix_description_external_material_group_staging_client_external_material_group">
            <column name="client"></column>
            <column name="external_material_group"></column>
        </createIndex>


        <createSequence sequenceName="external_material_group_staging_seq" incrementBy="1000"/>
        <createTable tableName="external_material_group_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="external_material_group" type="varchar"/>
        </createTable>
        <createIndex tableName="external_material_group_staging"
                     indexName="ix_external_material_group_staging_client_external_material_group">
            <column name="client"></column>
            <column name="external_material_group"></column>
        </createIndex>

        <createSequence sequenceName="hazardous_material_staging_seq" incrementBy="1000"/>
        <createTable tableName="hazardous_material_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="hazardous_material_number" type="varchar"/>
            <column name="region_code" type="varchar"/>
            <column name="storage_class" type="varchar"/>
        </createTable>
        <createIndex tableName="hazardous_material_staging"
                     indexName="ix_hazardous_material_staging_client_hazardous_material_number">
            <column name="client"></column>
            <column name="hazardous_material_number"></column>
        </createIndex>

        <createSequence sequenceName="laboratory_staging_seq" incrementBy="1000"/>
        <createTable tableName="laboratory_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="laboratory" type="varchar"/>
        </createTable>
        <createIndex tableName="laboratory_staging" indexName="ix_laboratory_staging_client_laboratory">
            <column name="client"></column>
            <column name="laboratory"></column>
        </createIndex>

        <createSequence sequenceName="logistics_handling_group_staging_seq" incrementBy="1000"/>
        <createTable tableName="logistics_handling_group_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="logistics_handling_group" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="description" type="varchar"/>
        </createTable>
        <createIndex tableName="logistics_handling_group_staging"
                     indexName="ix_logistics_handling_group_staging_client_logistics_handling_group">
            <column name="client"></column>
            <column name="logistics_handling_group"></column>
        </createIndex>

        <createSequence sequenceName="mrp_controller_staging_seq" incrementBy="1000"/>
        <createTable tableName="mrp_controller_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="mrp_controller" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="mrp_controller_name" type="varchar"/>
            <column name="purchasing_group" type="varchar"/>
        </createTable>
        <createIndex tableName="mrp_controller_staging" indexName="ix_mrp_controller_staging_client_mrp_controller">
            <column name="client"></column>
            <column name="mrp_controller"></column>
        </createIndex>

        <createSequence sequenceName="mrp_material_level_staging_seq" incrementBy="1000"/>
        <createTable tableName="mrp_material_level_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="mrp_group" type="varchar"/>
        </createTable>
        <createIndex tableName="mrp_material_level_staging" indexName="ix_mrp_material_level_staging_client_plant">
            <column name="client"></column>
            <column name="plant"></column>
        </createIndex>

        <createSequence sequenceName="mrp_type_staging_seq" incrementBy="1000"/>
        <createTable tableName="mrp_type_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="mrp_type" type="varchar"/>
            <column name="mrp_procedure" type="varchar"/>
            <column name="forecast_indicator" type="varchar"/>
            <column name="consumption_forecast" type="varchar"/>
            <column name="mrp_forecast" type="varchar"/>
            <column name="safety_stock" type="varchar"/>
            <column name="reorder_point" type="varchar"/>
            <column name="reduce_forecast" type="varchar"/>
            <column name="firming_type" type="varchar"/>
            <column name="roll_forward" type="varchar"/>
            <column name="plan_regularly" type="varchar"/>
            <column name="reorder_point_with_requirements" type="varchar"/>
            <column name="time_phased_planning" type="varchar"/>
            <column name="screen_sequence" type="varchar"/>
            <column name="planning_method" type="varchar"/>
            <column name="material_staging_requirements" type="varchar"/>
            <column name="order_delay_scheduled" type="varchar"/>
            <column name="delivery_schedule_for_stock_transfer_requisition" type="varchar"/>
            <column name="delivery_schedule_for_stock_transport_agreement" type="varchar"/>
            <column name="order_reservation" type="varchar"/>
            <column name="network_reservation" type="varchar"/>
        </createTable>
        <createIndex tableName="mrp_type_staging" indexName="ix_mrp_type_staging_client_mrp_type">
            <column name="client"></column>
            <column name="mrp_type"></column>
        </createIndex>

        <createSequence sequenceName="qm_certificate_category_staging_seq" incrementBy="1000"/>
        <createTable tableName="qm_certificate_category_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="certificate_type" type="varchar"/>
        </createTable>
        <createIndex tableName="qm_certificate_category_staging"
                     indexName="ix_qm_certificate_category_staging_client_certificate_type">
            <column name="client"></column>
            <column name="certificate_type"></column>
        </createIndex>

        <createSequence sequenceName="qm_control_staging_seq" incrementBy="1000"/>
        <createTable tableName="qm_control_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="qm_control_key" type="varchar"/>
            <column name="technical_delivery_terms" type="varchar"/>
            <column name="quality_assurance_agreement" type="varchar"/>
            <column name="vendor_release" type="varchar"/>
            <column name="certification_required" type="varchar"/>
            <column name="documentation" type="varchar"/>
            <column name="notification_type" type="varchar"/>
            <column name="block_inactive" type="varchar"/>
            <column name="block_invoice" type="varchar"/>
            <column name="rejection_gr" type="varchar"/>
            <column name="control_gr" type="varchar"/>
        </createTable>
        <createIndex tableName="qm_control_staging" indexName="ix_qm_control_staging_client_qm_control_key">
            <column name="client"></column>
            <column name="qm_control_key"></column>
        </createIndex>

        <createSequence sequenceName="qm_text_staging_seq" incrementBy="1000"/>
        <createTable tableName="qm_text_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="language" type="varchar"/>
            <column name="qm_control_key" type="varchar"/>
            <column name="short_text" type="varchar"/>
        </createTable>
        <createIndex tableName="qm_text_staging" indexName="ix_qm_text_staging_client_qm_control_key">
            <column name="client"></column>
            <column name="qm_control_key"></column>
        </createIndex>

        <createSequence sequenceName="avato_sync_staging_seq" incrementBy="1000"/>
        <createTable tableName="avato_sync_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="entity_name" type="varchar"/>
            <column name="start_id" type="integer"/>
            <column name="max_id" type="integer"/>
        </createTable>
    </changeSet>

    <changeSet id="1.0-31" author="nejane.zenjelaj">
        <renameColumn tableName="materials_data_staging" oldColumnName="created_on"
                      newColumnName="material_created_on"/>
        <addColumn tableName="materials_data_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_group_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="basic_material_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="certificate_type_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="characteristics_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="company_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="country_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="currency_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="description_external_material_group_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="long_purchase_order_descriptions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="short_descriptions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="external_material_group_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="global_valuation_category_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="hazardous_material_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="industry_sector_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="item_category_group_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="laboratory_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="logistics_handling_group_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_group_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_status_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_type_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_type_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="mrp_controller_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="mrp_material_level_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="mrp_type_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="plant_currency">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="plant_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="material_storage_locations_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="consumption_order_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="history_order_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="plants_valuation_data_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="product_division_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="purchasing_group_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="qm_certificate_category_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="qm_control_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="qm_text_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="serial_number_management_values_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="storage_location_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="suppliers_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="valuation_class_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="units_of_measure_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="unit_of_measure_definitions_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="units_of_measurement_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="valuation_area_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="1.0-32" author="michele.masili">
        <sql>
            create view material_created_messages as
            select mds.*, array_agg(sds.language || ':' || sds.description) as descriptions
            from materials_data_staging mds
                     left join short_descriptions_staging sds
                               on mds.client = sds.client and mds.material_code = sds.material
            group by mds.id
            order by mds.id;
        </sql>
    </changeSet>
    <changeSet id="1.0-33" author="michele.masili">
        <sql>
            alter table event_staging alter column id type bigint using id::bigint;
        </sql>
    </changeSet>
    <changeSet id="1.0-34" author="matteo.laurenzi">
        <addColumn tableName="materials_data_staging">
            <column name="semantically_analyzed" type="boolean" defaultValueBoolean="true"/>
        </addColumn>
    </changeSet>

    <changeSet id="1.0-35" author="bejane.zenjelaj">
        <createSequence sequenceName="mrp_type_definition_staging_seq" incrementBy="1000"/>
        <createTable tableName="mrp_type_definition_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="mrp_type" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="mrp_type_definition_staging" indexName="ix_mrp_type_definition_staging_client_mrp_type">
            <column name="client"></column>
            <column name="mrp_type"></column>
        </createIndex>
        <createSequence sequenceName="lot_size_definition_staging_seq" incrementBy="1000"/>
        <createTable tableName="lot_size_definition_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="lot_size" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="lot_size_definition_staging" indexName="ix_lot_size_definition_staging_client_lot_size">
            <column name="client"></column>
            <column name="lot_size"></column>
        </createIndex>
    </changeSet>
    <changeSet id="1.0-36" author="bejane.zenjelaj">
        <addColumn tableName="plant_definitions_staging">
            <column name="enabled" type="boolean"/>
        </addColumn>
    </changeSet>
    <changeSet id="1.0-37" author="bejane.zenjelaj">
        <sql>
            create view material_extension_messages as
            select mds.*
            from materials_data_staging mds
            group by mds.id
            order by mds.id;
        </sql>
        <sql>
            create view material_plant_valuation_messages as
            select mds.*
            from materials_data_staging mds
            group by mds.id
            order by mds.id;
        </sql>
    </changeSet>
    <changeSet id="1.0-38" author="bejane.zenjelaj">
        <addColumn tableName="country_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="material_group_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <dropTable tableName="material_group_keys_staging"/>
        <addColumn tableName="valuation_class_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="material_type_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <dropTable tableName="material_type_keys_staging"/>
        <addColumn tableName="industry_sector_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="material_status_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="global_valuation_category_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="logistics_handling_group_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="item_category_group_keys_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="qm_certificate_category_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="qm_control_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
        <addColumn tableName="external_material_group_staging">
            <column name="enabled" type="boolean"></column>
        </addColumn>
    </changeSet>

    <changeSet id="1.0-39" author="jualba.qorri">
        <createSequence sequenceName="float_scheduling_definitions_staging_seq" incrementBy="1000"/>
        <createTable tableName="float_scheduling_definitions_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="sched_marg_key" type="varchar"/>
            <column name="enabled" type="boolean"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>
    <changeSet id="1.0-40" author="b.zenjelaj">
        <createSequence sequenceName="material_consumption_staging_seq" incrementBy="1000"/>
        <createTable tableName="material_consumption_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="fiscal_year" type="varchar"/>
            <column name="follow_on_record_nr" type="varchar"/>
            <column name="period_indicator" type="varchar"/>
            <column name="total_consumption_01" type="varchar"/>
            <column name="total_consumption_02" type="varchar"/>
            <column name="total_consumption_03" type="varchar"/>
            <column name="total_consumption_04" type="varchar"/>
            <column name="total_consumption_05" type="varchar"/>
            <column name="total_consumption_06" type="varchar"/>
            <column name="total_consumption_07" type="varchar"/>
            <column name="total_consumption_08" type="varchar"/>
            <column name="total_consumption_09" type="varchar"/>
            <column name="total_consumption_10" type="varchar"/>
            <column name="total_consumption_11" type="varchar"/>
            <column name="total_consumption_12" type="varchar"/>
            <column name="total_consumption_13" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
