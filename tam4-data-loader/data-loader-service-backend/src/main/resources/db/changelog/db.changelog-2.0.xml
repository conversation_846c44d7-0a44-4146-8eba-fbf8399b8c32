<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">
    <changeSet id="2.0.0" author="michele.masili">
        <addColumn tableName="event_staging">
            <column name="old_value" type="text"/>
        </addColumn>
        <addColumn tableName="event_staging">
            <column name="new_value" type="text"/>
        </addColumn>
    </changeSet>
    <changeSet id="2.0.1" author="b.z">
        <createSequence sequenceName="approved_manufactured_parts_staging_seq" incrementBy="1000"/>
        <createTable tableName="approved_manufactured_parts_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="record_number" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="manufacturer_number" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>

        <createSequence sequenceName="qm_record_staging_seq" incrementBy="1000"/>
        <createTable tableName="qm_record_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="internal_counter" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="vendor_account_number" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="valid_date" type="varchar"/>
            <column name="release_quantity_active" type="varchar"/>
            <column name="base_unit_of_measure" type="varchar"/>
            <column name="released_material_quantity" type="varchar"/>
            <column name="material_quantity_order" type="varchar"/>
            <column name="blocking_version" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="blocked_function" type="varchar"/>
            <column name="inspection_type" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>
    <changeSet id="2.0.2" author="b.z">
        <createSequence sequenceName="business_address_service_staging_seq" incrementBy="1000"/>
        <createTable tableName="business_address_service_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="address_number" type="varchar"/>
            <column name="valid_from_date" type="varchar"/>
            <column name="address_version_id" type="varchar"/>
            <column name="valid_to_date" type="varchar"/>
            <column name="name_1" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="city_postal" type="varchar"/>
            <column name="po_box" type="varchar"/>
            <column name="street" type="varchar"/>
            <column name="house_number" type="varchar"/>
            <column name="county" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="region" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>
    <changeSet id="2.0.3" author="b.z">
        <createSequence sequenceName="characteristics_cabn_staging_seq" incrementBy="1000"/>
        <createTable tableName="characteristics_cabn_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="code" type="varchar"/>
            <column name="name" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>

    <changeSet id="2.0.4" author="b.z">
        <createSequence sequenceName="purchasing_document_item_staging_seq" incrementBy="1000"/>
        <createTable tableName="purchasing_document_item_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="purchase_order_quantity" type="varchar"/>
            <column name="net_order_value" type="varchar"/>
            <column name="purchasing_document_nr" type="varchar"/>
            <column name="item_nr" type="varchar"/>
            <column name="deletion_indicator" type="varchar"/>
            <column name="rfq_status" type="varchar"/>
            <column name="short_text" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="material_group" type="varchar"/>
            <column name="purchasing_record_nr" type="varchar"/>
            <column name="target_quantity" type="varchar"/>
            <column name="purchasing_unit_of_measure" type="varchar"/>
            <column name="order_price_unit" type="varchar"/>
            <column name="conversion_numerator_order_unit" type="varchar"/>
            <column name="conversion_numerator_base_unit" type="varchar"/>
            <column name="conversion_denominator_order_unit" type="varchar"/>
            <column name="conversion_denominator_base_unit" type="varchar"/>
            <column name="purchasing_group_net_price" type="varchar"/>
            <column name="price_unit" type="varchar"/>
            <column name="gross_order" type="varchar"/>
            <column name="delivery_indicator" type="varchar"/>
            <column name="item_category" type="varchar"/>
            <column name="account_assignment_category" type="varchar"/>
            <column name="invoice_receipt_indicator" type="varchar"/>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="2.0.5" author="michele.masili">
        <addColumn tableName="suppliers_definitions_staging">
            <column name="name2" type="text"/>
            <column name="name3" type="text"/>
            <column name="name4" type="text"/>
            <column name="address" type="text"/>
        </addColumn>
    </changeSet>
    <changeSet id="2.0.6" author="b.z">
        <createSequence sequenceName="company_text_staging_seq" incrementBy="1000"/>
        <createTable tableName="company_text_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="company_code" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="standard_text_name" type="varchar"/>
            <column name="text_usage" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="company_text_staging" indexName="ix_company_text_staging_client_companycode">
            <column name="client"/>
            <column name="company_code"/>
        </createIndex>
        <createSequence sequenceName="dimension_text_staging_seq" incrementBy="1000"/>
        <createTable tableName="dimension_text_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="language_key" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="dimension_key" type="varchar"/>
            <column name="dimension_text" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="dimension_text_staging" indexName="ix_dimension_text_staging_client_dimensionkey">
            <column name="client"/>
            <column name="dimension_key"/>
        </createIndex>
        <createSequence sequenceName="purchasing_document_header_staging_seq" incrementBy="1000"/>
        <createTable tableName="purchasing_document_header_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="purchasing_document_nr" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="company_code" type="varchar"/>
            <column name="purchasing_document_category" type="varchar"/>
            <column name="purchasing_document_type" type="varchar"/>
            <column name="deletion_indicator" type="varchar"/>
            <column name="purchasing_document_status" type="varchar"/>
            <column name="record_creation_date" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="currency_key" type="varchar"/>
            <column name="exchange_rate" type="varchar"/>
            <column name="address_number" type="varchar"/>
            <column name="vendor_account_number" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="purchasing_document_header_staging"
                     indexName="ix_purchasing_document_header_staging_client_companycode">
            <column name="client"/>
            <column name="company_code"/>
        </createIndex>
        <createSequence sequenceName="iso_unit_of_measure_staging_seq" incrementBy="1000"/>
        <createTable tableName="iso_unit_of_measure_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="iso_unit_of_measurement" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="iso_measurement" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="iso_unit_of_measure_staging"
                     indexName="ix_iso_unit_of_measure_staging_client_isounitofmeasurement">
            <column name="client"/>
            <column name="iso_unit_of_measurement"/>
        </createIndex>
        <createSequence sequenceName="iso_units_of_measurement_staging_seq" incrementBy="1000"/>
        <createTable tableName="iso_units_of_measurement_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="iso_units_of_measurement" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="iso_units_of_measurement_staging"
                     indexName="ix_iso_units_of_measurement_staging_client_isounitsofmeasurement">
            <column name="client"/>
            <column name="iso_units_of_measurement"/>
        </createIndex>
        <createSequence sequenceName="valuation_class_staging_seq" incrementBy="1000"/>
        <createTable tableName="valuation_class_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="valuation_class" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="account_category_reference" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="valuation_class_staging"
                     indexName="ix_iso_valuation_class_staging_client_valuationclass">
            <column name="client"/>
            <column name="valuation_class"/>
        </createIndex>
        <createSequence sequenceName="mmv_material_status_staging_seq" incrementBy="1000"/>
        <createTable tableName="mmv_material_status_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="material_status" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="purchasing_message" type="varchar"/>
            <column name="bom_header_message" type="varchar"/>
            <column name="routing_message" type="varchar"/>
            <column name="requirement_message" type="varchar"/>
            <column name="mrp_message" type="varchar"/>
            <column name="production_item_message" type="varchar"/>
            <column name="production_header_message" type="varchar"/>
            <column name="plant_maintenance_message" type="varchar"/>
            <column name="inventory_message" type="varchar"/>
            <column name="forecasting_message" type="varchar"/>
            <column name="prt_order_message" type="varchar"/>
            <column name="qm_message" type="varchar"/>
            <column name="wm_change_message" type="varchar"/>
            <column name="wm_order_message" type="varchar"/>
            <column name="mc_estimate_procedure" type="varchar"/>
            <column name="long_term_planning_message" type="varchar"/>
            <column name="distribution_lock_indicator" type="varchar"/>
            <column name="ale_profile_name" type="varchar"/>
            <column name="locked_for_purchase" type="varchar"/>
            <column name="blocked_for_po" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="mmv_material_status_staging"
                     indexName="ix_mmv_material_status_staging_client_materialstatus">
            <column name="client"/>
            <column name="material_status"/>
        </createIndex>
    </changeSet>
    <changeSet id="2.0.7" author="Michele Masili">
        <createTable tableName="outbound_queue">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="message_type" type="text"/>
            <column name="exchange" type="text"/>
            <column name="content" type="text"/>
            <column name="sent" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="2.0.8" author="daniele.canteri">
        <createTable tableName="temporary_aggregated_send_plants">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="text"/>
            <column name="material_code" type="text"/>
            <column name="revision_number" type="text"/>
            <column name="deletion_flag" type="boolean"/>
            <column name="material_type" type="text"/>
            <column name="industry_sector" type="text"/>
            <column name="material_group" type="text"/>
            <column name="old_material_number" type="text"/>
            <column name="base_unit_of_measurement" type="text"/>
            <column name="product_division" type="text"/>
            <column name="authorization_group" type="text"/>
            <column name="cross_plant_material_status" type="text"/>
            <column name="material_status_valid_from_date" type="text"/>
            <column name="manufacturer_part_number" type="text"/>
            <column name="manufacturer_code" type="text"/>
            <column name="generic_item_group" type="text"/>
            <column name="purchasing_measurement_units" type="text"/>
            <column name="material_created_on" type="text"/>
            <column name="external_material_group" type="text"/>
            <column name="weight_unit" type="text"/>
            <column name="net_weight" type="double precision"/>
            <column name="gross_weight" type="double precision"/>
            <column name="size_dimension" type="text"/>
            <column name="hazardous_material_number" type="text"/>
            <column name="ignore" type="boolean"/>
            <column name="created_on" type="timestamptz"/>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="text"/>
            <column name="semantically_analyzed" type="boolean"/>
            <column name="materialplant" type="text[]"/>
            <column name="plants" type="text[]"/>
            <column name="storage" type="text[]"/>
        </createTable>
    </changeSet>
    <changeSet id="2.0.9" author="b.zenjelaj">
        <addColumn tableName="suppliers_definitions_staging">
            <column name="supplier_name_normalized" type="text"></column>
            <column name="supplier_group_normalized" type="text"></column>
        </addColumn>
    </changeSet>
    <changeSet id="1.0-10" author="redinela.allaraj">
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_01"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_02"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_03"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_04"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_05"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_06"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_07"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_08"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_09"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_10"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_11"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_12"
                        newDataType="double precision"/>
        <modifyDataType tableName="material_consumption_staging" columnName="total_consumption_13"
                        newDataType="double precision"/>
    </changeSet>
    <changeSet id="2.0.11" author="daniele.canteri">
        <createTable tableName="inbound_queue">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="message_type" type="text"/>
            <column name="queue" type="text"/>
            <column name="content" type="text"/>
            <column name="received_time" type="bigint"/>
            <column name="retries" type="int"/>
            <column name="retry_after_time" type="bigint"/>
            <column name="last_retry_time" type="bigint"/>
        </createTable>
    </changeSet>
    <changeSet id="2.0.12" author="federico.franceschetti">
        <sql>alter table purchasing_document_item_staging alter column net_order_value type double precision using net_order_value::double precision</sql>
        <sql>alter table purchasing_document_item_staging alter column purchase_order_quantity type double precision using purchase_order_quantity::double precision</sql>
    </changeSet>

    <changeSet id="2.0.13" author="inis.bali">
        <sql>
            drop view material_extension_messages;
        </sql>

        <sql>
            drop view material_plant_valuation_messages;
        </sql>

        <sql>
            drop view material_created_messages;
        </sql>

        <modifyDataType tableName="units_of_measure_staging" columnName="numerator" newDataType="numeric"/>
        <modifyDataType tableName="units_of_measure_staging" columnName="denominator" newDataType="numeric"/>
        <modifyDataType tableName="consumption_order_staging" columnName="consumption_quantity" newDataType="numeric"/>
        <modifyDataType tableName="consumption_order_staging" columnName="consumption_amount" newDataType="numeric"/>
        <modifyDataType tableName="material_storage_locations_staging" columnName="valuated_unrestricted_use_stock"
                        newDataType="numeric"/>
        <modifyDataType tableName="material_storage_locations_staging" columnName="stock_in_transfer"
                        newDataType="numeric"/>
        <modifyDataType tableName="material_storage_locations_staging" columnName="stock_in_quality_inspection"
                        newDataType="numeric"/>
        <modifyDataType tableName="material_storage_locations_staging" columnName="blocked_stock"
                        newDataType="numeric"/>
        <modifyDataType tableName="materials_data_staging" columnName="material_status_valid_from_date"
                        newDataType="bigint"/>
        <modifyDataType tableName="materials_data_staging" columnName="net_weight" newDataType="numeric"/>
        <modifyDataType tableName="materials_data_staging" columnName="gross_weight" newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="lead_time_in_days" newDataType="int"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="reorder_point" newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="safety_stock" newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="maximum_stock_level"
                        newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="min_lot_size" newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="minimum_safety_stock"
                        newDataType="numeric"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="valid_from_date" newDataType="bigint"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="in_house_production_time"
                        newDataType="int"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="goods_receipt_processing_time_in_days"
                        newDataType="int"/>
        <modifyDataType tableName="materials_plant_data_staging" columnName="batch_management_requirement"
                        newDataType="boolean"/>
        <modifyDataType tableName="history_order_staging" columnName="ordered_amount" newDataType="numeric"/>
        <modifyDataType tableName="history_order_staging" columnName="ordered_quantity" newDataType="numeric"/>
        <modifyDataType tableName="plants_valuation_data_staging" columnName="inventory_amount" newDataType="numeric"/>
        <modifyDataType tableName="plants_valuation_data_staging" columnName="moving_average_price"
                        newDataType="numeric"/>
        <modifyDataType tableName="plants_valuation_data_staging" columnName="standard_price" newDataType="numeric"/>
        <modifyDataType tableName="plants_valuation_data_staging" columnName="price_unit" newDataType="int"/>
        <modifyDataType tableName="plants_valuation_data_staging" columnName="total_value" newDataType="numeric"/>

        <renameColumn tableName="material_group_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="material_type_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="description_external_material_group_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="mrp_type_definition_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="certificate_type_staging" oldColumnName="language_key" newColumnName="language"/>
        <renameColumn tableName="lot_size_definition_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="country_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="global_valuation_category_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="industry_sector_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="item_category_group_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="material_status_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="product_division_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="unit_of_measure_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>

        <sql>
            create view material_extension_messages as
            select mds.*
            from materials_data_staging mds
            group by mds.id
            order by mds.id;
        </sql>

        <sql>
            create view material_plant_valuation_messages as
            select mds.*
            from materials_data_staging mds
            group by mds.id
            order by mds.id;
        </sql>

        <sql>
            create view material_created_messages as
            select mds.*, array_agg(sds.language || ':' || sds.description) as descriptions
            from materials_data_staging mds
                     left join short_descriptions_staging sds
                               on mds.client = sds.client and mds.material_code = sds.material
            group by mds.id
            order by mds.id;
        </sql>
    </changeSet>

    <changeSet id="2.0.14" author="calin.dumitru">
        <addColumn tableName="materials_data_staging">
            <column name="basic_material" type="varchar"/>
            <column name="document_number" type="varchar"/>
            <column name="laboratory" type="varchar"/>
            <column name="batch_management_requirement_indicator" type="boolean"/>
        </addColumn>
    </changeSet>

    <changeSet id="2.0.15" author="calin.dumitru">
        <renameColumn tableName="valuation_class_definitions_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
        <renameColumn tableName="qm_record_staging" oldColumnName="sap_language_key" newColumnName="language"/>
        <renameColumn tableName="plant_definitions_staging" oldColumnName="sap_language_key" newColumnName="language"/>
        <renameColumn tableName="company_staging" oldColumnName="sap_language_key" newColumnName="language"/>
    </changeSet>

    <changeSet id="2.0.16" author="calin.dumitru">
        <renameColumn tableName="logistics_handling_group_staging" oldColumnName="sap_language_key"
                      newColumnName="language"/>
    </changeSet>

    <changeSet id="2.0.17" author="calin.dumitru">
        <!--dropping columns to keep them at the end of the table-->
        <dropColumn tableName="temporary_aggregated_send_plants" columnName="materialplant"/>
        <dropColumn tableName="temporary_aggregated_send_plants" columnName="plants"/>
        <dropColumn tableName="temporary_aggregated_send_plants" columnName="storage"/>
        <addColumn tableName="temporary_aggregated_send_plants">
            <column name="basic_material" type="varchar"/>
            <column name="document_number" type="varchar"/>
            <column name="laboratory" type="varchar"/>
            <column name="batch_management_requirement_indicator" type="boolean"/>
            <column name="materialplant" type="text[]"/>
            <column name="plants" type="text[]"/>
            <column name="storage" type="text[]"/>
        </addColumn>
    </changeSet>

    <changeSet id="2.0.18" author="redinela.allaraj">
        <addColumn tableName="characteristics_cabn_staging">
            <column name="client" type="text"/>
        </addColumn>
        <addColumn tableName="characteristics_staging">
            <column name="client" type="text"/>
        </addColumn>
        <createIndex tableName="characteristics_cabn_staging" indexName="ix_characteristics_cabn_staging_client_code">
            <column name="client"/>
            <column name="code"/>
        </createIndex>
    </changeSet>

    <changeSet id="2.0.19" author="calin.dumitru">
        <createIndex tableName="characteristics_staging" indexName="ix_characteristics_staging_client_material">
            <column name="client"/>
            <column name="material_code"/>
        </createIndex>
        <dropIndex tableName="characteristics_staging" indexName="ix_characteristics_staging_client_material"/>
    </changeSet>

    <changeSet id="2.0.20" author="calin.dumitru">
        <createIndex tableName="outbound_queue" indexName="ix_outbound_queue_sent">
            <column name="sent"/>
        </createIndex>
    </changeSet>

    <changeSet id="2.0.21" author="christian.biasi">
        <createSequence sequenceName="client_staging_seq" incrementBy="1000"/>
        <createTable tableName="client_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="name" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>

    <changeSet id="2.0.22" author="christian.biasi">
        <addColumn tableName="materials_data_staging">
            <column name="md_domain" type="text"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
