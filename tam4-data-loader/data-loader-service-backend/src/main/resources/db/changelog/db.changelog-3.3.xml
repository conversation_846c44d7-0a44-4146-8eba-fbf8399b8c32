<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">
    <changeSet id="3.3.0" author="matteo.laurenzi">
        <addColumn tableName="materials_data_staging">
            <column name="service_valuation_class" type="varchar"/>
        </addColumn>
    </changeSet>

    <changeSet id="3.3.1" author="allaraj.reddinela">
        <sql>UPDATE long_purchase_order_descriptions_staging
             SET description = 'BEST';</sql>
        <renameTable catalogName="cat"
                     newTableName="long_descriptions_staging"
                     oldTableName="long_purchase_order_descriptions_staging"
                     schemaName="public"/>
        <renameColumn catalogName="cat"
                      columnDataType="varchar"
                      newColumnName="type"
                      oldColumnName="description"
                      remarks="A String"
                      schemaName="public"
                      tableName="long_descriptions_staging"/>
        <renameColumn catalogName="cat"
                      columnDataType="varchar"
                      newColumnName="long_description"
                      oldColumnName="po_description"
                      remarks="A String"
                      schemaName="public"
                      tableName="long_descriptions_staging"/>
    </changeSet>

</databaseChangeLog>
