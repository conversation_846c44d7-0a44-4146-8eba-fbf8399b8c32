<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

    <changeSet id="2024.19.1_001" author="roberto.gabrieli">
        <sql>ALTER TABLE outbound_queue ALTER COLUMN id DROP IDENTITY IF EXISTS;</sql>
        <sql>ALTER TABLE outbound_queue ALTER COLUMN id TYPE bigint USING id::bigint;</sql>
        <sql>alter table outbound_queue alter column id drop default;</sql>
    </changeSet>
    <changeSet id="2024.19.1_002" author="roberto.gabrieli">
        <sql>create sequence if not exists outbound_queue_id_seq increment by 1;</sql>
    </changeSet>
    <changeSet id="2024.19.1_003" author="roberto.gabrieli" runAlways="false">
        <sql>alter table outbound_queue alter column id set default nextval('outbound_queue_id_seq');</sql>
        <sql>alter sequence outbound_queue_id_seq cache 100000;</sql>
        <sql>SELECT SETVAL('outbound_queue_id_seq', (Select coalesce(max(id), 1) from outbound_queue));</sql>
    </changeSet>
    <changeSet id="2024.19.1_004" author="roberto.gabrieli">
        <sql>alter sequence iso_units_of_measurement_staging_seq increment 100000;</sql>
        <sql>alter sequence materials_plant_data_staging_seq increment 100000;</sql>
        <sql>alter sequence plants_valuation_data_staging_seq increment 100000;</sql>
        <sql>alter sequence short_descriptions_staging_seq increment 100000;</sql>
        <sql>alter sequence long_purchase_order_descriptions_staging_seq increment 100000;</sql>
        <sql>alter sequence history_order_staging_seq increment 100000;</sql>
        <sql>alter sequence consumption_order_staging_seq increment 100000;</sql>
        <sql>alter sequence basic_material_staging_seq increment 100000;</sql>
        <sql>alter sequence characteristics_staging_seq increment 100000;</sql>
        <sql>alter sequence material_storage_locations_staging_seq increment 100000;</sql>
    </changeSet>

</databaseChangeLog>
