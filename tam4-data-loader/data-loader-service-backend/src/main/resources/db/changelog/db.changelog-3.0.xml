<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">
    <changeSet id="3.0.0" author="federico.franceschetti">
        <createTable tableName="events">
            <column name="event_id" type="UUID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="entity_type" type="varchar"/>
            <column name="status" type="varchar"/>
            <column name="operation_type" type="varchar"/>
            <column name="entity_client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="plant_code" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()"/>
        </createTable>
        <createTable tableName="event_changes">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="field" type="varchar"/>
            <column name="source" type="varchar"/>
            <column name="old_value" type="varchar"/>
            <column name="new_value" type="varchar"/>
            <column name="event_id" type="UUID">
                <constraints nullable="false" references="events" foreignKeyName="fk_events_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationships">
            <column name="operation_type" type="varchar"/>
            <column name="relationship_id" type="UUID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="status" type="varchar"/>
            <column name="relationship_type" type="varchar"/>
            <column name="note" type="varchar"/>
            <column name="event_id" type="UUID">
                <constraints nullable="false" references="events" foreignKeyName="fk_relationships_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationship_materials">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="role" type="varchar"/>
            <column name="relationship_id" type="UUID">
                <constraints nullable="false" references="relationships"
                             foreignKeyName="fk_relationships_relationship_id" deleteCascade="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="3.0.0-1" author="michele.masili">
        <createSequence sequenceName="inbound_queue_seq" incrementBy="10000"/>
    </changeSet>
    <changeSet id="3.0.0-2" author="michele.masili">
        <dropTable tableName="relationship_materials"/>
        <dropTable tableName="relationships"/>
        <dropTable tableName="event_changes"/>
        <dropTable tableName="events"/>

        <createTable tableName="events">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="correlation_id" type="UUID"/>
            <column name="entity_type" type="varchar"/>
            <column name="status" type="varchar"/>
            <column name="operation_type" type="varchar"/>
            <column name="entity_client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="plant_code" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()"/>
        </createTable>
        <createTable tableName="event_changes">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="field" type="varchar"/>
            <column name="source" type="varchar"/>
            <column name="old_value" type="varchar"/>
            <column name="new_value" type="varchar"/>
            <column name="event_id" type="bigint">
                <constraints nullable="false" references="events" foreignKeyName="fk_events_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationships">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="operation_type" type="varchar"/>
            <column name="relationship_id" type="UUID"/>
            <column name="status" type="varchar"/>
            <column name="relationship_type" type="varchar"/>
            <column name="note" type="varchar"/>
            <column name="event_id" type="bigint">
                <constraints nullable="false" references="events" foreignKeyName="fk_relationships_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationship_materials">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="role" type="varchar"/>
            <column name="relationship_id" type="bigint">
                <constraints nullable="false" references="relationships"
                             foreignKeyName="fk_relationships_relationship_id" deleteCascade="true"/>
            </column>
        </createTable>

    </changeSet>
    <changeSet id="3.0.0-3" author="michele.masili">

        <dropTable tableName="relationship_materials"/>
        <dropTable tableName="relationships"/>
        <dropTable tableName="event_changes"/>
        <dropTable tableName="events"/>

        <createTable tableName="deprecated_events">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="correlation_id" type="UUID"/>
            <column name="entity_type" type="varchar"/>
            <column name="status" type="varchar"/>
            <column name="operation_type" type="varchar"/>
            <column name="entity_client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="plant_code" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()"/>
        </createTable>
        <createTable tableName="event_changes">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="field" type="varchar"/>
            <column name="source" type="varchar"/>
            <column name="old_value" type="varchar"/>
            <column name="new_value" type="varchar"/>
            <column name="event_id" type="bigint">
                <constraints nullable="false" references="deprecated_events" foreignKeyName="fk_events_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationships">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="operation_type" type="varchar"/>
            <column name="relationship_id" type="UUID"/>
            <column name="status" type="varchar"/>
            <column name="relationship_type" type="varchar"/>
            <column name="note" type="varchar"/>
            <column name="event_id" type="bigint">
                <constraints nullable="false" references="deprecated_events" foreignKeyName="fk_relationships_event_id"
                             deleteCascade="true"/>
            </column>
        </createTable>
        <createTable tableName="relationship_materials">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="material_id" type="UUID"/>
            <column name="role" type="varchar"/>
            <column name="relationship_id" type="bigint">
                <constraints nullable="false" references="relationships"
                             foreignKeyName="fk_relationships_relationship_id" deleteCascade="true"/>
            </column>
        </createTable>

    </changeSet>
    <changeSet id="3.0.0-4" author="michele.masili">
        <createTable tableName="events">
            <column name="id" type="bigserial">
                <constraints nullable="false" unique="true" primaryKey="true" primaryKeyName="new_events_pkey"/>
            </column>
            <column name="revision" type="smallint"/>
            <column name="process_id" type="UUID"/>
            <column name="master_data_id" type="UUID"/>
            <column name="entity_type" type="text"/>
            <column name="status" type="text"/>
            <column name="event_data" type="jsonb"/>
            <column name="received_data" type="jsonb"/>
            <column name="last_updated_date" type="timestamp"/>
            <column name="created_date" type="timestamp"/>
        </createTable>
    </changeSet>
    <changeSet id="3.0.0-5" author="michele.masili">
        <addColumn tableName="events">
            <column name="client" type="text"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-6" author="federico.franceschetti">
        <addColumn tableName="material_status_keys_staging">
            <column name="obsolete" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-7" author="stefano.battistin">
        <addColumn tableName="materials_data_staging">
            <column name="product_hierarchy" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-8" author="emanuele.giordano">
        <addColumn tableName="materials_data_staging">
            <column name="famiglia" type="text"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="sotto_famiglia" type="text"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="specifica_tecnica" type="text"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="edizione" type="text"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="revisione" type="text"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="data_custom" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-8" author="federico.franceschetti">
        <addColumn tableName="event_staging">
            <column name="log_system" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-9" author="federico.franceschetti">
        <sql>create index on outbound_queue(sent);</sql>
    </changeSet>
    <changeSet id="3.0.0-10" author="emanuele.giordano">
        <addColumn tableName="relationships">
            <column name="correlation_id" type="UUID"/>
        </addColumn>
        <addColumn tableName="relationships">
            <column name="last_updated_date" type="datetime"/>
        </addColumn>
        <addColumn tableName="relationships">
            <column name="created_date" type="datetime"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="intrastat_code" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-11" author="federico.franceschetti">
        <addColumn tableName="materials_data_staging">
            <column name="volume" type="decimal"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="volume_unit" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="internation_article_number" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-12" author="emanuele.giordano">
        <addColumn tableName="materials_plant_data_staging">
            <column name="control_code_consumption" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="material_cfop_category" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="period_indicator" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="special_procurement_type" type="varchar"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="checking_group_availability_check" type="varchar"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.0.0-13" author="emanuele.giordano">
        <addColumn tableName="plants_valuation_data_staging">
            <column name="origin_material" type="varchar"/>
        </addColumn>
        <addColumn tableName="plants_valuation_data_staging">
            <column name="usage_material" type="varchar"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
