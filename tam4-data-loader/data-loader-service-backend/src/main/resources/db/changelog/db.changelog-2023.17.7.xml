<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

  <changeSet id="2023.17.7-CREAC-3726-001" author="roberto.gabrieli">
    <createSequence sequenceName="standard_api_received_payloads_id_seq" incrementBy="1000"/>
    <createTable tableName="standard_api_received_payloads">
      <column name="id" type="bigint" defaultValue="nextval('standard_api_received_payloads_id_seq')">
        <constraints nullable="false" primaryKey="true"/>
      </column>
      <column name="message_type" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="record_key" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="payload_hash" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="payload_content" type="text"/>
      <column name="first_received_tms" type="timestamp" defaultValue="NOW()"/>
      <column name="received_counter" type="int" defaultValue="0"/>
      <column name="last_received_tms" type="timestamp" defaultValue="NOW()"/>
    </createTable>
  </changeSet>

</databaseChangeLog>
