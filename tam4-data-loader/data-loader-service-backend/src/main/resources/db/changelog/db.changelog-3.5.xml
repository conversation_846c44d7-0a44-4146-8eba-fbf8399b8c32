<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

    <changeSet id="3.5.0" author="davide.leonardi">
        <addColumn tableName="units_of_measure_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="approved_manufactured_parts_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="basic_material_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="business_address_service_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="characteristics_cabn_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="characteristics_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="client_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="company_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="company_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="consumption_order_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="country_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="currency_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="description_external_material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="dimension_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="external_material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="global_valuation_category_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="hazardous_material_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="industry_sector_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="iso_unit_of_measure_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="iso_units_of_measurement_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="item_category_group_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="laboratory_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="logistics_handling_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="long_descriptions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="lot_size_definition_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_consumption_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_group_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="plants_valuation_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_status_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_storage_locations_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_type_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mmv_material_status_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_controller_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_material_level_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_type_definition_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="plant_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="history_order_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="product_division_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_document_header_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="certificate_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_certificate_category_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_control_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="float_scheduling_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="short_descriptions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="unit_of_measure_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_area_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_class_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_class_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_document_item_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_record_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
    </changeSet>
    <changeSet id="3.5.1" author="davide.leonardi">
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="units_of_measure_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="approved_manufactured_parts_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="basic_material_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="business_address_service_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="characteristics_cabn_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="characteristics_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="client_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="company_text_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="company_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="consumption_order_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="country_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="currency_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="description_external_material_group_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="dimension_text_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="external_material_group_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="global_valuation_category_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="hazardous_material_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="industry_sector_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="iso_unit_of_measure_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="iso_units_of_measurement_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="item_category_group_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="laboratory_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="logistics_handling_group_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="long_descriptions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="lot_size_definition_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="material_consumption_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="materials_data_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="material_group_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="material_group_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="materials_plant_data_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="plants_valuation_data_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="material_status_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="material_storage_locations_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="material_type_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="material_type_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="mmv_material_status_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="mrp_controller_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="mrp_material_level_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="mrp_type_definition_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="mrp_type_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="plant_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="history_order_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="product_division_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="purchasing_document_header_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="purchasing_group_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="certificate_type_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="qm_certificate_category_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="qm_text_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="qm_control_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="float_scheduling_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="short_descriptions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="unit_of_measure_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="valuation_area_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="valuation_class_definitions_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="valuation_class_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false"
                         tableName="purchasing_document_item_staging"/>
        <addDefaultValue columnName="api_sent" defaultValueBoolean="false" tableName="qm_record_staging"/>
    </changeSet>

    <changeSet id="3.5.2" author="davide.leonardi">
        <sql>ALTER TABLE public.validation_log_simple
            ADD start_date timestamp NULL;</sql>
        <sql>ALTER TABLE public.validation_log_simple
            ADD end_date timestamp NULL;</sql>
        <sql>ALTER TABLE public.validation_log_simple DROP COLUMN executiontime;</sql>
    </changeSet>

    <changeSet id="3.5.3" author="davide.leonardi">
        <addColumn tableName="suppliers_definitions_staging">
            <column name="api_sent" type="boolean" defaultValueBoolean="false"/>
        </addColumn>
        <addColumn tableName="storage_location_definitions_staging">
            <column name="api_sent" type="boolean" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="3.5.4" author="roberto.gabrieli">
        <comment>This is needed because if you add the column and set the value but you don't update it,
            it'll get null and in some already running envs will blow</comment>
        <sql>update units_of_measure_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update approved_manufactured_parts_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update basic_material_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update business_address_service_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update characteristics_cabn_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update characteristics_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update client_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update company_text_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update company_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update consumption_order_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update country_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update currency_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update description_external_material_group_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update dimension_text_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update external_material_group_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update global_valuation_category_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update hazardous_material_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update industry_sector_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update iso_unit_of_measure_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update iso_units_of_measurement_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update item_category_group_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update laboratory_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update logistics_handling_group_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update long_descriptions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update lot_size_definition_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_consumption_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update materials_data_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_group_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_group_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update materials_plant_data_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update plants_valuation_data_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_status_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_storage_locations_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_type_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update material_type_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update mmv_material_status_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update mrp_controller_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update mrp_material_level_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update mrp_type_definition_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update mrp_type_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update plant_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update history_order_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update product_division_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update purchasing_document_header_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update purchasing_group_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update certificate_type_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update qm_certificate_category_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update qm_text_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update qm_control_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update float_scheduling_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update short_descriptions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update unit_of_measure_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update valuation_area_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update valuation_class_definitions_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update valuation_class_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update purchasing_document_item_staging set  api_sent=false where api_sent is null;</sql>
        <sql>update qm_record_staging set  api_sent=false where api_sent is null;</sql>
    </changeSet>
</databaseChangeLog>
