server:
  forward-headers-strategy: native
  error:
    include-message: always
  tomcat:
    basedir: /tmp/data_loader
    max-http-header-size: 80KB

spring:
  banner:
    location: classpath:banner.txt

  batch:
    job:
      enabled: false
#    jdbc:
#      initialize-schema: never

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  devtools:
    restart:
      enabled: false
  cache:
#    cache-names: plantCurrency, supplierNames, materialGroupDescriptions, users
    caffeine:
      spec: maximumSize=500, expireAfterAccess=900s, expireAfterWrite=1800s
    type: caffeine
  liquibase:

    change-log: classpath:db/changelog/db.changelog-master.xml
  jpa:
    show-sql: false
    properties:
      hibernate:
        dialect: 'org.hibernate.dialect.PostgreSQLDialect'
        generate_statistics: false
        order_updates: true
        jdbc:
          batch_size: 1000
        temp:
          # disable error log during startup, see https://stackoverflow.com/questions/4588755/disabling-contextual-lob-creation-as-createclob-method-threw-error
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: validate
  rabbitmq:
    listener:
      simple:
        retry:
          max-attempts: 5
          initial-interval: 10000
          enabled: true
          multiplier: 2
          #max-interval: 60000
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
management:
  endpoints:
    enabled-by-default: false
    web:
      base-path: /data-loader/api
      exposure:
        include: health,refresh
  endpoint:
    health:
      enabled: true
    refresh:
      enabled: true
jasypt:
  encryptor:
    password: Cre@ctiv3s2019!

springdoc:
  api-docs:
    path: /swagger/v3/api-docs
  swagger-ui:
    path: /swagger/swagger-ui.html