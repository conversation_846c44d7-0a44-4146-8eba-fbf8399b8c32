<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">

    <changeSet id="3.5.0-pre" author="davide.leonardi">
        <createTable tableName="qm_record_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="internal_counter" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="vendor_account_number" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="valid_date" type="varchar"/>
            <column name="release_quantity_active" type="varchar"/>
            <column name="base_unit_of_measure" type="varchar"/>
            <column name="released_material_quantity" type="varchar"/>
            <column name="material_quantity_order" type="varchar"/>
            <column name="blocking_version" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="blocked_function" type="varchar"/>
            <column name="inspection_type" type="varchar"/>
            <column name="sap_language_key" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="purchasing_document_item_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="material_code" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="purchase_order_quantity" type="varchar"/>
            <column name="net_order_value" type="varchar"/>
            <column name="purchasing_document_nr" type="varchar"/>
            <column name="item_nr" type="varchar"/>
            <column name="deletion_indicator" type="varchar"/>
            <column name="rfq_status" type="varchar"/>
            <column name="short_text" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="storage_location" type="varchar"/>
            <column name="material_group" type="varchar"/>
            <column name="purchasing_record_nr" type="varchar"/>
            <column name="target_quantity" type="varchar"/>
            <column name="purchasing_unit_of_measure" type="varchar"/>
            <column name="order_price_unit" type="varchar"/>
            <column name="conversion_numerator_order_unit" type="varchar"/>
            <column name="conversion_numerator_base_unit" type="varchar"/>
            <column name="conversion_denominator_order_unit" type="varchar"/>
            <column name="conversion_denominator_base_unit" type="varchar"/>
            <column name="purchasing_group_net_price" type="varchar"/>
            <column name="price_unit" type="varchar"/>
            <column name="gross_order" type="varchar"/>
            <column name="delivery_indicator" type="varchar"/>
            <column name="item_category" type="varchar"/>
            <column name="account_assignment_category" type="varchar"/>
            <column name="invoice_receipt_indicator" type="varchar"/>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="valuation_class_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="valuation_class" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="account_category_reference" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="float_scheduling_definitions_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="sched_marg_key" type="varchar"/>
            <column name="enabled" type="boolean"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="purchasing_document_header_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="purchasing_document_nr" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="company_code" type="varchar"/>
            <column name="purchasing_document_category" type="varchar"/>
            <column name="purchasing_document_type" type="varchar"/>
            <column name="deletion_indicator" type="varchar"/>
            <column name="purchasing_document_status" type="varchar"/>
            <column name="record_creation_date" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="currency_key" type="varchar"/>
            <column name="exchange_rate" type="varchar"/>
            <column name="address_number" type="varchar"/>
            <column name="vendor_account_number" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="mmv_material_status_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="material_status" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="purchasing_message" type="varchar"/>
            <column name="bom_header_message" type="varchar"/>
            <column name="routing_message" type="varchar"/>
            <column name="requirement_message" type="varchar"/>
            <column name="mrp_message" type="varchar"/>
            <column name="production_item_message" type="varchar"/>
            <column name="production_header_message" type="varchar"/>
            <column name="plant_maintenance_message" type="varchar"/>
            <column name="inventory_message" type="varchar"/>
            <column name="forecasting_message" type="varchar"/>
            <column name="prt_order_message" type="varchar"/>
            <column name="qm_message" type="varchar"/>
            <column name="wm_change_message" type="varchar"/>
            <column name="wm_order_message" type="varchar"/>
            <column name="mc_estimate_procedure" type="varchar"/>
            <column name="long_term_planning_message" type="varchar"/>
            <column name="distribution_lock_indicator" type="varchar"/>
            <column name="ale_profile_name" type="varchar"/>
            <column name="locked_for_purchase" type="varchar"/>
            <column name="blocked_for_po" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="material_consumption_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="fiscal_year" type="varchar"/>
            <column name="follow_on_record_nr" type="varchar"/>
            <column name="period_indicator" type="varchar"/>
            <column name="total_consumption_01" type="varchar"/>
            <column name="total_consumption_02" type="varchar"/>
            <column name="total_consumption_03" type="varchar"/>
            <column name="total_consumption_04" type="varchar"/>
            <column name="total_consumption_05" type="varchar"/>
            <column name="total_consumption_06" type="varchar"/>
            <column name="total_consumption_07" type="varchar"/>
            <column name="total_consumption_08" type="varchar"/>
            <column name="total_consumption_09" type="varchar"/>
            <column name="total_consumption_10" type="varchar"/>
            <column name="total_consumption_11" type="varchar"/>
            <column name="total_consumption_12" type="varchar"/>
            <column name="total_consumption_13" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="iso_units_of_measurement_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="iso_units_of_measurement" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="iso_unit_of_measure_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="iso_unit_of_measurement" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="iso_measurement" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="dimension_text_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="language_key" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="dimension_key" type="varchar"/>
            <column name="dimension_text" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="company_text_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="company_code" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="standard_text_name" type="varchar"/>
            <column name="text_usage" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="client_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="name" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="characteristics_cabn_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="code" type="varchar"/>
            <column name="name" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="business_address_service_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="address_number" type="varchar"/>
            <column name="valid_from_date" type="varchar"/>
            <column name="address_version_id" type="varchar"/>
            <column name="valid_to_date" type="varchar"/>
            <column name="name_1" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="city_postal" type="varchar"/>
            <column name="po_box" type="varchar"/>
            <column name="street" type="varchar"/>
            <column name="house_number" type="varchar"/>
            <column name="county" type="varchar"/>
            <column name="language_key" type="varchar"/>
            <column name="region" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createTable tableName="approved_manufactured_parts_staging">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="client" type="varchar"/>
            <column name="record_number" type="varchar"/>
            <column name="material_number" type="varchar"/>
            <column name="plant" type="varchar"/>
            <column name="manufacturer_number" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
    </changeSet>

    <changeSet id="3.5.0" author="davide.leonardi">
        <addColumn tableName="units_of_measure_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="approved_manufactured_parts_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="basic_material_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="business_address_service_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="characteristics_cabn_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="characteristics_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="client_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="company_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="company_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="consumption_order_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="country_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="currency_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="description_external_material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="dimension_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="external_material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="global_valuation_category_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="hazardous_material_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="industry_sector_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="iso_unit_of_measure_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="iso_units_of_measurement_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="item_category_group_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="laboratory_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="logistics_handling_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="long_descriptions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="lot_size_definition_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_consumption_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="materials_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_group_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="materials_plant_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="plants_valuation_data_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_status_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_storage_locations_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_type_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="material_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mmv_material_status_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_controller_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_material_level_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_type_definition_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="mrp_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="plant_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="history_order_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="product_division_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_document_header_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_group_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="certificate_type_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_certificate_category_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_text_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_control_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="float_scheduling_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="short_descriptions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="unit_of_measure_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_area_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_class_definitions_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="valuation_class_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="purchasing_document_item_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
        <addColumn tableName="qm_record_staging">
            <column name="api_sent" type="boolean"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
