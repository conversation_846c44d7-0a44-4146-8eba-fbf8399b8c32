<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="2025.1.1" author="jualba.qorri">
        <addColumn tableName="materials_plant_data_staging">
            <column name="fixed_lot_size" type="numeric"/>
            <column name="ordering_costs" type="numeric"/>
            <column name="storage_costs_indicator" type="varchar"/>
            <column name="rounding_value_for_purchase_order_quantity" type="numeric"/>
            <column name="unit_of_issue" type="varchar"/>
            <column name="procurement_type" type="varchar"/>
            <column name="strategy_group" type="varchar"/>
            <column name="critical_part" type="varchar"/>
            <column name="effective_out_date" type="bigint"/>
            <column name="country_of_origin" type="varchar"/>
            <column name="loading_group" type="varchar"/>
            <column name="planning_time_fence" type="varchar"/>
            <column name="consumption_mode" type="varchar"/>
            <column name="consumption_period_backward" type="varchar"/>
            <column name="consumption_period_forward" type="varchar"/>
            <column name="maximum_lot_size" type="numeric"/>
        </addColumn>
    </changeSet>
    <changeSet id="2025.1.1_001" author="matteo.laurenzi">
        <addColumn tableName="materials_plant_data_staging">
            <column name="good_receipt_processing_time_in_days" type="numeric"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
