<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.22.xsd">
    <changeSet id="3.3.1" author="redinela.allaraj">
        <createSequence sequenceName="long_descriptions_staging_seq" incrementBy="1000"/>
        <createTable tableName="long_descriptions_staging">
            <column name="id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="material" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="language" type="varchar"/>
            <column name="client" type="varchar"/>
            <column name="created_on" type="timestamptz" defaultValueDate="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on" type="timestamptz"/>
            <column name="synchronized_on" type="timestamptz"/>
            <column name="synchronization_confirmed_on" type="timestamptz"/>
            <column name="synchronization_state" type="varchar"/>
        </createTable>
        <createIndex tableName="long_descriptions_staging" indexName="ix_long_descriptions_staging_material">
            <column name="material"/>
        </createIndex>
    </changeSet>

  <changeSet id="2023.17.7-CREAC-3726-001" author="roberto.gabrieli">
    <createSequence sequenceName="standard_api_received_payloads_id_seq" incrementBy="1000"/>
    <createTable tableName="standard_api_received_payloads">
      <column name="id" type="bigint" defaultValue="nextval('standard_api_received_payloads_id_seq')">
        <constraints nullable="false" primaryKey="true"/>
      </column>
      <column name="messageType" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="key" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="hash" type="varchar">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux01_standard_api_received_payloads"/>
      </column>
      <column name="payload" type="text"/>
      <column name="first_received_tms" type="timestamp" defaultValue="NOW()"/>
      <column name="received_counter" type="int" defaultValue="0"/>
      <column name="last_received_tms" type="timestamp" defaultValue="NOW()"/>
    </createTable>
  </changeSet>

</databaseChangeLog>
