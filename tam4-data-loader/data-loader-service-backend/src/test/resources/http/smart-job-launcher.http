# Load excel specific client
#GET http://localhost:9070/data-loader/api/job/excel/load-specific-client/100

###

GET http://localhost:9070/data-loader/api/job/excel/load-specific-client-v2/CIM

###

#
GET http://localhost:9070/data-loader/api/job/excel/send-messages?skipClassification=true

###

#
GET http://localhost:9070/data-loader/api/job/send-db-messages

###

# Run a specific job
# jobName list:
# - send-clients-descriptions
# - send-item-category-group-definition
# - send-country-definition
# - send-item-category-group-definition
# - send-industry-sector-definition
# - send-material-status-definition
# - send-unit-of-measure-definition
# - send-plant
# - send-storage-location
# - send-valuation-class-definition
# - send-global-valuation-category-definition
# - send-purchasing-group
# - send-supplier-definition
# - send-serial-number
# - send-product-division-definition
# - send-material-group
# - send-material-types
# - send-basic-material
# - send-external-material-group
# - send-hazardous-material
# - send-laboratory
# - send-logistics-handling-group-definitions
# - send-mrpcontroller
# - send-mrpmaterial-level
# - send-mrptype
# - send-qmcertificate-category
# - send-qmcontrol
# - send-lot-size-definition
# - send-scheduling-margin-key
# - send-material-created-messages
# - send-material-apply-extension-requested-messages
# - send-material-plant-valuation-requested-messages
# - update-synchronization-state
< {%
    request.variables.set("clientCode", "-------")
    request.variables.set("jobName", "-----")
%}
GET http://localhost:9070/data-loader/api/job/{{clientCode}}/{{jobName}}

###
# - - - - - - - - - - - - - - - -
GET http://localhost:9070/data-loader/api/job/100/send-clients-descriptions

###
GET http://localhost:9070/data-loader/api/job/100/send-item-category-group-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-country-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-item-category-group-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-industry-sector-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-material-status-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-unit-of-measure-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-plant

###
GET http://localhost:9070/data-loader/api/job/100/send-storage-location

###
GET http://localhost:9070/data-loader/api/job/100/send-valuation-class-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-global-valuation-category-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-purchasing-group

###
GET http://localhost:9070/data-loader/api/job/100/send-supplier-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-serial-number

###
GET http://localhost:9070/data-loader/api/job/100/send-product-division-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-material-group

###
GET http://localhost:9070/data-loader/api/job/100/send-material-types

###
GET http://localhost:9070/data-loader/api/job/100/send-basic-material

###
GET http://localhost:9070/data-loader/api/job/100/send-external-material-group

###
GET http://localhost:9070/data-loader/api/job/100/send-hazardous-material

###
GET http://localhost:9070/data-loader/api/job/100/send-laboratory

###
GET http://localhost:9070/data-loader/api/job/100/send-logistics-handling-group-definitions

###
GET http://localhost:9070/data-loader/api/job/100/send-material-controller

###
GET http://localhost:9070/data-loader/api/job/100/send-mrp-material-level

###
GET http://localhost:9070/data-loader/api/job/100/send-mrp-type

###
GET http://localhost:9070/data-loader/api/job/100/send-qm-certificate-category

###
GET http://localhost:9070/data-loader/api/job/100/send-qm-control

###
GET http://localhost:9070/data-loader/api/job/100/send-lot-size-definition

###
GET http://localhost:9070/data-loader/api/job/100/send-margin-key

###
GET http://localhost:9070/data-loader/api/job/100/send-material-created

###
GET http://localhost:9070/data-loader/api/job/100/send-plants-v2

###
GET http://localhost:9070/data-loader/api/job/100/send-material-plant-valuation-requested

###
GET http://localhost:9070/data-loader/api/job/100/update-synchronization-state

###

