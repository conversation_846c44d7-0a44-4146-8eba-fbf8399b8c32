package com.creactives.tam4.dataloader.entrypoints.job.utils;

import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class CachedTableStepListenerTest {

  @Mock
  private NamedParameterJdbcTemplate jdbcTemplate = Mockito.mock(NamedParameterJdbcTemplate.class);

  @Mock
  private StepExecution stepExecution = Mockito.mock(StepExecution.class);

  private final CacheTableConfig cacheTableConfig_test1 = CacheTableConfig.builder()
      .tableName("test")
      .query("select key, value from test1")
      .keyFields(List.of("key"))
      .build();
  private final CacheTableConfig cacheTableConfig_test2 = CacheTableConfig.builder()
      .tableName("test2")
      .query("select key, value from test2")
      .keyFields(List.of("key"))
      .build();
  private final CacheTableConfig cacheTableConfig_test3 = CacheTableConfig.builder()
      .tableName("test3")
      .query("select key, value from test3")
      .keyFields(List.of("key"))
      .build();

  @Test
  public void loadCacheTest() {
    final List<Map<String, Object>> rows_test1 = List.of(Map.of("key", "test_key", "value", "test_value"));
    final List<Map<String, Object>> rows_test2 = Collections.emptyList();
    final List<Map<String, Object>> rows_test3 = null;

    final CachedTableStepListener listener = new CachedTableStepListener(jdbcTemplate, List.of());
    Mockito.when(jdbcTemplate.queryForList(cacheTableConfig_test1.getQuery(), Collections.emptyMap())).thenReturn(rows_test1);
    Mockito.when(jdbcTemplate.queryForList(cacheTableConfig_test2.getQuery(), Collections.emptyMap())).thenReturn(rows_test2);
    Mockito.when(jdbcTemplate.queryForList(cacheTableConfig_test3.getQuery(), Collections.emptyMap())).thenReturn(rows_test3);

    final Map<String, Map<String, Object>> cache1 = listener.loadCache(cacheTableConfig_test1, stepExecution);
    final Map<String, Map<String, Object>> cache2 = listener.loadCache(cacheTableConfig_test2, stepExecution);
    final Map<String, Map<String, Object>> cache3 = listener.loadCache(cacheTableConfig_test3, stepExecution);

    Mockito.when(stepExecution.getExecutionContext()).thenReturn(Mockito.mock(ExecutionContext.class));
    Mockito.when(stepExecution.getExecutionContext().get(cacheTableConfig_test1.getTableName())).thenReturn(cache1);
    Mockito.when(stepExecution.getExecutionContext().get(cacheTableConfig_test2.getTableName())).thenReturn(cache2);
    Mockito.when(stepExecution.getExecutionContext().get(cacheTableConfig_test3.getTableName())).thenReturn(cache3);


    final Map<String, String> cache1_remapped = CachedTableStepListener.extractValueCache(stepExecution, cacheTableConfig_test1.getTableName(), List.of("key"), "value");
    final Map<String, String> cache2_remapped = CachedTableStepListener.extractValueCache(stepExecution, cacheTableConfig_test2.getTableName(), List.of("key"), "value");
    final Map<String, String> cache3_remapped = CachedTableStepListener.extractValueCache(stepExecution, cacheTableConfig_test3.getTableName(), List.of("key"), "value");

    assertTrue(cache1_remapped.size() == 1);
    assertTrue("test_value".equals(CachedTableStepListener.getValueFromCache(cache1_remapped, "test_key")));
    assertTrue(cache2_remapped.isEmpty());
    assertTrue(cache3_remapped.isEmpty());

  }
}
