package com.creactives.tam4.dataloader.core.usecase.loadclient;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {GetTotalClientsInMaterialsUseCase.class})
@ExtendWith(SpringExtension.class)
class GetTotalClientsInMaterialsUseCaseTest {

  @MockBean
  private GetTotalClientsInMaterialsInDatabase getTotalClientsInMaterialsInDatabase;

  @Autowired
  private GetTotalClientsInMaterialsUseCase getTotalClientsInMaterialsUseCase;

  @Test
  void testTotalClients_Empty() {
    when(getTotalClientsInMaterialsInDatabase.totalClients()).thenReturn(new ArrayList<>());

    final List<String> actualTotalClientsResult = getTotalClientsInMaterialsUseCase.totalClients();

    verify(getTotalClientsInMaterialsInDatabase).totalClients();
    assertTrue(actualTotalClientsResult.isEmpty());
  }

  @Test
  void testTotalClients_twoElements() {
    final ArrayList<String> stringList = new ArrayList<>();
    stringList.add("EU");
    stringList.add("AU");
    when(getTotalClientsInMaterialsInDatabase.totalClients()).thenReturn(stringList);

    final List<String> actualTotalClientsResult = getTotalClientsInMaterialsUseCase.totalClients();

    verify(getTotalClientsInMaterialsInDatabase).totalClients();
    assertEquals(2, actualTotalClientsResult.size());
    assertEquals("EU", actualTotalClientsResult.get(0));
    assertEquals("AU", actualTotalClientsResult.get(1));
    assertSame(stringList, actualTotalClientsResult);
  }
}
