package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation;

import com.creactives.tam4.common.utils.currency.CurrencyUtils;
import com.creactives.tam4.dataloader.configuration.CurrencyConverterConfiguration;
import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.dataproviders.database.plant.valuation.consumption.ConsumptionDataEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialplantvaluation.v1.MaterialPlantValuationItemProcessor;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class MaterialPlantItemProcessorTest {

  @Mock
  private MaterialCodeNormalizer materialCodeNormalizer;
  @Autowired
  private CurrencyUtils currencyUtils;

  private MaterialPlantValuationItemProcessor underTest;

  @BeforeEach
  void setUp() {
    final CurrencyConverterConfiguration currencyConverterConfiguration = new CurrencyConverterConfiguration();
    final CurrencyConverter currencyConverter = new CurrencyConverter(currencyConverterConfiguration, currencyUtils);
    final Map<String, Double> ratesMap = new HashMap<>();
    ratesMap.put("AUD", 0.669404);
    currencyConverter.setRates(ratesMap);
    underTest = new MaterialPlantValuationItemProcessor(currencyConverter, materialCodeNormalizer);
  }

  @Test
  void calculateTotalConsumptionAmountEUR_whenConsumptionDataCurrencyNotSpecified_returnsDefaultCurrencyValueConvertingThePlantOne() {
    final List<ConsumptionDataEntity> consumptionDataEntities = new ArrayList<>();
    consumptionDataEntities.add(ConsumptionDataEntity.builder().consumptionAmount(BigDecimal.valueOf(10)).build());
    consumptionDataEntities.add(ConsumptionDataEntity.builder().consumptionAmount(BigDecimal.valueOf(20)).build());
    final String plantCurrency = "AUD";
    final BigDecimal totalConsumptionAmountEUR = underTest.calculateTotalConsumptionAmountEUR(consumptionDataEntities, plantCurrency);
    Assertions.assertThat(totalConsumptionAmountEUR).isNotNull();
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(20.08212);
  }

  @Test
  void calculateTotalConsumptionAmountEUR_whenConsumptionCurrencySpecifiedAndEqualToDefault_returnsDefaultCurrencyValueWithoutConverting() {
    final List<ConsumptionDataEntity> consumptionDataEntities = new ArrayList<>();
    consumptionDataEntities.add(ConsumptionDataEntity.builder().consumptionAmount(BigDecimal.valueOf(10)).currency("EUR").build());
    consumptionDataEntities.add(ConsumptionDataEntity.builder().consumptionAmount(BigDecimal.valueOf(20)).currency("EUR").build());
    final String plantCurrency = "AUD";
    final BigDecimal totalConsumptionAmountEUR = underTest.calculateTotalConsumptionAmountEUR(consumptionDataEntities, plantCurrency);
    Assertions.assertThat(totalConsumptionAmountEUR).isNotNull();
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(30);
  }


}
