package com.creactives.tam4.dataloader.core.entity;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DateConverterTest {

  @Test
  @DisplayName("Verify that passing a null value, a null value is returned")
  public void convertToMillis_withNullValue_aNullValueIsReturned() {
    Long millis = DateConverter.convertToMillis(null);
    assertNull(millis,"Should return null value");
  }

  @Test
  @DisplayName("Verify that passing a empty value, a null value is returned")
  public void convertToMillis_withEmptyValue_aNullValueIsReturned() {
    Long millis = DateConverter.convertToMillis(StringUtils.EMPTY);
    assertNull(millis,"Should return null value");
  }

  @Test
  @DisplayName("Verify that passing a blank space value, a null value is returned")
  public void convertToMillis_withBlankSpaceValue_aNullValueIsReturned() {
    Long millis = DateConverter.convertToMillis("    ");
    assertNull(millis,"Should return null value");
  }

  @Test
  @DisplayName("Verify that passing a date with wrong format, a UnsupportedOperationException is raised")
  public void convertToMillis_withWrongFormatDate_aNullValueIsReturned() {
    final UnsupportedOperationException exception = assertThrows(UnsupportedOperationException.class, () -> DateConverter.convertToMillis("2025-07-30"));
    assertEquals("Impossible to parse date 2025-07-30 -> Text '2025-07-30' could not be parsed at index 2", exception.getMessage());
  }

  @Test
  @DisplayName("Verify that passing a valid date value, a milliseconds value is returned")
  public void convertToMillis_withValidDate_aMillisecondsValueIsReturned() {
    Long millis = DateConverter.convertToMillis("30/07/2025");
    assertEquals(1753833600000L, millis,"Should return null value");

  }

}