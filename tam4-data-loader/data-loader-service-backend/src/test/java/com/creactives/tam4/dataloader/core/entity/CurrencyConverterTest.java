package com.creactives.tam4.dataloader.core.entity;

import com.creactives.tam4.common.utils.currency.CurrencyUtils;
import com.creactives.tam4.dataloader.configuration.CurrencyConverterConfiguration;
import com.creactives.tam4.dataloader.core.exceptions.MissingExchangeRateException;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class CurrencyConverterTest {

  private CurrencyConverter underTest;
//  @Mock
//  private CurrencyConverterConfiguration currencyConverterConfiguration;

  @Autowired
  private CurrencyUtils currencyUtils;

  @BeforeEach
  void setUp() {
    final CurrencyConverterConfiguration currencyConverterConfiguration = new CurrencyConverterConfiguration();
    underTest = new CurrencyConverter(currencyConverterConfiguration, currencyUtils);
    final Map<String, Double> ratesMap = new HashMap<>();
    ratesMap.put("AUD", 0.61);
    ratesMap.put("USD", 0.93);
    ratesMap.put("NOK", 0.0849912);
    underTest.setRates(ratesMap);
  }

  @Test
  void convertToCurrency_whenTargetIsEqualToDefault_appliesOnlyAConversion() {
    final BigDecimal oneNOK = BigDecimal.valueOf(1);
    final BigDecimal totalConsumptionAmountEUR = underTest.convertToCurrency("NOK", oneNOK, "EUR");
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(0.0849912);
  }

  @Test
  void convertToCurrency_whenOriginalIsEqualToDefault_appliesOnlyAConversion() {
    final BigDecimal oneEur = BigDecimal.valueOf(1);
    final BigDecimal totalConsumptionAmountEUR = underTest.convertToCurrency("EUR", oneEur, "USD");
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(1.075268817204301);
  }

  @Test
  void convertToCurrency_whenMissingOriginalDefaultRate_throwMissingExchangeRateException() {
    final BigDecimal oneEur = BigDecimal.valueOf(1);
    org.junit.jupiter.api.Assertions.assertThrows(MissingExchangeRateException.class,
        () -> underTest.convertToCurrency("BRL", oneEur, "USD"));
  }

  @Test
  void convertToCurrency_whenMissingOriginalCurrency_throwIllegalStateException() {
    final BigDecimal oneEur = BigDecimal.valueOf(1);
    org.junit.jupiter.api.Assertions.assertThrows(IllegalStateException.class,
        () -> underTest.convertToCurrency("", oneEur, "USD"));
  }

  @Test
  void convertToCurrency_whenMissingTargetCurrency_throwIllegalStateException() {
    final BigDecimal oneEur = BigDecimal.valueOf(1);
    org.junit.jupiter.api.Assertions.assertThrows(IllegalStateException.class,
        () -> underTest.convertToCurrency("BRL", oneEur, ""));
  }

  @Test
  void convertToCurrency_whenMissingDefaultToTargetRate_throwMissingExchangeRateException() {
    final BigDecimal oneEur = BigDecimal.valueOf(1);
    org.junit.jupiter.api.Assertions.assertThrows(MissingExchangeRateException.class,
        () -> underTest.convertToCurrency("USD", oneEur, "BRL"));
  }

  @Test
  void convertToCurrency_whenTargetIsNotEqualToDefaultOrOriginal_appliesTwoConversions() {
    final BigDecimal oneDollar = BigDecimal.valueOf(1);
    final BigDecimal totalConsumptionAmountEUR = underTest.convertToCurrency("USD", oneDollar,
        "AUD");
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(1.5245901639344264);
  }


  @Test
  void convertToCurrency_whenTargetIsEqualToOriginal_appliesZeroConversions() {
    final BigDecimal oneDollar = BigDecimal.valueOf(1);
    final BigDecimal totalConsumptionAmountEUR = underTest.convertToCurrency("USD", oneDollar,
        "USD");
    Assertions.assertThat(totalConsumptionAmountEUR.doubleValue()).isEqualTo(1);
  }

}
