package com.creactives.tam4.dataloader.dataproviders.database.material;

import com.creactives.tam4.dataloader.configuration.CurrencyConverterConfiguration;
import com.creactives.tam4.dataloader.core.entity.CurrencyConverter;
import com.creactives.tam4.dataloader.dataproviders.database.descriptions.ShortDescriptionStagingRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.web.client.AutoConfigureWebClient;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.sql.Timestamp;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@EnableJpaRepositories(basePackages = "com.creactives.tam4.dataloader.dataproviders.database")
@DataJpaTest
@AutoConfigureWebClient
@ContextConfiguration(classes = {IncorrectMaterialsService.class, CurrencyConverter.class, CurrencyConverterConfiguration.class})
@EntityScan(basePackages = {"com.creactives.tam4.dataloader.dataproviders.database"})
@ComponentScan(basePackages = {"com.creactives.tam4.dataloader.dataproviders.database", "com.creactives.tam4.common.utils.currency"}, excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
    value = {MaterialRepositoryItemReader.class}))
@TestPropertySource(properties = {"spring.jpa.hibernate.ddl-auto=none", "spring.liquibase.change-log=classpath:db/changelog/db.changelog-master-h2.xml"})
public class IncorrectMaterialsServiceTest {

  @Autowired
  private MaterialStagingRepository materialStagingRepository;

  @Autowired
  private IncorrectMaterialsService incorrectMaterialsService;

  @Autowired
  private ShortDescriptionStagingRepository shortDescriptionStagingRepository;

  @Autowired
  private JdbcTemplate jdbcTemplate;

  @Test
  void totalMaterials() {
    createMaterial("matCode", "client1", true, true);
    createMaterial("matCode1", "client1", true, true);
    createMaterial("matCode1", "wrongClient", true, true);
    final long totalMaterials = incorrectMaterialsService.totalMaterials("client1");
    assertThat(totalMaterials).isEqualTo(2);
  }

  @Test
  void totalIgnoredMaterials() {
    createMaterial("matCode", "client1", true, true);
    createMaterial("matCode1", "client1", true, true);
    createMaterial("matCode2", "client1", false, true);
    createMaterial("matCode3", "wrongClient", true, true);
    final long totalIgnoredMaterials = incorrectMaterialsService.totalIgnoredMaterials("client1", true);
    assertThat(totalIgnoredMaterials).isEqualTo(2);
  }

  @Test
  void totalAnalysedMaterials() {
    createMaterial("matCode", "client1", true, true);
    createMaterial("matCode1", "client1", true, true);
    createMaterial("matCode2", "client1", true, false);
    createMaterial("matCode1", "wrongClient", true, true);
    final long totalAnalysedMaterials = incorrectMaterialsService.totalAnalysedMaterials("client1", true);
    assertThat(totalAnalysedMaterials).isEqualTo(2);
  }

  @Test
  void detectMaterialsWithNoPlants_insertTwoMaterials_onlyOneHasPlants() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_plant') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'client1','mat_no_plant') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'wrongClient','mat_with_plant') ");
    jdbcTemplate.update("insert into materials_plant_data_staging (id, client, material_code, plant_id) values (10, 'client1','mat_with_plant', '100') ");
    jdbcTemplate.update("insert into materials_plant_data_staging (id, client, material_code, plant_id) values (11, 'wrongClient','mat_with_plant', '100') ");

    final long totalMaterialsWithNoPlants = incorrectMaterialsService.detectMaterialsWithNoPlants("client1");
    assertThat(totalMaterialsWithNoPlants).isEqualTo(1L);
  }

  @Test
  void detectMaterialsWithNoOrdered_insertTwoMaterials_onlyOneHasOrders() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_orders') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'client1','mat_no_orders') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'wrongClient','mat_with_orders') ");
    jdbcTemplate.update("insert into history_order_staging (id, client, material_code, ordered_quantity) values (10, 'client1','mat_with_orders', 'orders') ");
    jdbcTemplate.update("insert into history_order_staging (id, client, material_code, ordered_quantity) values (11, 'wrongClient','mat_with_orders', 'orders') ");

    final long totalMaterialsWithNoOrdered = incorrectMaterialsService.detectMaterialsWithNoOrdered("client1");
    assertThat(totalMaterialsWithNoOrdered).isEqualTo(1L);
  }

  @Test
  void detectMaterialsWithNoConsumptions_insertTwoMaterials_onlyOneHasConsumptions() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_consumptions') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'client1','mat_no_consumptions') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'wrongClient','mat_with_consumptions') ");
    jdbcTemplate.update("insert into consumption_order_staging (id, client, material_code, consumption_quantity) values (10, 'client1', 'mat_with_consumptions', 'consumptions') ");
    jdbcTemplate.update("insert into consumption_order_staging (id, client, material_code, consumption_quantity) values (11, 'wrongClient', 'mat_with_consumptions', 'consumptions') ");

    final long totalMaterialsWithNoConsumptions = incorrectMaterialsService.detectMaterialsWithNoConsumptions("client1");
    assertThat(totalMaterialsWithNoConsumptions).isEqualTo(1L);
  }

  @Test
  void detectMaterialsWithNoValuations_insertTwoMaterials_onlyOneHasValuation() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_valuation') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'client1','mat_no_valuation') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'wrongClient','mat_with_valuation') ");
    jdbcTemplate.update("insert into plants_valuation_data_staging (id, client, material_code, valuation_type) values (10, 'client','mat_with_valuation', 'valuation') ");
    jdbcTemplate.update("insert into plants_valuation_data_staging (id, client, material_code, valuation_type) values (11, 'wrongClient','mat_with_valuation', 'valuation') ");

    final long totalMaterialsWithNoValuations = incorrectMaterialsService.detectMaterialsWithNoValuations("client1");
    assertThat(totalMaterialsWithNoValuations).isEqualTo(1L);
  }

  @Test
  void detectMaterialsWithNoShortText_insertTwoMaterials_onlyOneHasShortDescription() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_short_desc') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'client1','mat_no_short_desc') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'wrongClient','mat_with_short_desc') ");
    jdbcTemplate.update("insert into short_descriptions_staging (id, client, material, description) values (10, 'client1', 'mat_with_short_desc', 'desc') ");
    jdbcTemplate.update("insert into short_descriptions_staging (id, client, material, description) values (11, 'wrongClient', 'mat_with_short_desc', 'desc') ");

    final long totalMaterialsWithNoShortDescription = incorrectMaterialsService.detectMaterialsWithNoShortText("client1");
    assertThat(totalMaterialsWithNoShortDescription).isEqualTo(1L);
  }

  @Test
  void detectMaterialsWithNoPODescriptions_insertTwoMaterials_onlyOneHasPODescription() {
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (1, 'client1','mat_with_po_desc') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (2, 'client1','mat_no_po_desc') ");
    jdbcTemplate.update("insert into materials_data_staging (id, client, material_code) values (3, 'wrongClient','mat_with_po_desc') ");
    jdbcTemplate.update("insert into long_descriptions_staging (id, client, material, description) values (10, 'client1', 'mat_with_po_desc', 'po_desc') ");
    jdbcTemplate.update("insert into long_descriptions_staging (id, client, material, description) values (11, 'wrongClient', 'mat_with_po_desc', 'po_desc') ");

    final long totalMaterialsWithNoPODescriptions = incorrectMaterialsService.detectMaterialsWithNoPODescriptions("client1");
    assertThat(totalMaterialsWithNoPODescriptions).isEqualTo(1L);
  }

  private void createMaterial(final String materialCode, final String client, final boolean isIgnored, final boolean isAnalysed) {
    final MaterialEntity materialEntity = new MaterialEntity();
    materialEntity.setMaterialCode(materialCode);
    materialEntity.setClient(client);
    materialEntity.setCreatedOn(new Timestamp(System.currentTimeMillis()));
    materialEntity.setIgnore(isIgnored);
    materialEntity.setSemanticallyAnalyzed(isAnalysed);
    materialStagingRepository.save(materialEntity);
  }

  @Configuration
  public static class CachingTestConfig {

    @Bean
    public CacheManager cacheManager() {
      return new ConcurrentMapCacheManager();
    }
  }

}
