package com.creactives.tam4.dataloader.dataproviders.database;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialStagingRepository;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class RepositoryHolder {

  @Autowired
  private final MaterialStagingRepository materialStagingRepository;

  @Autowired
  public RepositoryHolder(final MaterialStagingRepository materialStagingRepository) {
    this.materialStagingRepository = materialStagingRepository;
  }
}
