package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.core.entity.MaterialCodeNormalizer;
import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationEntity;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import com.creactives.tam4.messaging.materials.load.MaterialApplyExtensionLoadMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(MockitoExtension.class)
class MaterialPlantItemProcessorV3Test {
  @Mock
  private MaterialCodeNormalizer materialCodeNormalizer;

  private MaterialPlantItemProcessorV3 underTest;

  @BeforeEach
  void setUp() {
    underTest = new MaterialPlantItemProcessorV3(materialCodeNormalizer);
  }

  @Test
  void process() {
    final MaterialEntity materialEntity = new MaterialEntity();
    materialEntity.setClient("001");
    materialEntity.setMaterialCode("6001");

    final MaterialPlantEntity plantABE = new MaterialPlantEntity();
    plantABE.setId(1);
    plantABE.setClient("001");
    plantABE.setMaterialCode("6001");
    plantABE.setPlantId("ABE1");
    plantABE.setDeletionFlag(false);

    final MaterialStorageLocationEntity storage = new MaterialStorageLocationEntity();
    storage.setId(1);
    storage.setMaterialCode("6001");
    storage.setPlantCode("ABE1");
    storage.setStorageLocation("W1");
    storage.setFlagMaterialForDeletionAtStorageLocationLevel("false");

    final MaterialStorageLocationEntity storage2 = new MaterialStorageLocationEntity();
    storage2.setId(2);
    storage2.setMaterialCode("6001");
    storage2.setPlantCode("ABE1");
    storage2.setStorageLocation("W2");
    storage2.setFlagMaterialForDeletionAtStorageLocationLevel("true");

    final MaterialStorageLocationEntity storage3 = new MaterialStorageLocationEntity();
    storage3.setId(3);
    storage3.setMaterialCode("6001");
    storage3.setPlantCode("ABE1");
    storage3.setStorageLocation("W3");
    storage3.setFlagMaterialForDeletionAtStorageLocationLevel("");

    final MaterialStorageLocationEntity storage4 = new MaterialStorageLocationEntity();
    storage4.setId(4);
    storage4.setMaterialCode("6001");
    storage4.setPlantCode("ABE1");
    storage4.setStorageLocation("W4");
    storage4.setFlagMaterialForDeletionAtStorageLocationLevel(null);

    final PlantEntity plantDef = new PlantEntity();
    plantDef.setId(1);
    plantDef.setClient("001");
    plantDef.setPlant("ABE1");
    plantDef.setDescription("ABE1");

    final Map<String, PlantEntity> plantMap = new HashMap<>();
    plantMap.put("ABE1", plantDef);

    final AggregateDataForMaterialExtension aggregateData = AggregateDataForMaterialExtension.builder()
        .materialEntity(materialEntity)
        .materialPlantEntities(Collections.singletonList(plantABE))
        .materialStorageLocationEntities(List.of(storage, storage2, storage3, storage4))
        .plants(plantMap)
        .build();

    final MaterialApplyExtensionLoadMessage message = underTest.process(aggregateData);

    assertFalse(message.getMaterialPlantDetails().get(0).getStorageLocations().get(0).isDeletionFlag());
    assertFalse(message.getMaterialPlantDetails().get(0).getStorageLocations().get(1).isDeletionFlag());
    assertFalse(message.getMaterialPlantDetails().get(0).getStorageLocations().get(2).isDeletionFlag());
    assertFalse(message.getMaterialPlantDetails().get(0).getStorageLocations().get(3).isDeletionFlag());

  }

}
