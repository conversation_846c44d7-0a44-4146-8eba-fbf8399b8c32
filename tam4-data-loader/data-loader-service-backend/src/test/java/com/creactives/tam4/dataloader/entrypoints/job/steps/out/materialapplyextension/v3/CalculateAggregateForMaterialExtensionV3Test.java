package com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v3;

import com.creactives.tam4.dataloader.dataproviders.database.material.MaterialEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.MaterialPlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantStagingRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.storage.MaterialStorageLocationsStagingRepository;
import com.creactives.tam4.dataloader.entrypoints.job.steps.out.materialapplyextension.v1.AggregateDataForMaterialExtension;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
public class CalculateAggregateForMaterialExtensionV3Test {

  public static final String MATERIAL_CODE = "123-Stella";
  public static final String CLIENT = "Client-1";
  public static final String PLANT_1 = "Plant-1";
  public static final String PLANT_2 = "Plant-2";
  private CalculateAggregateForMaterialExtensionV3 underTest;

  @Mock
  private MaterialPlantStagingRepository materialPlantStagingRepository;

  @Mock
  private MaterialStorageLocationsStagingRepository materialStorageLocationsStagingRepository;

  @Mock
  private PlantStagingRepository plantStagingRepository;

  @BeforeEach
  public void init() {
    underTest = new CalculateAggregateForMaterialExtensionV3(materialPlantStagingRepository, materialStorageLocationsStagingRepository, plantStagingRepository);
  }

  @Test
  public void process_DuplicatedMaterialPlantEntity_returnLatestMaterialPlantEntity() {

    final MaterialEntity fakeMaterialEntity = MaterialEntity.builder()
        .client(CLIENT)
        .materialCode(MATERIAL_CODE)
        .build();

    final List<MaterialPlantEntity> materialPlantEntities = new ArrayList<>(3);

    final Instant now = Instant.now();
    final Instant now_plus_1_day = now.plus(1, ChronoUnit.DAYS);

    final Timestamp timestampNow = Timestamp.from(now);
    final Timestamp timestampNowPlusOne = Timestamp.from(now_plus_1_day);
    final MaterialPlantEntity materialPlantEntity_plant_1_old = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_1).client(CLIENT).createdOn(timestampNow).build();
    final MaterialPlantEntity materialPlantEntity_plant_1_new = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_1).client(CLIENT).createdOn(timestampNowPlusOne).build();
    final MaterialPlantEntity materialPlantEntity_plant_2 = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_2).client(CLIENT).createdOn(timestampNow).build();

    materialPlantEntities.add(materialPlantEntity_plant_1_old);
    materialPlantEntities.add(materialPlantEntity_plant_1_new);
    materialPlantEntities.add(materialPlantEntity_plant_2);

    when(materialPlantStagingRepository.findByClientAndMaterialCode(CLIENT, MATERIAL_CODE)).thenReturn(materialPlantEntities);
    when(materialStorageLocationsStagingRepository.findByClientAndMaterialCode(CLIENT, MATERIAL_CODE)).thenReturn(List.of());

    final PlantEntity plantEntity_1 = PlantEntity.builder()
        .client(CLIENT)
        .plant(PLANT_1).build();
    when(plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(CLIENT, PLANT_1)).thenReturn(plantEntity_1);

    final PlantEntity plantEntity_2 = PlantEntity.builder()
        .client(CLIENT)
        .plant(PLANT_1).build();
    when(plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(CLIENT, PLANT_2)).thenReturn(plantEntity_2);


    final AggregateDataForMaterialExtension result = underTest.process(fakeMaterialEntity);

    assertNotNull(result);
    assertEquals(2, result.getMaterialPlantEntities().size(), "Wrong number of MaterialPLantEntity");
    assertEquals(timestampNowPlusOne, result.getMaterialPlantEntities().stream().filter(materialPlantEntity -> materialPlantEntity.getPlantId().equals(PLANT_1)).findFirst().get().getCreatedOn(), "Wrong creation date of MaterialPlantEntity");


  }

  @Test
  public void process_SingleMaterialPlantEntity_returnMaterialPlantEntity() {

    final MaterialEntity fakeMaterialEntity = MaterialEntity.builder()
        .client(CLIENT)
        .materialCode(MATERIAL_CODE)
        .build();

    final List<MaterialPlantEntity> materialPlantEntities = new ArrayList<>(3);

    final Instant now = Instant.now();
    final Instant now_plus_1_day = now.plus(1, ChronoUnit.DAYS);

    final Timestamp timestampNow = Timestamp.from(now);
    final MaterialPlantEntity materialPlantEntity_plant_1_old = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_1).client(CLIENT).createdOn(timestampNow).build();

    materialPlantEntities.add(materialPlantEntity_plant_1_old);

    when(materialPlantStagingRepository.findByClientAndMaterialCode(CLIENT, MATERIAL_CODE)).thenReturn(materialPlantEntities);
    when(materialStorageLocationsStagingRepository.findByClientAndMaterialCode(CLIENT, MATERIAL_CODE)).thenReturn(List.of());

    final PlantEntity plantEntity_1 = PlantEntity.builder()
        .client(CLIENT)
        .plant(PLANT_1).build();
    when(plantStagingRepository.findFirstByClientAndPlantOrderByErpSequenceNumDesc(CLIENT, PLANT_1)).thenReturn(plantEntity_1);


    final AggregateDataForMaterialExtension result = underTest.process(fakeMaterialEntity);

    assertNotNull(result);
    assertEquals(1, result.getMaterialPlantEntities().size(), "Wrong number of MaterialPLantEntity");
    assertEquals(timestampNow, result.getMaterialPlantEntities().stream().filter(materialPlantEntity -> materialPlantEntity.getPlantId().equals(PLANT_1)).findFirst().get().getCreatedOn(), "Wrong creation date of MaterialPlantEntity");


  }

  private List<MaterialPlantEntity> getFakeMaterialPlantEntityList() {
    final List<MaterialPlantEntity> materialPlantEntities = new ArrayList<>(3);

    final Instant now = Instant.now();

    final MaterialPlantEntity materialPlantEntity_plant_1_old = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_1).client(CLIENT).createdOn(Timestamp.from(now)).build();
    final MaterialPlantEntity materialPlantEntity_plant_1_new = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_1).client(CLIENT).createdOn(Timestamp.from(now.plus(1, ChronoUnit.DAYS))).build();
    final MaterialPlantEntity materialPlantEntity_plant_2 = MaterialPlantEntity.builder().materialCode(MATERIAL_CODE).plantId(PLANT_2).client(CLIENT).createdOn(Timestamp.from(now)).build();

    materialPlantEntities.add(materialPlantEntity_plant_1_old);
    materialPlantEntities.add(materialPlantEntity_plant_1_new);
    materialPlantEntities.add(materialPlantEntity_plant_2);

    return materialPlantEntities;
  }

}
