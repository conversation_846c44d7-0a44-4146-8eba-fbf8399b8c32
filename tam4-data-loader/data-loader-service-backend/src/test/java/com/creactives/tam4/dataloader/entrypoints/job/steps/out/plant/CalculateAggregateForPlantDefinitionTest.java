package com.creactives.tam4.dataloader.entrypoints.job.steps.out.plant;

import com.creactives.tam4.dataloader.core.entity.SAPLanguageConverter;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantcurrency.PlantCurrencyRepository;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantdefiniton.PlantEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingEntity;
import com.creactives.tam4.dataloader.dataproviders.database.plant.plantlanguages.PlantsAdditionalLanguagesStagingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
public class CalculateAggregateForPlantDefinitionTest {

  public static final String MATERIAL_CODE = "123-Stella";
  public static final String CLIENT = "Client-1";
  public static final String PLANT_1 = "Plant-1";
  public static final String PLANT_2 = "Plant-2";
  private final SAPLanguageConverter sapLanguageConverter = new SAPLanguageConverter();
  private CalculateAggregateForPlantDefinition underTest;
  @Mock
  private PlantCurrencyRepository plantCurrencyRepository;
  @Mock
  private PlantsAdditionalLanguagesStagingRepository plantsAdditionalLanguagesStagingRepository;

  private static PlantEntity getPlantEntity(final String language) {
    return PlantEntity.builder().plant(PLANT_1).client(CLIENT).language(language).build();
  }

  @BeforeEach
  public void init() {
    underTest = new CalculateAggregateForPlantDefinition(plantCurrencyRepository, plantsAdditionalLanguagesStagingRepository);
    when(plantCurrencyRepository.findByClientAndPlant(anyString(), anyString())).thenReturn(PlantCurrencyEntity.builder().build());
  }

  @Test
  public void whenNoAdditionalLanguageEntityPresent_thenReturnAggregateWithAdditionalEmptyList() throws Exception {
    when(plantsAdditionalLanguagesStagingRepository.findByClientAndPlant(CLIENT, PLANT_1)).thenReturn(Collections.emptySet());
    final PlantEntity plantEntity = getPlantEntity("en");
    final AggregateDataForPlantDefinition aggregateDataForPlantDefinition = underTest.process(plantEntity);

    assertNotNull(aggregateDataForPlantDefinition);
    final Set<String> additionalLanguages = aggregateDataForPlantDefinition.getAdditionalLanguages();
    assertTrue(additionalLanguages.isEmpty());


  }

  @Test
  public void whenMultipleAdditionalLanguageArePresent_thenReturnListWithAllTheLanguages() throws Exception {
    final PlantEntity plantEntity = getPlantEntity("af");
    final Set<PlantsAdditionalLanguagesStagingEntity> additionalLanguagesEntities = Set.of("EN", "es", "ru").stream().map(it -> PlantsAdditionalLanguagesStagingEntity.builder().plant(plantEntity.getPlant()).client(plantEntity.getClient()).languageCode(sapLanguageConverter.convertToLanguage(it)).build()).collect(Collectors.toSet());
    when(plantsAdditionalLanguagesStagingRepository.findByClientAndPlant(CLIENT, PLANT_1))
        .thenReturn(additionalLanguagesEntities);
    final AggregateDataForPlantDefinition aggregateDataForPlantDefinition = underTest.process(plantEntity);
    final Set<String> additionalLanguages = aggregateDataForPlantDefinition.getAdditionalLanguages();
    assertFalse(additionalLanguages.isEmpty());
    assertEquals(additionalLanguages.size(), 3);
  }

  @Test
  public void whenAdditionalLanguageContainsThePlantLanguage_thenReturnListWithAllTheAdditionalLanguagesWithoutThePlantLanguage() throws Exception {
    final PlantEntity plantEntity = getPlantEntity("en");
    final Set<PlantsAdditionalLanguagesStagingEntity> additionalLanguagesEntities = Set.of("EN", "es", "ru").stream().map(it -> PlantsAdditionalLanguagesStagingEntity.builder().plant(plantEntity.getPlant()).client(plantEntity.getClient()).languageCode(sapLanguageConverter.convertToLanguage(it)).build()).collect(Collectors.toSet());
    when(plantsAdditionalLanguagesStagingRepository.findByClientAndPlant(CLIENT, PLANT_1))
        .thenReturn(additionalLanguagesEntities);
    final AggregateDataForPlantDefinition aggregateDataForPlantDefinition = underTest.process(plantEntity);
    final Set<String> additionalLanguages = aggregateDataForPlantDefinition.getAdditionalLanguages();
    assertFalse(additionalLanguages.isEmpty());
    assertEquals(additionalLanguages.size(), 2);
  }
}
