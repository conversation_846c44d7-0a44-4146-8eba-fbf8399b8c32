package com.creactives.tam4.massiveoperation.massivecreation.strategies;

import com.creactives.tam4.bulkupload.core.entity.TemplateType;
import com.creactives.tam4.common.constants.ExcelCustomPropertyMetadata;
import com.creactives.tam4.common.constants.attributes.AttributeCodes;
import com.creactives.tam4.common.dataproviders.rest.response.LocalizedFieldValue;
import com.creactives.tam4.common.messaging.massiveoperation.ColumnWrapper;
import com.creactives.tam4.common.messaging.massiveoperation.RowWrapper;
import com.creactives.tam4.common.types.MasterdataExtractionType;
import com.creactives.tam4.massiveoperation.common.core.entity.MassiveOperationTemplateType;
import com.creactives.tam4.massiveoperation.common.strategies.MassiveTemplateStrategy;
import com.creactives.tam4.massiveoperation.massivecreation.core.usecases.BulkEditExcelTemplateGenerator;
import com.creactives.tam4.massiveoperation.massivecreation.core.usecases.MassiveCreationAttributesUseCase;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ooxml.POIXMLProperties;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

@RequiredArgsConstructor
public class MassiveCreationTemplateStrategy implements MassiveTemplateStrategy {

  private final String FILE_NAME = "Massive Creation";
  private final String REQUEST_TYPE = MassiveOperationTemplateType.CREATION.toString();
  private final String EXCEL_EXTENSION = ".xlsx";
  private final MassiveCreationAttributesUseCase massiveCreationAttributesUseCase;

  @Override
  public void generateTemplateContent(final SXSSFWorkbook workbook, final Integer userId,
      final String language,
      final String requestId, final Map<String, Object> metadata) {

    List<RowWrapper> rowWrappers = massiveCreationAttributesUseCase.getRowsAttributeWithDropdownValues(
        userId,
        language,
        metadata.get("client").toString(),
        metadata.get("mdDomain").toString(),
        Boolean.valueOf(metadata.get("goldenRecord").toString()),
        metadata.get("page").toString()).stream().filter(el -> !el.isReadOnly()).collect(Collectors.toList());

    addFieldsForMetadata(rowWrappers, metadata);

    BulkEditExcelTemplateGenerator bulkEditExcelTemplateGenerator = new BulkEditExcelTemplateGenerator(
        workbook, FILE_NAME,
        rowWrappers);
    bulkEditExcelTemplateGenerator.generateTemplate();
    addCustomProperties(workbook, language, metadata);
  }

  private void addFieldsForMetadata(List<RowWrapper> rowWrappers, Map<String, Object> metadata) {
    List<LocalizedFieldValue> clientsSelectable = new ArrayList<>();
    List<LocalizedFieldValue> dropdownValues = List.of(new LocalizedFieldValue("Y", "Y"));

    RowWrapper clientSelected = rowWrappers.stream()
        .filter(el -> el.getColumnWrapper().getAttributesColumn()
            .equalsIgnoreCase(AttributeCodes.CLIENT)).findFirst().orElse(null);

    // TODO: Possibile nuove soluzione per recuperare i client selezionabili dall'utente
    if (clientSelected != null) {
      clientsSelectable = clientSelected.getColumnWrapper()
          .getDropdownValues();
      rowWrappers.remove(clientSelected);
    }

    if (clientSelected != null) {
      clientSelected.setFieldValue(dropdownValues.get(0).getText());
      clientSelected.getColumnWrapper().setDescriptionLanguage(
          clientSelected.getColumnWrapper().getDescriptionLanguage() + " " + metadata.get(
              "client").toString());
      clientSelected.getColumnWrapper().setDropdownValues(dropdownValues);
      clientSelected.setReadOnly(true);
    }

    if (metadata.get("goldenRecord") != null && Boolean.valueOf(metadata.get("goldenRecord").toString())) {
      AtomicInteger i = new AtomicInteger();
      clientsSelectable.stream().sorted(Comparator.comparing(LocalizedFieldValue::getText)).forEach(el -> {

        if (metadata.get("client").toString().equals(el.getText())) {
          rowWrappers.add(i.get(), clientSelected);
        } else {

          rowWrappers.add(i.get(), RowWrapper.builder()
              .readOnly(false)
              .columnWrapper(ColumnWrapper.builder()
                  .attributesColumn(AttributeCodes.CLIENT + el.getText())
                  .descriptionLanguage(el.getText())
                  .descriptionField(false)
                  .technicalAttribute(false)
                  .customerField(false)
                  .dropdownValues(dropdownValues)
                  .build()).build());
        }
        i.getAndIncrement();
        });

      RowWrapper goldenRecordRow = RowWrapper.builder()
          .fieldValue(dropdownValues.get(0).getText())
          .readOnly(true)
          .columnWrapper(ColumnWrapper.builder()
              .attributesColumn("MC_GoldenRecord")
              .descriptionLanguage("Golden Record")
              .descriptionField(false)
              .technicalAttribute(false)
              .customerField(false)
              .dropdownValues(dropdownValues)
              .build())
          .build();
      rowWrappers.add(0, goldenRecordRow);

    } else {
      rowWrappers.add(0, clientSelected);
    }

    RowWrapper noteColumn = RowWrapper.builder()
        .readOnly(false)
        .columnWrapper(ColumnWrapper.builder()
            .attributesColumn("MC_Note")
            .descriptionLanguage("Note")
            .descriptionField(false)
            .technicalAttribute(false)
            .customerField(false)
            .build())
        .build();
    rowWrappers.add(rowWrappers.size(), noteColumn);
  }

  @Override
  public String getRequestType() {
    return REQUEST_TYPE;
  }

  @Override
  public String getFileName() {
    return FILE_NAME + EXCEL_EXTENSION;
  }

  private void addCustomProperties(final SXSSFWorkbook workbook, final String language,
                                   final Map<String, Object> metadata) {
    final POIXMLProperties properties = workbook.getXSSFWorkbook().getProperties();
    final POIXMLProperties.CustomProperties props = properties.getCustomProperties();
    //props.addProperty(getFileName(), JSONUtils.asString(columns));
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_TEMPLATE_TYPE, MassiveOperationTemplateType.CREATION.toString());
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_DOMAIN_TYPE,
                      TemplateType.getTemplateTypeByMdDomain((String) metadata.get("mdDomain"), (boolean) metadata.get("goldenRecord")));
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_CLIENT, metadata.get("client").toString());
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_EXTRACTION_PURPOSE, MasterdataExtractionType.EXPORT_MASSIVE_CREATION.toString());
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_LANGUAGE, language);
    props.addProperty(ExcelCustomPropertyMetadata.EXCEL_CUSTOM_PROPERTY_REQUEST_ID, metadata.get("requestId").toString());
  }
}
