package com.creactives.tam4.materials.plugin.acerinox.services;

import com.creactives.tam4.materials.dataproviders.database.entities.md.MasterDataEntity;
import com.creactives.tam4.materials.dataproviders.database.entities.md.materials.MaterialEntity;
import com.creactives.tam4.materials.dataproviders.database.repositories.MasterDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AcerinoxClearBusinessUnitFieldServiceTest {

  @Mock
  private MasterDataRepository masterDataRepository;

  @InjectMocks
  private AcerinoxClearBusinessUnitFieldService acerinoxClearBusinessUnitFieldService;

  private MasterDataEntity masterDataEntity;

  @BeforeEach
  void setUp() {
    masterDataEntity = new MaterialEntity();
  }

  @Test
  void testExecute_WithBusinessUnitField() {
    final Map<String, String> customerFields = new HashMap<>();
    customerFields.put("Business_Units", "SomeValue");
    masterDataEntity.setCustomerFields(customerFields);

    acerinoxClearBusinessUnitFieldService.execute(masterDataEntity);

    assertFalse(masterDataEntity.getCustomerFields().containsKey("Business_Units"));
    verify(masterDataRepository, times(1)).save(masterDataEntity);
  }

  @Test
  void testExecute_WithoutBusinessUnitField() {
    final Map<String, String> customerFields = new HashMap<>();
    masterDataEntity.setCustomerFields(customerFields);

    acerinoxClearBusinessUnitFieldService.execute(masterDataEntity);

    assertTrue(masterDataEntity.getCustomerFields().isEmpty());
    verify(masterDataRepository, times(1)).save(masterDataEntity);
  }

  @Test
  void testExecute_NullCustomerFields() {
    masterDataEntity.setCustomerFields(null);

    acerinoxClearBusinessUnitFieldService.execute(masterDataEntity);

    assertNull(masterDataEntity.getCustomerFields());
    verify(masterDataRepository, times(1)).save(masterDataEntity);
  }
}
