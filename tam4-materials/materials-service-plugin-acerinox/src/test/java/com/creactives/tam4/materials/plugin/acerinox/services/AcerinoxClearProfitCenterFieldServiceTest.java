package com.creactives.tam4.materials.plugin.acerinox.services;

import com.creactives.tam4.materials.dataproviders.database.entities.PlantSpecificDataEntity;
import com.creactives.tam4.materials.dataproviders.database.entities.md.materials.MaterialEntity;
import com.creactives.tam4.materials.dataproviders.database.repositories.PlantSpecificDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AcerinoxClearProfitCenterFieldServiceTest {

  @Mock
  private PlantSpecificDataRepository plantSpecificDataRepository;

  @InjectMocks
  private AcerinoxClearProfitCenterFieldService acerinoxClearProfitCenterFieldService;

  private MaterialEntity masterDataEntity;
  private PlantSpecificDataEntity plantSpecificDataEntity1;
  private PlantSpecificDataEntity plantSpecificDataEntity2;

  @BeforeEach
  void setUp() {
    masterDataEntity = new MaterialEntity();

    plantSpecificDataEntity1 = new PlantSpecificDataEntity();
    plantSpecificDataEntity1.setProfitCenter("PC1");

    plantSpecificDataEntity2 = new PlantSpecificDataEntity();
    plantSpecificDataEntity2.setProfitCenter("PC2");

    final List<PlantSpecificDataEntity> plantSpecificDataEntities = new ArrayList<>();
    plantSpecificDataEntities.add(plantSpecificDataEntity1);
    plantSpecificDataEntities.add(plantSpecificDataEntity2);

    masterDataEntity.setPlantSpecificDataEntities(plantSpecificDataEntities);
  }

  @Test
  void execute_ShouldClearProfitCenterFieldAndSave_WhenProfitCenterExists() {
    acerinoxClearProfitCenterFieldService.execute(masterDataEntity);

    assertNull(plantSpecificDataEntity1.getProfitCenter());
    assertNull(plantSpecificDataEntity2.getProfitCenter());

    verify(plantSpecificDataRepository, times(1)).saveAll(forClass(List.class).capture());
  }

  @Test
  void execute_ShouldNotCallSave_WhenNoPlantSpecificDataEntities() {
    masterDataEntity.setPlantSpecificDataEntities(new ArrayList<>());

    acerinoxClearProfitCenterFieldService.execute(masterDataEntity);

    verify(plantSpecificDataRepository, times(0)).saveAll(Mockito.anyList());
  }

  @Test
  void execute_ShouldNotModifyProfitCenterField_WhenProfitCenterAlreadyNull() {
    plantSpecificDataEntity1.setProfitCenter(null);
    plantSpecificDataEntity2.setProfitCenter(null);

    acerinoxClearProfitCenterFieldService.execute(masterDataEntity);

    verify(plantSpecificDataRepository, times(1)).saveAll(Mockito.anyList());
  }
}
