package com.creactives.tam4.materials.plugin.acerinox.services;

import com.creactives.tam4.materials.core.usecase.clearfields.IClearBusinessUnitField;
import com.creactives.tam4.materials.dataproviders.database.entities.md.MasterDataEntity;
import com.creactives.tam4.materials.dataproviders.database.repositories.MasterDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("acerinoxClearBusinessUnitFieldService")
@Log4j2
@ConditionalOnProperty(prefix = "creactives.materials.plugin",
    name = "clear-business-unit-field-service-name",
    havingValue = "acerinoxClearBusinessUnitFieldService")
@RequiredArgsConstructor
public class AcerinoxClearBusinessUnitFieldService implements IClearBusinessUnitField {
  private final MasterDataRepository masterDataRepository;
  private final String BUSINESS_UNIT_KEY = "Business_Units";


  @Override
  public void execute(final MasterDataEntity masterDataEntity) {
    masterDataEntity.setCustomerFields(removeBusinessUnitField(masterDataEntity.getCustomerFields())
    );
    masterDataRepository.save(masterDataEntity);
  }

  private Map<String, String> removeBusinessUnitField(final Map<String, String> customerFields) {
    if (customerFields == null) {
      return null;
    }
    customerFields.remove(BUSINESS_UNIT_KEY);
    return customerFields;
  }


}
