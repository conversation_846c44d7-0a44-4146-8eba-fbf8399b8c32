package com.creactives.tam4.materials.plugin.acerinox.services;

import com.creactives.tam4.materials.core.usecase.clearfields.IClearProfitCenterField;
import com.creactives.tam4.materials.dataproviders.database.entities.PlantSpecificDataEntity;
import com.creactives.tam4.materials.dataproviders.database.entities.md.MasterDataEntity;
import com.creactives.tam4.materials.dataproviders.database.repositories.PlantSpecificDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service("acerinoxClearProfitCenterFieldService")
@Log4j2
@ConditionalOnProperty(prefix = "creactives.materials.plugin",
    name = "clear-profit-center-field-service-name",
    havingValue = "acerinoxClearProfitCenterFieldService")
@RequiredArgsConstructor
public class AcerinoxClearProfitCenterFieldService implements IClearProfitCenterField {
  private final PlantSpecificDataRepository plantSpecificDataRepository;

  @Override
  public void execute(final MasterDataEntity masterDataEntity) {
    final List<PlantSpecificDataEntity> plantSpecificDataEntityList = masterDataEntity.getPlantSpecificDataEntities();
    if (!plantSpecificDataEntityList.isEmpty()) {

      final List<PlantSpecificDataEntity> updatedEntityList = plantSpecificDataEntityList.stream().peek(plantSpecificDataEntity -> plantSpecificDataEntity.setProfitCenter(null)
      ).collect(Collectors.toList());

      plantSpecificDataRepository.saveAll(updatedEntityList);
    }
  }


}
