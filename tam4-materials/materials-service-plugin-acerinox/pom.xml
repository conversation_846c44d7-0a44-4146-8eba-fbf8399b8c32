<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>materials-service</artifactId>
		<groupId>com.creactives.tam4</groupId>
		<version>2025.02.1-CREAC-6609-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>materials-service-plugin-acerinox</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.parent.groupId}</groupId>
			<artifactId>materials-service-backend</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
