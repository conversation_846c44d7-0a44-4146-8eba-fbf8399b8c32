const express = require('express');
const app = express();

var helloRouter = require('./routes/hello');
var oauthRouter = require('./routes/oauth');
var legacyRouter = require('./routes/legacy');
var materialsRouter = require('./routes/materials');
var ontologyRouter = require('./routes/ontology');

app.use(express.json());

app.use('/', helloRouter);
app.use('/oauth', oauthRouter);
app.use('/legacy', legacyRouter);
app.use('/materials', materialsRouter);
app.use('/ontology', ontologyRouter);

app.use(function (req, res, next) {
    console.log(`REQUEST DID NOT MATCH | method: ${req.method}, url: ${req.url}, query: ${JSON.stringify(req.query)} body: ${JSON.stringify(req.body)}`);
    next();
});

app.listen(3000);


