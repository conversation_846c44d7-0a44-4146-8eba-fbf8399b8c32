var express = require('express');
var router = express.Router();

router.post('/token', function (req, res) {
    res.json({
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsidGFtNCJdLCJ1c2VyX25hbWUiOiJkYW5pZWxlLmNhbnRlcmkiLCJzY29wZSI6WyJyZWFkIiwid3JpdGUiXSwiZXhwIjoxODg2OTEzODI4LCJhdXRob3JpdGllcyI6WyJVU0VSIl0sImp0aSI6ImY3NTRiZWZhLTBiNGMtNDMwOC04OGJhLTIzYWJjNGIzNTQwNiIsImNsaWVudF9pZCI6InRhbTQifQ.hWOmjxbdJzJgCeKfGkgfcwCwcuSLSieR-Gw3v6WjjLY",
        "token_type": "bearer",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsidGFtNCJdLCJ1c2VyX25hbWUiOiJkYW5pZWxlLmNhbnRlcmkiLCJzY29wZSI6WyJyZWFkIiwid3JpdGUiXSwiYXRpIjoiZjc1NGJlZmEtMGI0Yy00MzA4LTg4YmEtMjNhYmM0YjM1NDA2IiwiZXhwIjoxODg2OTEzODI4LCJhdXRob3JpdGllcyI6WyJVU0VSIl0sImp0aSI6IjllMDJlMGExLTgzMmItNGMxZS1iNzM1LTNhZGY5ZWFiZDYyNiIsImNsaWVudF9pZCI6InRhbTQifQ.xC0Upz2e0AheiZukQfWdOONVCL9S6qUUWw5QnLz-cO8",
        "expires_in": 314999999,
        "scope": "read write",
        "jti": "f754befa-0b4c-4308-88ba-23abc4b35406"
    })
});


module.exports = router;