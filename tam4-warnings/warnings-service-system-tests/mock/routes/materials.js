var express = require('express');
var router = express.Router();

router.post('/api/details', function (req, res, next) {

    if (req.query.materialCode == '100200300') {
        res.json({
            "materialId": "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa",
            "materialKey": {
                "client": "EU",
                "materialCode": "100200300"
            },
            "materialGroup": "1011",
            "materialType": "Z013",
            "descriptions": {
                "shortDescriptions": {
                    "ru": "material aaaa short desc ru",
                    "en": "material aaaa short desc en"
                },
                "purchaseOrderDescriptions": {
                    "en": "material aaaa po desc en"
                },
                "internalNoteDescriptions": {},
                "longDescriptions": {},
                "normalizedShortDescriptions": {},
                "normalizedLongDescriptions": {}
            },
            "originalCharacteristicValues": null,
            "technicalClassification": null,
            "materialGroupClassification": {
                "code": "1011",
                "taxonomy": "Commodities"
            },
            "enrichedMaterialGroupClassification": null,
            "completeness": "FAIR",
            "countries": [
                "BE"
            ],
            "plants": [
                {
                    "code": "BE01",
                    "client": "EU"
                },
                {
                    "code": "BE03",
                    "client": "EU"
                }
            ],
            "attachments": [],
            "image": null
        })
    } else {
        next()
    }
});

router.post('/api/count', function (req, res, next) {
    res.set('Content-type', 'application/json;charset=UTF-8');
    res.send("1");
});

router.post('/api/details-with-specific-plant', function (req, res, next) {
    res.json({
        "materialDetails": {
            "materialId": "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa",
            "countries": ["RU"],
        },
        "plantDetails": [
            {
                "countryCode": "RU"
            }
        ]
    });
});

module.exports = router;
