var express = require('express');
var router = express.Router();

router.post('/api/users/get-account-details', function (req, res) {
    res.json({
        "userId": 2,
        "userName": "User",
        "userMail": "",
        "currentLanguage": "en",
        "login": "user",
        "token": null,
        "duplicateRoles": ["duplicates-custom-role"],
        "requesterRoles": ["EU_RU_RU01", "EU_BE_BE03"],
        "approverRoles": [],
        "enabledServices": ["reporting", "worklist-edit", "events-store", "reporting-materials", "plant-admin", "search-service", "worklist-creation", "duplicates-management", "region-admin", "reporting-workflow", "worklist-approver", "country-admin", "group-management", "warnings-dashboard", "clients-administrator", "worklist-relationship", "reporting-relationship", "smart-creation", "business-units-admin", "worklist-extension", "folder-navigation", "file-export", "country-region-admin", "worklist-duplicates", "account-management", "excel-integration", "role-management", "custom-roles-admin", "smart-creation", "group-management", "duplicates-management", "reporting-materials", "events-store", "warnings-dashboard", "business-units-admin", "worklist-relationship", "worklist-approver", "excel-integration", "country-region-admin", "worklist-edit", "search-service", "role-management", "reporting-workflow", "reporting", "region-admin", "worklist-extension", "country-admin", "clients-administrator", "custom-roles-admin", "file-export", "reporting-relationship", "plant-admin", "worklist-creation", "folder-navigation", "account-management", "worklist-duplicates", "worklist-relationship", "worklist-approver", "worklist-duplicates"]
    })
});

router.post('/user-list', function (req, res) {
    res.json([{id: 2, displayName: "user"}])
});

router.post('/plant-list', function (req, res) {
    res.json([{client: 'EU', plantCode: "BE03", description: "Plant BEO3"}])
});

module.exports = router;