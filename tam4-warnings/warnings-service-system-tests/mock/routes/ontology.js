var express = require('express');
var router = express.Router();

router.get('/api/getCategoryHierarchy', function (req, res, next) {
    if (req.query.code == '1011') {
        res.json([
            {
                "code": "1011",
                "taxonomy": "Commodities"
            },
            {
                "code": "10",
                "taxonomy": "Commodities"
            },
            {
                "code": "Commodities",
                "taxonomy": "Commodities"
            }
        ]);
    } else if (req.query.code == '417995') {
        res.json([
            {
                "code": "417995",
                "taxonomy": "Original Material Group"
            },
            {
                "code": "Original Material Group",
                "taxonomy": "Original Material Group"
            }
        ]);
    } else {
        next()
    }
});

module.exports = router;
