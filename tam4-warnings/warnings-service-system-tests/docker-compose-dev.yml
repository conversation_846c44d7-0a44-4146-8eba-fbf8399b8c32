version: '3'
services:
  apache:
    build: apache
    environment:
      - FE_PORT=4200
      - BE_PORT=9067
    extra_hosts:
      - warnings:************
    ports:
      - "8080:80"
  test-companion:
    build: test-companion
  mock:
    build: mock
    ports:
      - "3000:3000"
    volumes:
      - ./mock/app.js:/express/app.js
      - ./mock/routes:/express/routes
  postgres:
    image: "postgres:11.5"
    environment:
      POSTGRES_DB: tam4_warnings
      POSTGRES_USER: user_tam
      POSTGRES_PASSWORD: user_tam
    ports:
      - "25432:5432"
  rabbit:
    image: "rabbitmq:3.8-management-alpine"
    ports:
      - "45672:5672"
      - "55672:15672"
    volumes:
      - ./rabbit/rabbit.json:/opt/rabbit.json:ro
      - ./rabbit/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
