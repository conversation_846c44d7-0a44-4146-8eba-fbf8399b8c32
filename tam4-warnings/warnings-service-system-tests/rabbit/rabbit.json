{"rabbit_version": "3.8.0", "users": [{"name": "guest", "password_hash": "PPbzSNMPXUkt4mHTXVnUEh3KM2ZCjWW5hW/A4yn7QJVtiLJb", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "guest", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "rabbit@a029b19550fb"}], "policies": [], "queues": [{"name": "warnings-service-queue", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "system-tests-queue", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "warnings-exchange", "vhost": "/", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "data-loader-inbound-exchange", "vhost": "/", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "warnings-exchange", "vhost": "/", "destination": "system-tests-queue", "destination_type": "queue", "routing_key": "#", "arguments": {}}]}