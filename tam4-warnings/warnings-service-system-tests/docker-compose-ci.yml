version: '3'
services:
  apache:
    build: apache
    environment:
      - FE_PORT=9067
      - BE_PORT=9067
  test-companion:
    build: test-companion
  warnings:
    build: warnings
    volumes:
      - ${BASE_DIR}/warnings/application.yml:/data/creactives/tam4/warnings-service/application.yml:ro
      - ${BASE_DIR}/warnings/warnings-service-app.jar:/data/creactives/tam4/warnings-service/warnings-service-app.jar:ro
      - ${BASE_DIR}/warnings/start-warnings-service.sh:/data/creactives/tam4/warnings-service/start-warnings-service.sh
      - ${BASE_DIR}/warnings/static:/data/creactives/tam4/warnings-service/static
    depends_on:
      - postgres
      - rabbit
  mock:
    build: mock
    volumes:
      - ${BASE_DIR}/mock/app.js:/express/app.js
      - ${BASE_DIR}/mock/routes:/express/routes
  postgres:
    image: "postgres:11.5"
    environment:
      POSTGRES_DB: tam4_warnings
      POSTGRES_USER: user_tam
      POSTGRES_PASSWORD: user_tam
  rabbit:
    image: "rabbitmq:3.8-management-alpine"
    volumes:
      - ${BASE_DIR}/rabbit/rabbit.json:/opt/rabbit.json:ro
      - ${BASE_DIR}/rabbit/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
  cypress:
    build:
      context: cypress-tests
    shm_size: '4gb'
    volumes:
      - ${BASE_DIR}/cypress-tests/cypress:/cypress-tests/cypress
    depends_on:
      - warnings
