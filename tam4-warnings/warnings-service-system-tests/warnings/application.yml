server:
  port: 9067
  max-http-header-size: 80KB

warnings:
  rules-configuration:
    rules:
      completeness:
        should-create-warning-if-already-solved-in-the-past: true
      not-analyzed:
        should-create-warning-if-already-solved-in-the-past: true
  entity-types:
    types:
      - type: contracts
      - type: suppliers
      - type: batches
  materials:
    endpoint: http://mock:3000
  ontology:
    endpoint: http://mock:3000

spring:
  datasource:
    url: *********************************************
    username: user_tam
    password: user_tam
    hikari:
      minimum-idle: 2
  rabbitmq:
    port: 5672
    host: rabbit
    username: guest
    password: guest
    listener:
      simple:
        retry:
          max-attempts: 1


logging.file: logs/warnings-service.log
logging:
  file:
    max-history: 10
    max-size: 20MB
  pattern:
    dateformat: yyyy-MM-dd HH:mm:ss.SSSZ

creactives:
  rabbitmq:
    egress:
      enabled: false
    processing:
      enabled: false
  property-base-path: properties
