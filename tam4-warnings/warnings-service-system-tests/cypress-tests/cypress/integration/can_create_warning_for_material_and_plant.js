describe('Warnings tests', function () {

    it('Can create warning for material and plant', function () {
        cy.login();

        cy.cleanupDatabaseAndQueues();

        cy.sendMessageToRabbit('warnings-service-queue',
            'com.creactives.tam4.messaging.warnings.commands.MaterialWarningCreateRequestMessage',
            {
                "client": "EU",
                "materialCode": "100200300",
                "plants": ["RU01"],
                "warningType": "material-group",
                "externalCorrelationId": "AABBCC",
                "created": 1234567890

            }
        );
        cy.wait(2000);
        cy.verifySql('tam4_warnings', 'select * from warnings', [
            {
                "id": "__ignore__",
                "entity_id": null,
                "entity_type": "materials",
                "warning_type": "material-group",
                "qualifier": null,
                "json": "null",
                "descriptions": "null",
                "materials": [
                    "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa"
                ],
                "clients": [
                    "EU"
                ],
                "countries": [
                    "RU"
                ],
                "plants": [
                    "EU/RU01"
                ],
                "categories": [
                    "Commodities/1011",
                    "Commodities/10",
                    "Commodities/Commodities"
                ],
                "process_id": null,
                "created": 1234567890,
                "assigned": null,
                "external_correlation_id": "AABBCC"
            }
        ]);

        cy.verifyRabbitReceivedMessage('system-tests-queue',
            'warnings-exchange',
            'com.creactives.tam4.messaging.warnings.events.MaterialWarningCreatedMessage',
            {
                "uuid": "__ignore__",
                "warningType": "material-group",
                "qualifier": null,
                "material": "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa",
                "descriptions": null,
                "json": null,
                "client": "EU",
                "countries": [
                    "RU"
                ],
                "plants": [
                    {
                        "code": "RU01",
                        "client": "EU"
                    }
                ],
                "categories": [
                    {
                        "code": "1011",
                        "taxonomy": "Commodities"
                    },
                    {
                        "code": "10",
                        "taxonomy": "Commodities"
                    },
                    {
                        "code": "Commodities",
                        "taxonomy": "Commodities"
                    }
                ],
                "created": 1234567890
            }
        );

    });

});

