describe('Warnings tests', function () {

    it('Can create warning for material', function () {
        cy.login();

        cy.cleanupDatabaseAndQueues();

        cy.sendMessageToRabbit('warnings-service-queue',
            'com.creactives.tam4.messaging.materials.events.MaterialSummaryMessage',
            {
                "materialId": "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa",
                "client": "100",
                "materialCode": "12345",
                "completeness": "POOR",
                "countries": [],
                "plants": [{
                    "plantKey": {
                        "client": "100",
                        "code": "BE01"
                    }
                }],
                "categories": {
                    "materialGroupCategorization": [
                        {
                            "code": "Original Material Group",
                            "taxonomy": "Original Material Group"
                        },
                        {
                            "code": "417995",
                            "taxonomy": "Original Material Group"
                        }
                    ],
                    "enrichedMaterialGroupCategorization": null,
                    "technicalCategorization": null
                },
                "manufacturerDetails": {},
                "descriptions": {},
                "semanticallyAnalyzed": "true"

            }
        );
        cy.wait(2000);
        cy.verifySql('tam4_warnings', 'select * from warnings', [
            {
                "id": "__ignore__",
                "entity_id": null,
                "entity_type": "materials",
                "warning_type": "completeness",
                "qualifier": "POOR",
                "json": "{}",
                "descriptions": "{}",
                "materials": [
                    "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa"
                ],
                "clients": [
                    "100"
                ],
                "countries": [
                    null
                ],
                "plants": [
                    "100/BE01"
                ],
                "categories": [],
                "process_id": null,
                "created": "__ignore__",
                "assigned": null,
                "external_correlation_id": null
            }
        ]);

        cy.verifyRabbitReceivedMessage('system-tests-queue',
            'warnings-exchange',
            'com.creactives.tam4.messaging.warnings.events.MaterialWarningCreatedMessage',
            {
                "uuid": "__ignore__",
                "warningType": "completeness",
                "qualifier": "POOR",
                "material": "aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa",
                "descriptions": {},
                "json": {},
                "client": "100",
                "materialCode": "12345",
                "countries": [
                    null
                ],
                "plants": [
                    {
                        "code": "BE01",
                        "client": "100"
                    }
                ],
                "categories": [
                    {
                        "code": "Original Material Group",
                        "taxonomy": "Original Material Group"
                    },
                    {
                        "code": "417995",
                        "taxonomy": "Original Material Group"
                    }
                ],
                "created": "__ignore__"
            }
        );

    });

});

