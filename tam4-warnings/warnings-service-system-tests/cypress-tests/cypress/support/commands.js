Cypress.Commands.add('login', function (args) {
    cy.visit('/');
});

Cypress.Commands.add('logout', function () {
    cy.get('.m-nav__link-icon > .fal').click();
    cy.get('.btn').click()
});

Cypress.Commands.add('verifySql', function (database, sql, result) {
    cy.navigateUsingAngularRouterTo('/app/test-companion/home/<USER>/query');
    cy.setValue('[data-cy="form.database"]', database);
    cy.setValue('[data-cy="form.sql"]', sql);
    let jsonResult = JSON.stringify(result);
    cy.setValue('[data-cy="form.expected"]', jsonResult);
    cy.get('[data-cy="form.submit"]').click({force: true});
    cy.get('[data-cy="result.comparison"]').should($comparison => {
        expect($comparison, `SQL =${sql}`).to.have.text('OK')
    });
    cy.get(':nth-child(2) > pre').invoke('text').then(function (text) {
            let diff = JSON.parse(text);
            cy.log("diff: {} for sql: {}", sql, diff);
            cy.wrap(diff);
        },
    );
});


Cypress.Commands.add('executeSqlIgnoreFail', function (database, sql) {
    cy.request('POST', 'test-companion/api/sql/execute', {
        database: database,
        sql: sql
    });
});

Cypress.Commands.add('executeSql', function (database, sql) {
    cy.request('POST', 'test-companion/api/sql/execute', {
        database: database,
        sql: sql
    }).then((response) => {
        expect(response.status).to.eq(200);
    });
});

Cypress.Commands.add('purgeQueue', function (queue) {
    cy.request('POST', 'test-companion/api/rabbit/purge', {
        queue: queue
    }).then((response) => {
        expect(response.status).to.eq(200);
    });
});

Cypress.Commands.add('verifyRabbitReceivedMessage', function (queue, fromExchange, type, message) {
    cy.navigateUsingAngularRouterTo('/app/test-companion/home/<USER>/receive');
    cy.setValue('[data-cy="form.queue"]', queue);
    cy.setValue('[data-cy="form.expected"]', JSON.stringify(message));
    cy.get('[data-cy="form.submit"]').click({force: true});
    cy.wait(2000);
    cy.get('[data-cy="result.exchange"]').should('have.text', fromExchange);
    cy.get('[data-cy="result.type"]').should('have.text', type);
    cy.get('[data-cy="result.comparison"]').should('have.text', 'OK');
    cy.get(':nth-child(2) > pre').invoke('text').then(function (text) {
            cy.wrap(JSON.parse(text))
        }
    );
});

Cypress.Commands.add('sendMessageToRabbit', function (queue, type, body) {
    cy.request('POST', 'test-companion/api/rabbit/send-message', {
        type: type,
        body: JSON.stringify(body),
        queue: queue
    }).then((response) => {
        expect(response.status).to.eq(200);
    });
});

Cypress.Commands.add('navigateUsingAngularRouterTo', function (url) {
    cy.window().then(win => {
        win.cypressNavigateByUrl(url);
    })
});

Cypress.Commands.add('verifyJsonEquals', function (actual, expected) {
    cy.window().then(win => {
        cy.navigateUsingAngularRouterTo('/app/test-companion/home/<USER>/verify');
        cy.setValue('[data-cy="form.verify.expected"]', JSON.stringify(expected));
        cy.setValue('[data-cy="form.verify.actual"]', JSON.stringify(actual));

        cy.get('[data-cy="form.verify.submit"]').click({force: true});
        cy.get('[data-cy="result.comparison"]').should('have.text', 'OK');
    });
});

Cypress.Commands.add('waitUntilAngularStable', () => {
    cy.window().invoke('getAllAngularRootElements').then(ngRootElements => {
        cy.window().invoke('getAngularTestability', ngRootElements[0]).then(testability =>
            new Cypress.Promise(resolve => {
                testability.whenStable(() => {
                    resolve();
                });
            }),
        );
    });
});
