pipeline {
  agent any
  options {
    disableConcurrentBuilds()
    office365ConnectorWebhooks([[notifyBackToNormal: true, notifyFailure: true, notifyUnstable: true, url: 'https://outlook.office.com/webhook/ac6f652b-9ace-4a9d-bdb8-8b3105a28829@7f0012b6-0c6a-47c9-a536-7daa04e9fb08/IncomingWebhook/4b8806d37f8147a6b955900ad0106d26/2e65b01a-449d-419c-9dac-124ee8be2cf6']])
    buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '1'))
  }

  stages {
    stage('info') {
      steps {
        sh 'printenv'
      }
    }
    stage('build') {
      agent {
        docker {
          image 'docker.creactives.com/tam4/build-box-jdk11'
          args '-v $HOME/.m2:/root/.m2'
          reuseNode true
        }
      }
      steps {
        configFileProvider([configFile(fileId: '9e9a2048-1995-4348-8869-97ee05ba0295', variable: 'MAVEN_SETTINGS')]) {
          sh 'mvn -s $MAVEN_SETTINGS verify'
        }
      }
    }
    stage('prepare-docker') {
      steps {
        sh 'cp warnings-service-app/target/warnings-service-app-*.jar warnings-service-system-tests/warnings/warnings-service-app.jar'
        dir('warnings-service-system-tests') {
          sh 'docker-compose -f docker-compose-ci.yml build --pull'
        }
      }
    }
    stage('execute system tests') {
      steps {
        dir('warnings-service-system-tests') {
          sh 'echo $WORKSPACE | sed \'s/.*\\///\' | sed \'s/.*/BASE_DIR=\\/data\\/creactives\\/jenkins\\/workspace\\/&\\/warnings-service-system-tests/\''
          sh 'echo $WORKSPACE | sed \'s/.*\\///\' | sed \'s/.*/BASE_DIR=\\/data\\/creactives\\/jenkins\\/workspace\\/&\\/warnings-service-system-tests/\' > .env'
          sh "docker-compose -f docker-compose-ci.yml up --abort-on-container-exit --exit-code-from cypress"
        }
      }
    }
  }

  post {
    always {
      dir('warnings-service-system-tests') {
        sh 'docker-compose -f docker-compose-ci.yml down --volumes'
      }
      archiveArtifacts artifacts: 'warnings-service-system-tests/cypress-tests/cypress/screenshots/**/*.png', allowEmptyArchive: true
      junit  '**/test-results/**/*.xml, **/target/surefire-reports/*.xml'
//       sh 'find . -type d -regex \'.*frontend\\/node\\_modules$\' -print0 | xargs -I {} -0 rm -rf \"{}\"'
    }
  }
}
