FROM docker.creactives.com/tam4/openjdk-12:latest

RUN mkdir -p /data/creactives/tam4

RUN mkdir /data/creactives/tam4/warnings-service
COPY warnings-service-app.jar /data/creactives/tam4/warnings-service/warnings-service-app.jar
COPY start-warnings-service.sh /data/creactives/tam4/warnings-service/start-warnings-service.sh

WORKDIR /data/creactives/tam4/warnings-service
CMD ./start-warnings-service.sh
