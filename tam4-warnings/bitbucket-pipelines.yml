image: docker.creactives.com/tam4/build-box-jdk11

definitions:
  services:
    docker:
      memory: 2048

pipelines:
  default:
    - step:
        name: Build and test
        caches:
          - maven
        services:
          - docker
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - mvn package -P sonar-tests-coverage -s bitbucket-mvn-settings.xml
          - mvn sonar:sonar -s bitbucket-mvn-settings.xml || true
  tags:
    rc-*:
      - step:
          name: Build and test
          caches:
            - maven
          services:
            - docker
          script:
            - export TESTCONTAINERS_RYUK_DISABLED=true
            - mvn package -P sonar-tests-coverage -s bitbucket-mvn-settings.xml
            - mvn sonar:sonar -s bitbucket-mvn-settings.xml || true
            - PROJECT_VERSION=$(mvn org.apache.maven.plugins:maven-help-plugin:3.3.0:evaluate -Dexpression="project.version" -B -q -DforceStdout -s bitbucket-mvn-settings.xml)
            - echo "$PROJECT_VERSION"
            - aws configure set aws_access_key_id $aws_access_key_id
            - aws configure set aws_secret_access_key $aws_secret_access_key
            - aws s3 cp warnings-service-app/target/warnings-service-app-"$PROJECT_VERSION".jar s3://prompt-release/artifacts/tam4/warnings/
          artifacts:
            - warnings-service-app/target/warnings-service-app-*.jar
      - step:
          name: Build and publish docker image
          caches:
            - maven
            - docker
          script:
            - PROJECT_VERSION=$(mvn org.apache.maven.plugins:maven-help-plugin:3.3.0:evaluate -Dexpression="project.version" -B -q -DforceStdout -s bitbucket-mvn-settings.xml)
            - cp warnings-service-app/target/warnings-service-app-"$PROJECT_VERSION".jar docker/warnings-service-app.jar
            - docker build docker -t docker.creactives.com/tam4/warnings:"$PROJECT_VERSION"
            - docker push docker.creactives.com/tam4/warnings:"$PROJECT_VERSION"
          services:
            - docker
      - step:
          name: Publish ansible scripts
          caches:
            - maven
          script:
            - PROJECT_VERSION=$(mvn org.apache.maven.plugins:maven-help-plugin:3.3.0:evaluate -Dexpression="project.version" -B -q -DforceStdout -s bitbucket-mvn-settings.xml)
            - sed -i "s/service-version-placeholder/$PROJECT_VERSION/g" ./warnings-service-ansible/vars/main.yml
            - tar -czvf warnings-service-"$PROJECT_VERSION".tar.gz warnings-service-ansible
            - curl -k -u$artifactory_user:$artifactory_password -T warnings-service-"$PROJECT_VERSION".tar.gz "https://artifactory.creactives.com/artifactory/ansible/tam4-services/warnings-service-"$PROJECT_VERSION".tar.gz"
    dev-*:
      - step:
          name: Build artifacts without tests
          size: 2x # Double resources available for this step.
          caches:
            - maven
          services:
            - docker
          script:
            - export TESTCONTAINERS_RYUK_DISABLED=true
            - mvn package -DskipTests -s bitbucket-mvn-settings.xml
            - PROJECT_VERSION=$(mvn org.apache.maven.plugins:maven-help-plugin:3.3.0:evaluate -Dexpression="project.version" -B -q -DforceStdout -s bitbucket-mvn-settings.xml)
            - echo "$PROJECT_VERSION"
            - aws configure set aws_access_key_id $aws_access_key_id
            - aws configure set aws_secret_access_key $aws_secret_access_key
            - aws s3 cp warnings-service-app/target/warnings-service-app-"$PROJECT_VERSION".jar s3://prompt-release/artifacts/tam4/warnings/warnings-service-app-"$PROJECT_VERSION-$BITBUCKET_BUILD_NUMBER".jar
          artifacts:
            - warnings-service-app/target/warnings-service-app-*.jar
      - step:
          name: Build and publish docker image
          caches:
            - maven
            - docker
          script:
            - PROJECT_VERSION=$(mvn org.apache.maven.plugins:maven-help-plugin:3.3.0:evaluate -Dexpression="project.version" -B -q -DforceStdout -s bitbucket-mvn-settings.xml)
            - cp warnings-service-app/target/warnings-service-app-"$PROJECT_VERSION".jar docker/warnings-service-app.jar
            - docker build docker -t docker.creactives.com/tam4/warnings:"$PROJECT_VERSION-$BITBUCKET_BUILD_NUMBER"
            - docker push docker.creactives.com/tam4/warnings:"$PROJECT_VERSION-$BITBUCKET_BUILD_NUMBER"
          services:
            - docker
