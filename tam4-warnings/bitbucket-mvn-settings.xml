<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
        <server>
            <username>${env.artifactory_user}</username>
            <password>${env.artifactory_password}</password>
            <id>central-alternative</id>
        </server>
        <server>
            <username>${env.artifactory_user}</username>
            <password>${env.artifactory_password}</password>
            <id>central-alternative-snapshot</id>
        </server>
    </servers>
    <profiles>
        <profile>
            <repositories>
                <repository>
                    <id>spring-milestones</id>
                    <name>Spring Milestones</name>
                    <url>https://repo.spring.io/milestone</url>
                </repository>
                <repository>
                    <id>central1</id>
                    <name>Central Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>central</id>
                    <name>Central Repository</name>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>central-alternative</id>
                    <name>libs-release</name>
                    <url>https://artifactory.creactives.com/artifactory/libs-release</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>central-alternative-snapshot</id>
                    <snapshots>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                    <name>libs-snapshot</name>
                    <url>https://artifactory.creactives.com/artifactory/libs-snapshot</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                </repository>
            </repositories>
            <id>artifactory</id>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>artifactory</activeProfile>
    </activeProfiles>
</settings>
