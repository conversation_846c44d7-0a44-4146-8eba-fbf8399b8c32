---

- name: Ensure that "{{tam4_path}}/warnings-service" dir is present
  file:
    path: "{{tam4_path}}/warnings-service"
    owner: "{{ tam4_user }}"
    group: "{{ tam4_user }}"
    mode: 0755
    state: directory

- rabbitmq_exchange:
    name: warnings-exchange
    type: fanout

- name: Creating worklists Exchanges, Queues and Bindings
  include_tasks: library/create_rabbitmq_exchange_queue_binding.yml
  vars:
    exchange_name: "{{ item.exchange_name }}"
    queue_name: "{{ item.queue_name }}"
  loop:
    - { exchange_name: 'warnings-inbound-exchange', queue_name: 'warnings-service-queue' }
    - { exchange_name: 'workflow-exchange', queue_name: 'warnings-service-queue' }
    - { exchange_name: 'materials-summaries-exchange', queue_name: 'warnings-service-queue' }

- name: Ensure that data loader database exists
  include_tasks: library/execute_sql.yml
  vars:
    sql: "{{ item.sql }}"
  loop:
    - { sql: 'create database tam4_warnings'}
    - { sql: 'grant all privileges on database tam4_warnings to {{ tam4_db_user }}'}

- name: Copy application.yml
  template:
    src: tam4_warnings/application.yml
    dest: "{{tam4_path}}/warnings-service/application.yml"
    owner: "{{ tam4_user }}"
    group: "{{ tam4_user }}"
    mode: 0644
  notify: restart warnings

- name: Copy start-warnings-service.sh
  template:
    src: tam4_warnings/start-warnings-service.sh
    dest: "{{tam4_path}}/warnings-service/start-warnings-service.sh"
    owner: "{{ tam4_user }}"
    group: "{{ tam4_user }}"
    mode: 0755

- name: Fetch Warnings JAR (and delete old)
  include_tasks: library/fetch_from_aws.yml
  vars:
    remote_object: /artifacts/tam4/warnings/warnings-service-app-{{warnings_version}}.jar
    local_path: "{{tam4_path}}/warnings-service"
    local_file: "warnings-service-app-{{warnings_version}}.jar"
    notification: "restart warnings"
    delete_old: true
    delete_pattern: "warnings-service-app-*.jar"

- name: Ensure that the warnings service exists
  template:
    src: tam4_warnings/tam4-warnings.service
    dest: /lib/systemd/system/tam4-warnings.service
    owner: "root"
    group: "root"
    mode: 0644

- name: Ensure that the warnings service is started
  service:
    name: tam4-warnings
    enabled: yes
    state: started
