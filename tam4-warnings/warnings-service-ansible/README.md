Warnings
=========

Installation role for **Warnings service**.

Requirements
------------

The role uses the EC2 module, so `boto` will be required for this to work.

Role Variables
--------------

No variables defined at the moment

Playbook variables required
---------------------------

Warnings service requires the following variables to be defined as playbook/group variable:
* `tam4_path`: base path
* `tam4_user`: the user that runs/owns the services
* `tam4_db_user`: the DB user to use for the connection
* `database_host`: the DB server to use in the connection
* `spring.datasource.username`: the encoded DB user
* `spring.datasource.password`: the encoded DB password
* `spring.rabbitmq.host`: the RabbitMQ host to use
* `spring.rabbitmq.username`: the encoded RabbitMQ user
* `spring.rabbitmq.password`: the encoded RabbitMQ password

_Note_: the encoded variables will be used in Spring and should be in a form like this: `ENC(abcdefghij...)`

#### Additional variables
Additionally, Warnings service requires the following settings:

`warnings.entity_types`: the list of entity types for warnings

Example:
```yaml
warnings:
  entity_types:
    - contracts
    - suppliers
    - ...
```

Dependencies
------------

No dependencies.

Example Playbook
----------------

    - hosts: servers
      roles:
         - { role: <warnings-version>, become: yes }

Where `warnings-version` is the version for the workflow install scripts installed with ansible-galaxy. Use `ansible-galaxy list` to see the name of your installed roles (and their version).
Note: only the name will be used here.

License
-------

Proprietary

Author Information
------------------

Creactives S.p.A.
