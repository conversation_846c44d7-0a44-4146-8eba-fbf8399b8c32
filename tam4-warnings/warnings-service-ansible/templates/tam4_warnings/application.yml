server:
  port: 9067

warnings:
  entity-types:
    types:
{% for type in warnings.entity_types %}
      - {{ type }}
{% endfor %}
  materials:
    endpoint: {{ endpoints.materials }}:9090
  ontology:
    endpoint: {{ endpoints.ontology }}:9066

spring:
  datasource:
    url: jdbc:postgresql://{{ spring.datasource.host }}:5432/tam4_warnings
    username: {{ spring.datasource.username }}
    password: {{ spring.datasource.password }}
    hikari:
      minimum-idle: 2
  rabbitmq:
    port: 5672
    host: {{ spring.rabbitmq.host }}
    username: {{ spring.rabbitmq.username }}
    password: {{ spring.rabbitmq.password }}

logging.file: {{ tam4_path }}/warnings-service/logs/warnings-service.log
logging:
  file:
    max-history: 10
    max-size: 20MB
  pattern:
    dateformat: yyyy-MM-dd HH:mm:ss.SSSZ
