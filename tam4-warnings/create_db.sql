create database tam4_warnings;
--create user user_tam; --comment this line if the user is already present
--alter user user_tam WITH password 'user_tam';
grant all privileges on database tam4_warnings to user_tam;
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE OR R<PERSON>LACE FUNCTION array_compare_as_set(anyarray,anyarray) RETURNS boolean AS $$
SELECT CASE
  WHEN array_dims($1) <> array_dims($2) THEN
    'f'
  WHEN array_length($1,1) <> array_length($2,1) THEN
    'f'
  ELSE
    NOT EXISTS (SELECT 1
        FROM unnest($1) a
        FULL JOIN unnest($2) b ON (a=b)
        WHERE a IS NULL or b IS NULL
    )
  END
$$ LANGUAGE 'sql' IMMUTABLE;
