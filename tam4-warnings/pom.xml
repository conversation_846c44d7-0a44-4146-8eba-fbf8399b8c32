<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>warnings-service</artifactId>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.creactives.tam4</groupId>
        <artifactId>tam4-mono-parent</artifactId>
        <version>2025.02.1-CREAC-6609-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <description>test</description>

    <modules>
        <module>warnings-service-app</module>
        <module>warnings-service-backend</module>
        <module>warnings-service-local</module>
    </modules>

    <!--    <properties>-->
    <!--        <spring-boot.version>2.1.18.RELEASE</spring-boot.version>-->
    <!--        <gson.version>2.10.1</gson.version>-->
    <!--    </properties>-->

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <!-- from spring-boot-starter-parent -->
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>some test to exclude here</exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>buildonly</id>
            <properties>
                <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
            </properties>
        </profile>
    </profiles>
</project>
