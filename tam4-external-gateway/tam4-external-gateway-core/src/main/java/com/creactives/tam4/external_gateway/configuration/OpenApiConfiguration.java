package com.creactives.tam4.external_gateway.configuration;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfiguration {

  @Bean
  public GroupedOpenApi publicApi() {
    return GroupedOpenApi.builder()
        .group("Creactives Standard API - TAM External API Gateway")
        .pathsToMatch("/**")
        .addOpenApiMethodFilter(method -> method.isAnnotationPresent(Operation.class))
        .build();
  }

  @Bean
  public OpenAPI customOpenAPI() {
    return new OpenAPI()
        .info(new Info()
            .title("Creactives Standard API - TAM External API Gateway")
            .description("This document provides the full API documentation for integrating any system with TAM4, via the standard TAM API. It is the reference for configuring and/or developing clients needing to use TAM as a service. Usually, the integrated systems are ERPs.")
            .contact(new Contact()
                .name("API Support")
                .url("mailto:<EMAIL>")
                .email("<EMAIL>")
            )
            .version("2.0.0")
            .license(new io.swagger.v3.oas.models.info.License().name("Apache 2.0")
                .url("http://www.apache.org/licenses/LICENSE-2.0")));
  }
}
