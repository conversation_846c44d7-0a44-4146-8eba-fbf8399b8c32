package com.creactives.tam4.external_gateway.virtual_assistant.models;/*
 * Created on 09/10/2017 09.13
 * Project - prompt
 * Copyright (c) Creactives SPA - All rights reserved
 * <AUTHOR>
 */


import lombok.Data;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

@Data
@Component
@Scope(value = "session", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class Preferences implements Serializable {

  private static final long serialVersionUID = 8530836080985182077L;

  private Locale locale;
  private boolean isLogged;
  private boolean hasLoggedInWithUsernameAndPassword;
  private String apiToken;
  private String apiUsage;
  private String interactionUuid;
  private String sessionUuid;
  private List<String> languages;


  public String getLocaleAsString() {
    if (locale != null) {
      final String s = locale.toLanguageTag();
      return s.replace('-', '_');
    } else {
      return "en_US";
    }

  }

}
